<?php
/**
 * الوظائف المساعدة
 * Helper Functions
 */

/**
 * إرجاع استجابة JSON
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * إرجاع استجابة خطأ
 */
function errorResponse($message, $statusCode = 400) {
    jsonResponse(['error' => $message, 'success' => false], $statusCode);
}

/**
 * إرجاع استجابة نجاح
 */
function successResponse($data = [], $message = 'تم بنجاح') {
    $response = ['success' => true, 'message' => $message];
    if (!empty($data)) {
        $response['data'] = $data;
    }
    jsonResponse($response);
}

/**
 * تنظيف البيانات المدخلة
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من قوة كلمة المرور
 */
function validatePassword($password) {
    return strlen($password) >= 6;
}

/**
 * إنشاء توكن عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * تحويل التاريخ إلى تنسيق عربي
 */
function formatArabicDate($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "{$day} {$month} {$year}";
}

/**
 * رفع ملف
 */
function uploadFile($file, $directory, $allowedTypes = null) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }

    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }

    // التحقق من نوع الملف
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if ($allowedTypes && !in_array($fileExtension, $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
    }

    // إنشاء اسم ملف فريد
    $fileName = time() . '_' . uniqid() . '.' . $fileExtension;
    $uploadPath = UPLOAD_PATH . $directory . '/';
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }

    $fullPath = $uploadPath . $fileName;

    // رفع الملف
    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        return [
            'success' => true,
            'filename' => $fileName,
            'path' => $fullPath,
            'url' => APP_URL . '/' . $fullPath
        ];
    }

    return ['success' => false, 'message' => 'فشل في رفع الملف'];
}

/**
 * حذف ملف
 */
function deleteFile($filePath) {
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return false;
}

/**
 * التحقق من صحة البيانات
 */
function validateRequired($data, $requiredFields) {
    $errors = [];
    
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            $errors[] = "الحقل {$field} مطلوب";
        }
    }
    
    return $errors;
}

/**
 * تشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * إرسال إشعار Firebase
 */
function sendFirebaseNotification($tokens, $title, $body, $data = []) {
    $serverKey = FIREBASE_SERVER_KEY;
    
    if (empty($serverKey)) {
        return false;
    }

    $notification = [
        'title' => $title,
        'body' => $body,
        'sound' => 'default'
    ];

    $fields = [
        'registration_ids' => is_array($tokens) ? $tokens : [$tokens],
        'notification' => $notification,
        'data' => $data
    ];

    $headers = [
        'Authorization: key=' . $serverKey,
        'Content-Type: application/json'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));

    $result = curl_exec($ch);
    curl_close($ch);

    return json_decode($result, true);
}

/**
 * تسجيل الأخطاء
 */
function logError($message, $file = 'error.log') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents("logs/{$file}", $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * إنشاء مجلد إذا لم يكن موجوداً
 */
function createDirectoryIfNotExists($path) {
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
}

/**
 * تحويل الحجم إلى تنسيق قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * التحقق من نوع الملف
 */
function getFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    if (in_array($extension, ALLOWED_IMAGE_TYPES)) {
        return 'image';
    } elseif (in_array($extension, ALLOWED_VIDEO_TYPES)) {
        return 'video';
    }
    
    return 'unknown';
}

/**
 * تنظيف اسم الملف
 */
function sanitizeFilename($filename) {
    // إزالة الأحرف الخاصة والمسافات
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    return $filename;
}

/**
 * إنشاء رمز تحقق عشوائي
 */
function generateVerificationCode($length = 5) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}
