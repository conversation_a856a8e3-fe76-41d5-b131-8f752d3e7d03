(window.webpackJsonp=window.webpackJsonp||[]).push([[34,32,33],{nH37:function(t,e,i){"use strict";i.d(e,"a",(function(){return V}));var n=i("XuX8"),r=i.n(n),o=i("xjcK"),c=i("pyNs"),s=i("kGy3"),a=i("ex6f"),u=i("qMhD"),l=i("OljW"),h=i("2C+6"),d=i("z3V6"),b=i("3ec0"),f=i("BtlJ"),p=i("rUdO"),m=i("1SAT"),O=i("QPyd"),v=i("Hx7N"),j=i("kO/s"),g=i("YC3Q"),y=i("vJrl"),w=i("mS7b"),k=i("PCFI");function P(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var C="__bv__visibility_observer",D=function(){function t(e,i,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.el=e,this.callback=i.callback,this.margin=i.margin||0,this.once=i.once||!1,this.observer=null,this.visible=void 0,this.doneOnce=!1,this.createObserver(n)}var e,i,n;return e=t,(i=[{key:"createObserver",value:function(t){var e=this;if(this.observer&&this.stop(),!this.doneOnce&&Object(a.f)(this.callback)){try{this.observer=new IntersectionObserver(this.handler.bind(this),{root:null,rootMargin:this.margin,threshold:0})}catch(t){return this.doneOnce=!0,this.observer=void 0,void this.callback(null)}t.context.$nextTick((function(){Object(s.B)((function(){e.observer&&e.observer.observe(e.el)}))}))}}},{key:"handler",value:function(t){var e=t?t[0]:{},i=Boolean(e.isIntersecting||e.intersectionRatio>0);i!==this.visible&&(this.visible=i,this.callback(i),this.once&&this.visible&&(this.doneOnce=!0,this.stop()))}},{key:"stop",value:function(){this.observer&&this.observer.disconnect(),this.observer=null}}])&&P(e.prototype,i),n&&P(e,n),t}(),x=function(t){var e=t[C];e&&e.stop&&e.stop(),delete t[C]},F=function(t,e,i){var n=e.value,r=e.modifiers,o={margin:"0px",once:!1,callback:n};Object(h.h)(r).forEach((function(t){w.d.test(t)?o.margin="".concat(t,"px"):"once"===t.toLowerCase()&&(o.once=!0)})),x(t),t[C]=new D(t,o,i),t[C]._prevModifiers=Object(h.b)(r)},G={bind:F,componentUpdated:function(t,e,i){var n=e.value,r=e.oldValue,o=e.modifiers;o=Object(h.b)(o),!t||n===r&&t[C]&&Object(k.a)(o,t[C]._prevModifiers)||F(t,{value:n,modifiers:o},i)},unbind:function(t){x(t)}};function A(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function $(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?A(Object(i),!0).forEach((function(e){S(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):A(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function S(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var R=Object(d.d)(Object(h.m)($($($($($($({},j.b),b.b),p.b),m.b),O.b),{},{maxRows:Object(d.c)(c.o),noAutoShrink:Object(d.c)(c.g,!1),noResize:Object(d.c)(c.g,!1),rows:Object(d.c)(c.o,2),wrap:Object(d.c)(c.t,"soft")})),o.H),V=r.a.extend({name:o.H,directives:{"b-visible":G},mixins:[y.a,j.a,g.a,b.a,p.a,m.a,O.a,f.a,v.a],props:R,data:function(){return{heightInPx:null}},computed:{computedStyle:function(){var t={resize:!this.computedRows||this.noResize?"none":null};return this.computedRows||(t.height=this.heightInPx,t.overflowY="scroll"),t},computedMinRows:function(){return Object(u.c)(Object(l.b)(this.rows,2),2)},computedMaxRows:function(){return Object(u.c)(this.computedMinRows,Object(l.b)(this.maxRows,0))},computedRows:function(){return this.computedMinRows===this.computedMaxRows?this.computedMinRows:null},computedAttrs:function(){var t=this.disabled,e=this.required;return{id:this.safeId(),name:this.name||null,form:this.form||null,disabled:t,placeholder:this.placeholder||null,required:e,autocomplete:this.autocomplete||null,readonly:this.readonly||this.plaintext,rows:this.computedRows,wrap:this.wrap||null,"aria-required":this.required?"true":null,"aria-invalid":this.computedAriaInvalid}},computedListeners:function(){return $($({},this.bvListeners),{},{input:this.onInput,change:this.onChange,blur:this.onBlur})}},watch:{localValue:function(){this.setHeight()}},mounted:function(){this.setHeight()},methods:{visibleCallback:function(t){t&&this.$nextTick(this.setHeight)},setHeight:function(){var t=this;this.$nextTick((function(){Object(s.B)((function(){t.heightInPx=t.computeHeight()}))}))},computeHeight:function(){if(this.$isServer||!Object(a.g)(this.computedRows))return null;var t=this.$el;if(!Object(s.u)(t))return null;var e=Object(s.k)(t),i=Object(l.a)(e.lineHeight,1),n=Object(l.a)(e.borderTopWidth,0)+Object(l.a)(e.borderBottomWidth,0),r=Object(l.a)(e.paddingTop,0)+Object(l.a)(e.paddingBottom,0),o=n+r,c=i*this.computedMinRows+o,h=Object(s.m)(t,"height")||e.height;Object(s.F)(t,"height","auto");var d=t.scrollHeight;Object(s.F)(t,"height",h);var b=Object(u.c)((d-r)/i,2),f=Object(u.d)(Object(u.c)(b,this.computedMinRows),this.computedMaxRows),p=Object(u.c)(Object(u.a)(f*i+o),c);return this.noAutoShrink&&Object(l.a)(h,0)>p?h:"".concat(p,"px")}},render:function(t){return t("textarea",{class:this.computedClass,style:this.computedStyle,directives:[{name:"b-visible",value:this.visibleCallback,modifiers:{640:!0}}],attrs:this.computedAttrs,domProps:{value:this.localValue},on:this.computedListeners,ref:"input"})}})},"oVt+":function(t,e,i){"use strict";i.d(e,"a",(function(){return g}));var n=i("tC49"),r=i("xjcK"),o=i("pyNs"),c=i("Iyau"),s=i("Io6r"),a=i("bAY6"),u=i("tQiw"),l=i("2C+6"),h=i("z3V6"),d=i("+nMp");function b(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function f(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?b(Object(i),!0).forEach((function(e){p(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):b(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function p(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var m=["start","end","center"],O=Object(u.a)((function(t,e){return(e=Object(d.h)(Object(d.g)(e)))?Object(d.c)(["row-cols",t,e].filter(a.a).join("-")):null})),v=Object(u.a)((function(t){return Object(d.c)(t.replace("cols",""))})),j=[],g={name:r.gb,functional:!0,get props(){var t;return delete this.props,this.props=(t=Object(s.b)().reduce((function(t,e){return t[Object(h.g)(e,"cols")]=Object(h.c)(o.o),t}),Object(l.c)(null)),j=Object(l.h)(t),Object(h.d)(Object(l.m)(f(f({},t),{},{alignContent:Object(h.c)(o.t,null,(function(t){return Object(c.a)(Object(c.b)(m,"between","around","stretch"),t)})),alignH:Object(h.c)(o.t,null,(function(t){return Object(c.a)(Object(c.b)(m,"between","around"),t)})),alignV:Object(h.c)(o.t,null,(function(t){return Object(c.a)(Object(c.b)(m,"baseline","stretch"),t)})),noGutters:Object(h.c)(o.g,!1),tag:Object(h.c)(o.t,"div")})),r.gb)),this.props},render:function(t,e){var i,r=e.props,o=e.data,c=e.children,s=r.alignV,a=r.alignH,u=r.alignContent,l=[];return j.forEach((function(t){var e=O(v(t),r[t]);e&&l.push(e)})),l.push((p(i={"no-gutters":r.noGutters},"align-items-".concat(s),s),p(i,"justify-content-".concat(a),a),p(i,"align-content-".concat(u),u),i)),t(r.tag,Object(n.a)(o,{staticClass:"row",class:l}),c)}}},"w+YJ":function(t,e,i){"use strict";i.d(e,"a",(function(){return N}));var n,r,o=i("XuX8"),c=i.n(o),s=i("xjcK"),a=i("AFYn"),u=i("pyNs"),l=i("ex6f"),h=i("PCFI"),d=function(t,e){for(var i=0;i<t.length;i++)if(Object(h.a)(t[i],e))return i;return-1},b=i("2C+6"),f=i("z3V6"),p=i("kGy3"),m=i("WPLV"),O=i("STsD"),v=i("3ec0"),j=i("qVMd"),g=i("rUdO"),y=i("1SAT"),w=i("kO/s"),k=i("jBgq");function P(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function C(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?P(Object(i),!0).forEach((function(e){D(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):P(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function D(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var x,F=Object(m.a)("checked",{defaultValue:null}),G=F.mixin,A=F.props,$=F.prop,S=F.event,R=Object(f.d)(Object(b.m)(C(C(C(C(C(C(C({},w.b),A),v.b),g.b),y.b),j.b),{},{ariaLabel:Object(f.c)(u.t),ariaLabelledby:Object(f.c)(u.t),button:Object(f.c)(u.g,!1),buttonVariant:Object(f.c)(u.t),inline:Object(f.c)(u.g,!1),value:Object(f.c)(u.a)})),"formRadioCheckControls"),V=c.a.extend({mixins:[O.a,w.a,G,k.a,v.a,g.a,y.a,j.a],inheritAttrs:!1,props:R,data:function(){return{localChecked:this.isGroup?this.bvGroup[$]:this[$],hasFocus:!1}},computed:{computedLocalChecked:{get:function(){return this.isGroup?this.bvGroup.localChecked:this.localChecked},set:function(t){this.isGroup?this.bvGroup.localChecked=t:this.localChecked=t}},isChecked:function(){return Object(h.a)(this.value,this.computedLocalChecked)},isRadio:function(){return!0},isGroup:function(){return!!this.bvGroup},isBtnMode:function(){return this.isGroup?this.bvGroup.buttons:this.button},isPlain:function(){return!this.isBtnMode&&(this.isGroup?this.bvGroup.plain:this.plain)},isCustom:function(){return!this.isBtnMode&&!this.isPlain},isSwitch:function(){return!(this.isBtnMode||this.isRadio||this.isPlain)&&(this.isGroup?this.bvGroup.switches:this.switch)},isInline:function(){return this.isGroup?this.bvGroup.inline:this.inline},isDisabled:function(){return this.isGroup&&this.bvGroup.disabled||this.disabled},isRequired:function(){return this.computedName&&(this.isGroup?this.bvGroup.required:this.required)},computedName:function(){return(this.isGroup?this.bvGroup.groupName:this.name)||null},computedForm:function(){return(this.isGroup?this.bvGroup.form:this.form)||null},computedSize:function(){return(this.isGroup?this.bvGroup.size:this.size)||""},computedState:function(){return this.isGroup?this.bvGroup.computedState:Object(l.b)(this.state)?this.state:null},computedButtonVariant:function(){var t=this.buttonVariant;return t||(this.isGroup&&this.bvGroup.buttonVariant?this.bvGroup.buttonVariant:"secondary")},buttonClasses:function(){var t,e=this.computedSize;return["btn","btn-".concat(this.computedButtonVariant),(t={},D(t,"btn-".concat(e),e),D(t,"disabled",this.isDisabled),D(t,"active",this.isChecked),D(t,"focus",this.hasFocus),t)]},computedAttrs:function(){var t=this.isDisabled,e=this.isRequired;return C(C({},this.bvAttrs),{},{id:this.safeId(),type:this.isRadio?"radio":"checkbox",name:this.computedName,form:this.computedForm,disabled:t,required:e,"aria-required":e||null,"aria-label":this.ariaLabel||null,"aria-labelledby":this.ariaLabelledby||null})}},watch:(n={},D(n,$,(function(){this["".concat($,"Watcher")].apply(this,arguments)})),D(n,"computedLocalChecked",(function(){this.computedLocalCheckedWatcher.apply(this,arguments)})),n),methods:(r={},D(r,"".concat($,"Watcher"),(function(t){Object(h.a)(t,this.computedLocalChecked)||(this.computedLocalChecked=t)})),D(r,"computedLocalCheckedWatcher",(function(t,e){Object(h.a)(t,e)||this.$emit(S,t)})),D(r,"handleChange",(function(t){var e=this,i=t.target.checked,n=this.value,r=i?n:null;this.computedLocalChecked=n,this.$nextTick((function(){e.$emit(a.d,r),e.isGroup&&e.bvGroup.$emit(a.d,r)}))})),D(r,"handleFocus",(function(t){t.target&&("focus"===t.type?this.hasFocus=!0:"blur"===t.type&&(this.hasFocus=!1))})),D(r,"focus",(function(){this.isDisabled||Object(p.d)(this.$refs.input)})),D(r,"blur",(function(){this.isDisabled||Object(p.c)(this.$refs.input)})),r),render:function(t){var e=this.isRadio,i=this.isBtnMode,n=this.isPlain,r=this.isCustom,o=this.isInline,c=this.isSwitch,s=this.computedSize,a=this.bvAttrs,u=this.normalizeSlot(),l=t("input",{class:[{"form-check-input":n,"custom-control-input":r,"position-static":n&&!u},i?"":this.stateClass],directives:[{name:"model",value:this.computedLocalChecked}],attrs:this.computedAttrs,domProps:{value:this.value,checked:this.isChecked},on:C({change:this.handleChange},i?{focus:this.handleFocus,blur:this.handleFocus}:{}),key:"input",ref:"input"});if(i){var h=t("label",{class:this.buttonClasses},[l,u]);return this.isGroup||(h=t("div",{class:["btn-group-toggle","d-inline-block"]},[h])),h}var d=t();return n&&!u||(d=t("label",{class:{"form-check-label":n,"custom-control-label":r},attrs:{for:this.safeId()}},u)),t("div",{class:[D({"form-check":n,"form-check-inline":n&&o,"custom-control":r,"custom-control-inline":r&&o,"custom-checkbox":r&&!e&&!c,"custom-switch":c,"custom-radio":r&&e},"b-custom-control-".concat(s),s&&!i),a.class],style:a.style},[l,d])}});function E(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function I(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?E(Object(i),!0).forEach((function(e){T(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):E(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function T(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var L=a.W+"indeterminate",M=Object(f.d)(Object(b.m)(I(I({},R),{},(T(x={},"indeterminate",Object(f.c)(u.g,!1)),T(x,"switch",Object(f.c)(u.g,!1)),T(x,"uncheckedValue",Object(f.c)(u.a,!1)),T(x,"value",Object(f.c)(u.a,!0)),x))),s.w),N=c.a.extend({name:s.w,mixins:[V],inject:{bvGroup:{from:"bvCheckGroup",default:null}},props:M,computed:{isChecked:function(){var t=this.value,e=this.computedLocalChecked;return Object(l.a)(e)?d(e,t)>-1:Object(h.a)(e,t)},isRadio:function(){return!1}},watch:T({},"indeterminate",(function(t,e){Object(h.a)(t,e)||this.setIndeterminate(t)})),mounted:function(){this.setIndeterminate(this.indeterminate)},methods:{computedLocalCheckedWatcher:function(t,e){if(!Object(h.a)(t,e)){this.$emit(S,t);var i=this.$refs.input;i&&this.$emit(L,i.indeterminate)}},handleChange:function(t){var e=this,i=t.target,n=i.checked,r=i.indeterminate,o=this.value,c=this.uncheckedValue,s=this.computedLocalChecked;if(Object(l.a)(s)){var u=d(s,o);n&&u<0?s=s.concat(o):!n&&u>-1&&(s=s.slice(0,u).concat(s.slice(u+1)))}else s=n?o:c;this.computedLocalChecked=s,this.$nextTick((function(){e.$emit(a.d,s),e.isGroup&&e.bvGroup.$emit(a.d,s),e.$emit(L,r)}))},setIndeterminate:function(t){Object(l.a)(this.computedLocalChecked)&&(t=!1);var e=this.$refs.input;e&&(e.indeterminate=t,this.$emit(L,t))}}})},"xD+F":function(t,e,i){"use strict";i.d(e,"a",(function(){return _}));var n,r=i("XuX8"),o=i.n(r),c=i("xjcK"),s=i("6GPe"),a=i("AFYn"),u=i("pyNs"),l=i("m3aq"),h=i("mS7b"),d=i("yoge"),b=i("Iyau"),f=i("yanh"),p=i("kGy3"),m=i("a3f1"),O=i("bAY6"),v=i("ex6f"),j=i("PCFI"),g=i("WPLV"),y=i("2C+6"),w=i("z3V6"),k=i("+nMp"),P=i("aGvM"),C=i("STsD"),D=i("3ec0"),x=i("qVMd"),F=i("1SAT"),G=i("kO/s"),A=i("jBgq"),$=i("rUdO");function S(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function R(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?S(Object(i),!0).forEach((function(e){V(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):S(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function V(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var E=Object(g.a)("value",{type:[u.b,d.b],defaultValue:null,validator:function(t){return""===t?(Object(P.a)(N,c.x),!0):Object(v.p)(t)||q(t)}}),I=E.mixin,T=E.props,L=E.prop,M=E.event,N='Setting "value"/"v-model" to an empty string for reset is deprecated. Set to "null" instead.',q=function t(e){return Object(v.e)(e)||Object(v.a)(e)&&e.every((function(e){return t(e)}))},z=function(t){return Object(v.f)(t.getAsEntry)?t.getAsEntry():Object(v.f)(t.webkitGetAsEntry)?t.webkitGetAsEntry():null},B=function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return new Promise((function(n){var r=[];!function o(){e.readEntries((function(e){0===e.length?n(Promise.all(r).then((function(t){return Object(b.d)(t)}))):(r.push(Promise.all(e.map((function(e){if(e){if(e.isDirectory)return t(e.createReader(),"".concat(i).concat(e.name,"/"));if(e.isFile)return new Promise((function(t){e.file((function(e){e.$path="".concat(i).concat(e.name),t(e)}))}))}return null})).filter(O.a))),o())}))}()}))},H=Object(w.d)(Object(y.m)(R(R(R(R(R(R(R({},G.b),T),D.b),x.b),F.b),$.b),{},{accept:Object(w.c)(u.t,""),browseText:Object(w.c)(u.t,"Browse"),capture:Object(w.c)(u.g,!1),directory:Object(w.c)(u.g,!1),dropPlaceholder:Object(w.c)(u.t,"Drop files here"),fileNameFormatter:Object(w.c)(u.k),multiple:Object(w.c)(u.g,!1),noDrop:Object(w.c)(u.g,!1),noDropPlaceholder:Object(w.c)(u.t,"Not allowed"),noTraverse:Object(w.c)(u.g,!1),placeholder:Object(w.c)(u.t,"No file chosen")})),c.x),_=o.a.extend({name:c.x,mixins:[C.a,G.a,I,A.a,D.a,F.a,x.a,A.a],inheritAttrs:!1,props:H,data:function(){return{files:[],dragging:!1,dropAllowed:!this.noDrop,hasFocus:!1}},computed:{computedAccept:function(){var t=this.accept;return 0===(t=(t||"").trim().split(/[,\s]+/).filter(O.a)).length?null:t.map((function(t){var e="name",i="^",n="$";return h.g.test(t)?i="":(e="type",h.t.test(t)&&(n=".+$",t=t.slice(0,-1))),t=Object(k.a)(t),{rx:new RegExp("".concat(i).concat(t).concat(n)),prop:e}}))},computedCapture:function(){var t=this.capture;return!0===t||""===t||(t||null)},computedAttrs:function(){var t=this.name,e=this.disabled,i=this.required,n=this.form,r=this.computedCapture,o=this.accept,c=this.multiple,s=this.directory;return R(R({},this.bvAttrs),{},{type:"file",id:this.safeId(),name:t,disabled:e,required:i,form:n||null,capture:r,accept:o||null,multiple:c,directory:s,webkitdirectory:s,"aria-required":i?"true":null})},computedFileNameFormatter:function(){var t=this.fileNameFormatter;return Object(w.b)(t)?t:this.defaultFileNameFormatter},clonedFiles:function(){return Object(f.a)(this.files)},flattenedFiles:function(){return Object(b.e)(this.files)},fileNames:function(){return this.flattenedFiles.map((function(t){return t.name}))},labelContent:function(){if(this.dragging&&!this.noDrop)return this.normalizeSlot(l.j,{allowed:this.dropAllowed})||(this.dropAllowed?this.dropPlaceholder:this.$createElement("span",{staticClass:"text-danger"},this.noDropPlaceholder));if(0===this.files.length)return this.normalizeSlot(l.G)||this.placeholder;var t=this.flattenedFiles,e=this.clonedFiles,i=this.fileNames,n=this.computedFileNameFormatter;return this.hasNormalizedSlot(l.n)?this.normalizeSlot(l.n,{files:t,filesTraversed:e,names:i}):n(t,e,i)}},watch:(n={},V(n,L,(function(t){(!t||Object(v.a)(t)&&0===t.length)&&this.reset()})),V(n,"files",(function(t,e){if(!Object(j.a)(t,e)){var i=this.multiple,n=this.noTraverse,r=!i||n?Object(b.e)(t):t;this.$emit(M,i?r:r[0]||null)}})),n),created:function(){this.$_form=null},mounted:function(){var t=Object(p.e)("form",this.$el);t&&(Object(m.b)(t,"reset",this.reset,a.T),this.$_form=t)},beforeDestroy:function(){var t=this.$_form;t&&Object(m.a)(t,"reset",this.reset,a.T)},methods:{isFileValid:function(t){if(!t)return!1;var e=this.computedAccept;return!e||e.some((function(e){return e.rx.test(t[e.prop])}))},isFilesArrayValid:function(t){var e=this;return Object(v.a)(t)?t.every((function(t){return e.isFileValid(t)})):this.isFileValid(t)},defaultFileNameFormatter:function(t,e,i){return i.join(", ")},setFiles:function(t){this.dropAllowed=!this.noDrop,this.dragging=!1,this.files=this.multiple?this.directory?t:Object(b.e)(t):Object(b.e)(t).slice(0,1)},setInputFiles:function(t){try{var e=new ClipboardEvent("").clipboardData||new DataTransfer;Object(b.e)(Object(f.a)(t)).forEach((function(t){delete t.$path,e.items.add(t)})),this.$refs.input.files=e.files}catch(t){}},reset:function(){try{var t=this.$refs.input;t.value="",t.type="",t.type="file"}catch(t){}this.files=[]},handleFiles:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e){var i=t.filter(this.isFilesArrayValid);i.length>0&&(this.setFiles(i),this.setInputFiles(i))}else this.setFiles(t)},focusHandler:function(t){this.plain||"focusout"===t.type?this.hasFocus=!1:this.hasFocus=!0},onChange:function(t){var e=this,i=t.type,n=t.target,r=t.dataTransfer,o=void 0===r?{}:r,c="drop"===i;this.$emit(a.d,t);var u=Object(b.f)(o.items||[]);if(s.d&&u.length>0&&!Object(v.g)(z(u[0])))(function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Promise.all(Object(b.f)(t).filter((function(t){return"file"===t.kind})).map((function(t){var i=z(t);if(i){if(i.isDirectory&&e)return B(i.createReader(),"".concat(i.name,"/"));if(i.isFile)return new Promise((function(t){i.file((function(e){e.$path="",t(e)}))}))}return null})).filter(O.a))})(u,this.directory).then((function(t){return e.handleFiles(t,c)}));else{var l=Object(b.f)(n.files||o.files||[]).map((function(t){return t.$path=t.webkitRelativePath||"",t}));this.handleFiles(l,c)}},onDragenter:function(t){Object(m.f)(t),this.dragging=!0;var e=t.dataTransfer,i=void 0===e?{}:e;if(this.noDrop||this.disabled||!this.dropAllowed)return i.dropEffect="none",void(this.dropAllowed=!1);i.dropEffect="copy"},onDragover:function(t){Object(m.f)(t),this.dragging=!0;var e=t.dataTransfer,i=void 0===e?{}:e;if(this.noDrop||this.disabled||!this.dropAllowed)return i.dropEffect="none",void(this.dropAllowed=!1);i.dropEffect="copy"},onDragleave:function(t){var e=this;Object(m.f)(t),this.$nextTick((function(){e.dragging=!1,e.dropAllowed=!e.noDrop}))},onDrop:function(t){var e=this;Object(m.f)(t),this.dragging=!1,this.noDrop||this.disabled||!this.dropAllowed?this.$nextTick((function(){e.dropAllowed=!e.noDrop})):this.onChange(t)}},render:function(t){var e=this.custom,i=this.plain,n=this.size,r=this.dragging,o=this.stateClass,c=this.bvAttrs,s=t("input",{class:[{"form-control-file":i,"custom-file-input":e,focus:e&&this.hasFocus},o],style:e?{zIndex:-5}:{},attrs:this.computedAttrs,on:{change:this.onChange,focusin:this.focusHandler,focusout:this.focusHandler,reset:this.reset},ref:"input"});if(i)return s;var a=t("label",{staticClass:"custom-file-label",class:{dragging:r},attrs:{for:this.safeId(),"data-browse":this.browseText||null}},[t("span",{staticClass:"d-block form-file-text",style:{pointerEvents:"none"}},[this.labelContent])]);return t("div",{staticClass:"custom-file b-form-file",class:[V({},"b-custom-control-".concat(n),n),o,c.class],style:c.style,attrs:{id:this.safeId("_BV_file_outer_")},on:{dragenter:this.onDragenter,dragover:this.onDragover,dragleave:this.onDragleave,drop:this.onDrop}},[s,a])}})}}]);