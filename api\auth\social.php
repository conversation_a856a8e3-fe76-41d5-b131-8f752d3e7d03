<?php
/**
 * تسجيل الدخول عبر وسائل التواصل الاجتماعي
 * Social Login API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

// الحصول على نوع الخدمة من URL
$service = $_GET['service'] ?? '';
if (!in_array($service, ['facebook', 'google'])) {
    errorResponse('خدمة غير مدعومة');
}

$data = getRequestData();

// التحقق من البيانات المطلوبة
$requiredFields = ['name', 'email', 'token'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$name = $data['name'];
$email = $data['email'];
$socialToken = $data['token'];
$country = $data['country'] ?? null;

// التحقق من صحة البريد الإلكتروني
if (!validateEmail($email)) {
    errorResponse('البريد الإلكتروني غير صحيح');
}

// البحث عن المستخدم الموجود
$tokenField = $service === 'facebook' ? 'facebook_token' : 'google_token';
$userSql = "SELECT * FROM users WHERE email = ? OR {$tokenField} = ?";
$user = $db->selectOne($userSql, [$email, $socialToken]);

if ($user) {
    // تحديث التوكن إذا كان المستخدم موجود
    $updateSql = "UPDATE users SET {$tokenField} = ? WHERE id = ?";
    $db->update($updateSql, [$socialToken, $user['id']]);
    
    // تسجيل الدخول
    session_start();
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_token'] = $user['token'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['login_time'] = time();
    
    // الحصول على بيانات الاشتراك
    $subscriptionSql = "SELECT * FROM subscriptions WHERE user_id = ? AND active = 1 AND expire_date >= CURDATE()";
    $subscription = $db->selectOne($subscriptionSql, [$user['id']]);
    
    if ($subscription) {
        $user['subscription'] = $subscription;
    }
    
    successResponse($user, 'تم تسجيل الدخول بنجاح');
} else {
    // إنشاء مستخدم جديد
    $token = md5(time() . $email);
    $password = password_hash(uniqid(), PASSWORD_DEFAULT); // كلمة مرور عشوائية
    
    $insertData = [
        'name' => $name,
        'email' => $email,
        'password' => $password,
        'country' => $country,
        'token' => $token,
        'role' => 'user',
        'date' => date('Y-m-d'),
        'ban' => 0
    ];
    
    // إضافة التوكن الاجتماعي
    $insertData[$tokenField] = $socialToken;
    
    $fields = implode(', ', array_keys($insertData));
    $placeholders = ':' . implode(', :', array_keys($insertData));
    
    $insertSql = "INSERT INTO users ({$fields}) VALUES ({$placeholders})";
    $userId = $db->insert($insertSql, $insertData);
    
    if ($userId) {
        // تسجيل الدخول للمستخدم الجديد
        session_start();
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_token'] = $token;
        $_SESSION['user_role'] = 'user';
        $_SESSION['user_name'] = $name;
        $_SESSION['login_time'] = time();
        
        $newUser = $insertData;
        $newUser['id'] = $userId;
        unset($newUser['password']);
        
        successResponse($newUser, 'تم إنشاء الحساب وتسجيل الدخول بنجاح');
    } else {
        errorResponse('حدث خطأ أثناء إنشاء الحساب');
    }
}
