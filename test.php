<?php
/**
 * ملف اختبار للتحقق من المشاكل
 */

echo "<h1>اختبار النظام</h1>";

// 1. اختبار PHP
echo "<h2>1. اختبار PHP</h2>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

// 2. اختبار الملفات
echo "<h2>2. اختبار الملفات</h2>";
$files = [
    'config/app_config.php',
    'includes/init.php',
    'includes/Database.php',
    'includes/Auth.php',
    'includes/functions.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ {$file} موجود<br>";
    } else {
        echo "❌ {$file} غير موجود<br>";
    }
}

// 2.5 اختبار تحميل الملفات واحد تلو الآخر
echo "<h2>2.5 اختبار تحميل الملفات</h2>";

try {
    echo "تحميل config/app_config.php...<br>";
    require_once 'config/app_config.php';
    echo "✅ تم تحميل app_config.php<br>";

    echo "تحميل includes/functions.php...<br>";
    require_once 'includes/functions.php';
    echo "✅ تم تحميل functions.php<br>";

    echo "تحميل includes/Database.php...<br>";
    require_once 'includes/Database.php';
    echo "✅ تم تحميل Database.php<br>";

    echo "تحميل includes/Auth.php...<br>";
    require_once 'includes/Auth.php';
    echo "✅ تم تحميل Auth.php<br>";

} catch (Exception $e) {
    echo "❌ خطأ في تحميل الملفات: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ خطأ فادح: " . $e->getMessage() . "<br>";
}

// 3. اختبار قاعدة البيانات
echo "<h2>3. اختبار قاعدة البيانات</h2>";

try {
    $host = 'localhost';
    $dbname = 'rainapp4_matar';
    $username = 'root';
    $password = '';

    $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    echo "✅ اتصال قاعدة البيانات نجح<br>";

    // اختبار جدول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "عدد الجداول: " . count($tables) . "<br>";
    echo "الجداول: " . implode(', ', array_slice($tables, 0, 5)) . "...<br>";

} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// 4. اختبار المجلدات
echo "<h2>4. اختبار المجلدات</h2>";
$dirs = ['uploads', 'logs', 'admin', 'api', 'config', 'includes'];

foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? "قابل للكتابة" : "غير قابل للكتابة";
        echo "✅ {$dir}/ موجود ({$writable})<br>";
    } else {
        echo "❌ {$dir}/ غير موجود<br>";
    }
}

// 5. اختبار إعدادات PHP
echo "<h2>5. إعدادات PHP المهمة</h2>";
echo "display_errors: " . ini_get('display_errors') . "<br>";
echo "error_reporting: " . error_reporting() . "<br>";
echo "max_execution_time: " . ini_get('max_execution_time') . "<br>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";

echo "<h2>✅ انتهى الاختبار</h2>";
echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
?>
