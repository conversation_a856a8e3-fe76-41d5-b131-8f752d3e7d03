<?php
/**
 * الحصول على الكوبونات
 * Get Coupons API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// الحصول على الكوبونات النشطة فقط
$sql = "SELECT * FROM coupons 
        WHERE active = 1 
        AND (expire_date IS NULL OR expire_date >= CURDATE())
        ORDER BY id DESC";

$coupons = $db->select($sql);

// تنسيق البيانات
foreach ($coupons as &$coupon) {
    // تنسيق تاريخ الانتهاء
    if ($coupon['expire_date']) {
        $coupon['formatted_expire_date'] = formatArabicDate($coupon['expire_date']);
        
        // حساب الأيام المتبقية
        $expireTimestamp = strtotime($coupon['expire_date']);
        $currentTimestamp = time();
        $daysLeft = ceil(($expireTimestamp - $currentTimestamp) / (60 * 60 * 24));
        $coupon['days_left'] = max(0, $daysLeft);
    }
    
    // التحقق من صحة الكوبون
    $coupon['is_valid'] = true;
    if ($coupon['expire_date'] && strtotime($coupon['expire_date']) < time()) {
        $coupon['is_valid'] = false;
    }
}

successResponse($coupons);
