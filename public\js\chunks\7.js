/*! For license information please see 7.js.LICENSE.txt */
(window.webpackJsonp=window.webpackJsonp||[]).push([[7],{"9hfn":function(e,t,n){"use strict";(function(e){function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,a=!1,o=void 0;try{for(var i,s=e[Symbol.iterator]();!(r=(i=s.next()).done)&&(n.push(i.value),!t||n.length!==t);r=!0);}catch(e){a=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(a)throw o}}return n}(e,t)||s(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||s(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){if(e){if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,"a",(function(){return Ut}));var u="asc",c="desc",d="none",h="records",f=[10,20,30,40,50],g="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{};var p=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){var n="[object Arguments]",r="[object Map]",a="[object Object]",o="[object Set]",i=/^\[object .+?Constructor\]$/,s=/^(?:0|[1-9]\d*)$/,l={};l["[object Float32Array]"]=l["[object Float64Array]"]=l["[object Int8Array]"]=l["[object Int16Array]"]=l["[object Int32Array]"]=l["[object Uint8Array]"]=l["[object Uint8ClampedArray]"]=l["[object Uint16Array]"]=l["[object Uint32Array]"]=!0,l[n]=l["[object Array]"]=l["[object ArrayBuffer]"]=l["[object Boolean]"]=l["[object DataView]"]=l["[object Date]"]=l["[object Error]"]=l["[object Function]"]=l[r]=l["[object Number]"]=l[a]=l["[object RegExp]"]=l[o]=l["[object String]"]=l["[object WeakMap]"]=!1;var u="object"==typeof g&&g&&g.Object===Object&&g,c="object"==typeof self&&self&&self.Object===Object&&self,d=u||c||Function("return this")(),h=t&&!t.nodeType&&t,f=h&&e&&!e.nodeType&&e,p=f&&f.exports===h,m=p&&u.process,v=function(){try{return m&&m.binding&&m.binding("util")}catch(e){}}(),w=v&&v.isTypedArray;function b(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function y(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function _(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}var P,C,x,T=Array.prototype,S=Function.prototype,k=Object.prototype,O=d["__core-js_shared__"],R=S.toString,D=k.hasOwnProperty,E=(P=/[^.]+$/.exec(O&&O.keys&&O.keys.IE_PROTO||""))?"Symbol(src)_1."+P:"",F=k.toString,M=RegExp("^"+R.call(D).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),I=p?d.Buffer:void 0,U=d.Symbol,j=d.Uint8Array,N=k.propertyIsEnumerable,A=T.splice,$=U?U.toStringTag:void 0,L=Object.getOwnPropertySymbols,B=I?I.isBuffer:void 0,H=(C=Object.keys,x=Object,function(e){return C(x(e))}),q=ve(d,"DataView"),Y=ve(d,"Map"),z=ve(d,"Promise"),W=ve(d,"Set"),Q=ve(d,"WeakMap"),G=ve(Object,"create"),X=_e(q),K=_e(Y),J=_e(z),V=_e(W),Z=_e(Q),ee=U?U.prototype:void 0,te=ee?ee.valueOf:void 0;function ne(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function re(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ae(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function oe(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new ae;++t<n;)this.add(e[t])}function ie(e){var t=this.__data__=new re(e);this.size=t.size}function se(e,t){var n=xe(e),r=!n&&Ce(e),a=!n&&!r&&Te(e),o=!n&&!r&&!a&&De(e),i=n||r||a||o,s=i?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],l=s.length;for(var u in e)!t&&!D.call(e,u)||i&&("length"==u||a&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||ye(u,l))||s.push(u);return s}function le(e,t){for(var n=e.length;n--;)if(Pe(e[n][0],t))return n;return-1}function ue(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":$&&$ in Object(e)?function(e){var t=D.call(e,$),n=e[$];try{e[$]=void 0;var r=!0}catch(e){}var a=F.call(e);r&&(t?e[$]=n:delete e[$]);return a}(e):function(e){return F.call(e)}(e)}function ce(e){return Re(e)&&ue(e)==n}function de(e,t,i,s,l){return e===t||(null==e||null==t||!Re(e)&&!Re(t)?e!=e&&t!=t:function(e,t,i,s,l,u){var c=xe(e),d=xe(t),h=c?"[object Array]":be(e),f=d?"[object Array]":be(t),g=(h=h==n?a:h)==a,p=(f=f==n?a:f)==a,m=h==f;if(m&&Te(e)){if(!Te(t))return!1;c=!0,g=!1}if(m&&!g)return u||(u=new ie),c||De(e)?ge(e,t,i,s,l,u):function(e,t,n,a,i,s,l){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!s(new j(e),new j(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Pe(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case r:var u=y;case o:var c=1&a;if(u||(u=_),e.size!=t.size&&!c)return!1;var d=l.get(e);if(d)return d==t;a|=2,l.set(e,t);var h=ge(u(e),u(t),a,i,s,l);return l.delete(e),h;case"[object Symbol]":if(te)return te.call(e)==te.call(t)}return!1}(e,t,h,i,s,l,u);if(!(1&i)){var v=g&&D.call(e,"__wrapped__"),w=p&&D.call(t,"__wrapped__");if(v||w){var b=v?e.value():e,P=w?t.value():t;return u||(u=new ie),l(b,P,i,s,u)}}if(!m)return!1;return u||(u=new ie),function(e,t,n,r,a,o){var i=1&n,s=pe(e),l=s.length,u=pe(t).length;if(l!=u&&!i)return!1;var c=l;for(;c--;){var d=s[c];if(!(i?d in t:D.call(t,d)))return!1}var h=o.get(e);if(h&&o.get(t))return h==t;var f=!0;o.set(e,t),o.set(t,e);var g=i;for(;++c<l;){d=s[c];var p=e[d],m=t[d];if(r)var v=i?r(m,p,d,t,e,o):r(p,m,d,e,t,o);if(!(void 0===v?p===m||a(p,m,n,r,o):v)){f=!1;break}g||(g="constructor"==d)}if(f&&!g){var w=e.constructor,b=t.constructor;w==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof w&&w instanceof w&&"function"==typeof b&&b instanceof b||(f=!1)}return o.delete(e),o.delete(t),f}(e,t,i,s,l,u)}(e,t,i,s,de,l))}function he(e){return!(!Oe(e)||function(e){return!!E&&E in e}(e))&&(Se(e)?M:i).test(_e(e))}function fe(e){if(n=(t=e)&&t.constructor,r="function"==typeof n&&n.prototype||k,t!==r)return H(e);var t,n,r,a=[];for(var o in Object(e))D.call(e,o)&&"constructor"!=o&&a.push(o);return a}function ge(e,t,n,r,a,o){var i=1&n,s=e.length,l=t.length;if(s!=l&&!(i&&l>s))return!1;var u=o.get(e);if(u&&o.get(t))return u==t;var c=-1,d=!0,h=2&n?new oe:void 0;for(o.set(e,t),o.set(t,e);++c<s;){var f=e[c],g=t[c];if(r)var p=i?r(g,f,c,t,e,o):r(f,g,c,e,t,o);if(void 0!==p){if(p)continue;d=!1;break}if(h){if(!b(t,(function(e,t){if(i=t,!h.has(i)&&(f===e||a(f,e,n,r,o)))return h.push(t);var i}))){d=!1;break}}else if(f!==g&&!a(f,g,n,r,o)){d=!1;break}}return o.delete(e),o.delete(t),d}function pe(e){return function(e,t,n){var r=t(e);return xe(e)?r:function(e,t){for(var n=-1,r=t.length,a=e.length;++n<r;)e[a+n]=t[n];return e}(r,n(e))}(e,Ee,we)}function me(e,t){var n,r,a=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?a["string"==typeof t?"string":"hash"]:a.map}function ve(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return he(n)?n:void 0}ne.prototype.clear=function(){this.__data__=G?G(null):{},this.size=0},ne.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ne.prototype.get=function(e){var t=this.__data__;if(G){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return D.call(t,e)?t[e]:void 0},ne.prototype.has=function(e){var t=this.__data__;return G?void 0!==t[e]:D.call(t,e)},ne.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=G&&void 0===t?"__lodash_hash_undefined__":t,this},re.prototype.clear=function(){this.__data__=[],this.size=0},re.prototype.delete=function(e){var t=this.__data__,n=le(t,e);return!(n<0)&&(n==t.length-1?t.pop():A.call(t,n,1),--this.size,!0)},re.prototype.get=function(e){var t=this.__data__,n=le(t,e);return n<0?void 0:t[n][1]},re.prototype.has=function(e){return le(this.__data__,e)>-1},re.prototype.set=function(e,t){var n=this.__data__,r=le(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},ae.prototype.clear=function(){this.size=0,this.__data__={hash:new ne,map:new(Y||re),string:new ne}},ae.prototype.delete=function(e){var t=me(this,e).delete(e);return this.size-=t?1:0,t},ae.prototype.get=function(e){return me(this,e).get(e)},ae.prototype.has=function(e){return me(this,e).has(e)},ae.prototype.set=function(e,t){var n=me(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},oe.prototype.add=oe.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},oe.prototype.has=function(e){return this.__data__.has(e)},ie.prototype.clear=function(){this.__data__=new re,this.size=0},ie.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},ie.prototype.get=function(e){return this.__data__.get(e)},ie.prototype.has=function(e){return this.__data__.has(e)},ie.prototype.set=function(e,t){var n=this.__data__;if(n instanceof re){var r=n.__data__;if(!Y||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new ae(r)}return n.set(e,t),this.size=n.size,this};var we=L?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var n=-1,r=null==e?0:e.length,a=0,o=[];++n<r;){var i=e[n];t(i,n,e)&&(o[a++]=i)}return o}(L(e),(function(t){return N.call(e,t)})))}:function(){return[]},be=ue;function ye(e,t){return!!(t=null==t?9007199254740991:t)&&("number"==typeof e||s.test(e))&&e>-1&&e%1==0&&e<t}function _e(e){if(null!=e){try{return R.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Pe(e,t){return e===t||e!=e&&t!=t}(q&&"[object DataView]"!=be(new q(new ArrayBuffer(1)))||Y&&be(new Y)!=r||z&&"[object Promise]"!=be(z.resolve())||W&&be(new W)!=o||Q&&"[object WeakMap]"!=be(new Q))&&(be=function(e){var t=ue(e),n=t==a?e.constructor:void 0,i=n?_e(n):"";if(i)switch(i){case X:return"[object DataView]";case K:return r;case J:return"[object Promise]";case V:return o;case Z:return"[object WeakMap]"}return t});var Ce=ce(function(){return arguments}())?ce:function(e){return Re(e)&&D.call(e,"callee")&&!N.call(e,"callee")},xe=Array.isArray;var Te=B||function(){return!1};function Se(e){if(!Oe(e))return!1;var t=ue(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function ke(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function Oe(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Re(e){return null!=e&&"object"==typeof e}var De=w?function(e){return function(t){return e(t)}}(w):function(e){return Re(e)&&ke(e.length)&&!!l[ue(e)]};function Ee(e){return null!=(t=e)&&ke(t.length)&&!Se(t)?se(e):fe(e);var t}e.exports=function(e,t){return de(e,t)}})),m={a:["a","à","á","â","ã","ä","å","æ","ā","ă","ą","ǎ","ǟ","ǡ","ǻ","ȁ","ȃ","ȧ","ɐ","ɑ","ɒ","ͣ","а","ӑ","ӓ","ᵃ","ᵄ","ᶏ","ḁ","ẚ","ạ","ả","ấ","ầ","ẩ","ẫ","ậ","ắ","ằ","ẳ","ẵ","ặ","ₐ","ⱥ","ａ"],b:["b","ƀ","ƃ","ɓ","ᖯ","ᵇ","ᵬ","ᶀ","ḃ","ḅ","ḇ","ｂ"],c:["c","ç","ć","ĉ","ċ","č","ƈ","ȼ","ɕ","ͨ","ᴄ","ᶜ","ḉ","ↄ","ｃ"],d:["d","ď","đ","Ƌ","ƌ","ȡ","ɖ","ɗ","ͩ","ᵈ","ᵭ","ᶁ","ᶑ","ḋ","ḍ","ḏ","ḑ","ḓ","ｄ"],e:["e","è","é","ê","ë","ē","ĕ","ė","ę","ě","ǝ","ȅ","ȇ","ȩ","ɇ","ɘ","ͤ","ᵉ","ᶒ","ḕ","ḗ","ḙ","ḛ","ḝ","ẹ","ẻ","ẽ","ế","ề","ể","ễ","ệ","ₑ","ｅ"],f:["f","ƒ","ᵮ","ᶂ","ᶠ","ḟ","ｆ"],g:["g","ĝ","ğ","ġ","ģ","ǥ","ǧ","ǵ","ɠ","ɡ","ᵍ","ᵷ","ᵹ","ᶃ","ᶢ","ḡ","ｇ"],h:["h","ĥ","ħ","ƕ","ȟ","ɥ","ɦ","ʮ","ʯ","ʰ","ʱ","ͪ","Һ","һ","ᑋ","ᶣ","ḣ","ḥ","ḧ","ḩ","ḫ","ⱨ","ｈ"],i:["i","ì","í","î","ï","ĩ","ī","ĭ","į","ǐ","ȉ","ȋ","ɨ","ͥ","ᴉ","ᵎ","ᵢ","ᶖ","ᶤ","ḭ","ḯ","ỉ","ị","ｉ"],j:["j","ĵ","ǰ","ɉ","ʝ","ʲ","ᶡ","ᶨ","ｊ"],k:["k","ķ","ƙ","ǩ","ʞ","ᵏ","ᶄ","ḱ","ḳ","ḵ","ⱪ","ｋ"],l:["l","ĺ","ļ","ľ","ŀ","ł","ƚ","ȴ","ɫ","ɬ","ɭ","ˡ","ᶅ","ᶩ","ᶪ","ḷ","ḹ","ḻ","ḽ","ℓ","ⱡ"],m:["m","ɯ","ɰ","ɱ","ͫ","ᴟ","ᵐ","ᵚ","ᵯ","ᶆ","ᶬ","ᶭ","ḿ","ṁ","ṃ","㎡","㎥","ｍ"],n:["n","ñ","ń","ņ","ň","ŉ","ƞ","ǹ","ȵ","ɲ","ɳ","ᵰ","ᶇ","ᶮ","ᶯ","ṅ","ṇ","ṉ","ṋ","ⁿ","ｎ"],o:["o","ò","ó","ô","õ","ö","ø","ō","ŏ","ő","ơ","ǒ","ǫ","ǭ","ǿ","ȍ","ȏ","ȫ","ȭ","ȯ","ȱ","ɵ","ͦ","о","ӧ","ө","ᴏ","ᴑ","ᴓ","ᴼ","ᵒ","ᶱ","ṍ","ṏ","ṑ","ṓ","ọ","ỏ","ố","ồ","ổ","ỗ","ộ","ớ","ờ","ở","ỡ","ợ","ₒ","ｏ","𐐬"],p:["p","ᵖ","ᵱ","ᵽ","ᶈ","ṕ","ṗ","ｐ"],q:["q","ɋ","ʠ","ᛩ","ｑ"],r:["r","ŕ","ŗ","ř","ȑ","ȓ","ɍ","ɹ","ɻ","ʳ","ʴ","ʵ","ͬ","ᵣ","ᵲ","ᶉ","ṙ","ṛ","ṝ","ṟ"],s:["s","ś","ŝ","ş","š","ș","ʂ","ᔆ","ᶊ","ṡ","ṣ","ṥ","ṧ","ṩ","ｓ"],t:["t","ţ","ť","ŧ","ƫ","ƭ","ț","ʇ","ͭ","ᵀ","ᵗ","ᵵ","ᶵ","ṫ","ṭ","ṯ","ṱ","ẗ","ｔ"],u:["u","ù","ú","û","ü","ũ","ū","ŭ","ů","ű","ų","ư","ǔ","ǖ","ǘ","ǚ","ǜ","ȕ","ȗ","ͧ","ߎ","ᵘ","ᵤ","ṳ","ṵ","ṷ","ṹ","ṻ","ụ","ủ","ứ","ừ","ử","ữ","ự","ｕ"],v:["v","ʋ","ͮ","ᵛ","ᵥ","ᶹ","ṽ","ṿ","ⱱ","ｖ","ⱴ"],w:["w","ŵ","ʷ","ᵂ","ẁ","ẃ","ẅ","ẇ","ẉ","ẘ","ⱳ","ｗ"],x:["x","̽","͓","ᶍ","ͯ","ẋ","ẍ","ₓ","ｘ"],y:["y","ý","ÿ","ŷ","ȳ","ɏ","ʸ","ẏ","ỳ","ỵ","ỷ","ỹ","ｙ"],z:["z","ź","ż","ž","ƶ","ȥ","ɀ","ʐ","ʑ","ᙆ","ᙇ","ᶻ","ᶼ","ᶽ","ẑ","ẓ","ẕ","ⱬ","ｚ"]},v=function(){var e={};for(var t in m){var n=m[t];for(var r in n){var a=n[r];a!==t&&(e[a]=t)}}return e}(),w=/[^a-z0-9\s,.-]/,b=function(e){if(-1===e.search(w))return e;for(var t="",n=e.length,r=0;r<n;r++){var a=e.charAt(r);t+=a in v?v[a]:a}return t},y=function(e){return e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")},_={format:function(e){return e},filterPredicate:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(null==e)return!1;var a=n?String(e).toLowerCase():b(y(String(e)).toLowerCase()),o=n?t.toLowerCase():b(y(t).toLowerCase());return r?a===o:a.indexOf(o)>-1},compare:function(e,t){function n(e){return null==e?"":b(String(e).toLowerCase())}return(e=n(e))<(t=n(t))?-1:e>t?1:0}};function P(e,t,n,r,a,o,i,s,l,u){"boolean"!=typeof i&&(l=s,s=i,i=!1);const c="function"==typeof n?n.options:n;let d;if(e&&e.render&&(c.render=e.render,c.staticRenderFns=e.staticRenderFns,c._compiled=!0,a&&(c.functional=!0)),r&&(c._scopeId=r),o?(d=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=d):t&&(d=i?function(e){t.call(this,u(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,s(e))}),d)if(c.functional){const e=c.render;c.render=function(t,n){return d.call(n),e(t,n)}}else{const e=c.beforeCreate;c.beforeCreate=e?[].concat(e,d):[d]}return n}var C=P({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"vgt-wrap__footer vgt-clearfix"},[e.perPageDropdownEnabled?n("div",{staticClass:"footer__row-count vgt-pull-left"},[n("form",[n("label",{staticClass:"footer__row-count__label",attrs:{for:e.id}},[e._v(e._s(e.rowsPerPageText)+":")]),e._v(" "),n("select",{directives:[{name:"model",rawName:"v-model",value:e.currentPerPage,expression:"currentPerPage"}],staticClass:"footer__row-count__select",attrs:{id:e.id,autocomplete:"off",name:"perPageSelect","aria-controls":"vgt-table"},on:{change:[function(t){var n=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){return"_value"in e?e._value:e.value}));e.currentPerPage=t.target.multiple?n:n[0]},e.perPageChanged]}},[e._l(e.rowsPerPageOptions,(function(t,r){return n("option",{key:r,domProps:{value:t}},[e._v("\n          "+e._s(t)+"\n        ")])})),e._v(" "),e.paginateDropdownAllowAll?n("option",{domProps:{value:-1}},[e._v(e._s(e.allText))]):e._e()],2)])]):e._e(),e._v(" "),n("div",{staticClass:"footer__navigation vgt-pull-right"},[n("pagination-page-info",{attrs:{"total-records":e.total,"last-page":e.pagesCount,"current-page":e.currentPage,"current-per-page":e.currentPerPage,"of-text":e.ofText,"page-text":e.pageText,"info-fn":e.infoFn,mode:e.mode},on:{"page-changed":e.changePage}}),e._v(" "),e.jumpFirstOrLast?n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.firstIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.firstPage(t)}}},[n("span",{staticClass:"chevron",class:{left:!e.rtl,right:e.rtl},attrs:{"aria-hidden":"true"}}),e._v(" "),n("span",[e._v(e._s(e.firstText))])]):e._e(),e._v(" "),n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.prevIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.previousPage(t)}}},[n("span",{staticClass:"chevron",class:{left:!e.rtl,right:e.rtl},attrs:{"aria-hidden":"true"}}),e._v(" "),n("span",[e._v(e._s(e.prevText))])]),e._v(" "),n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.nextIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.nextPage(t)}}},[n("span",[e._v(e._s(e.nextText))]),e._v(" "),n("span",{staticClass:"chevron",class:{right:!e.rtl,left:e.rtl},attrs:{"aria-hidden":"true"}})]),e._v(" "),e.jumpFirstOrLast?n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.lastIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.lastPage(t)}}},[n("span",[e._v(e._s(e.lastText))]),e._v(" "),n("span",{staticClass:"chevron",class:{right:!e.rtl,left:e.rtl},attrs:{"aria-hidden":"true"}})]):e._e()],1)])},staticRenderFns:[]},void 0,{name:"VgtPagination",props:{styleClass:{default:"table table-bordered"},total:{default:null},perPage:{},rtl:{default:!1},perPageDropdownEnabled:{default:!0},customRowsPerPageDropdown:{default:function(){return[]}},paginateDropdownAllowAll:{default:!0},mode:{default:h},jumpFirstOrLast:{default:!1},firstText:{default:"First"},lastText:{default:"Last"},nextText:{default:"Next"},prevText:{default:"Prev"},rowsPerPageText:{default:"Rows per page:"},ofText:{default:"of"},pageText:{default:"page"},allText:{default:"All"},infoFn:{default:null}},data:function(){return{id:this.getId(),currentPage:1,prevPage:0,currentPerPage:10,rowsPerPageOptions:[]}},watch:{perPage:{handler:function(e,t){this.handlePerPage(),this.perPageChanged(t)},immediate:!0},customRowsPerPageDropdown:function(){this.handlePerPage()},total:{handler:function(e,t){-1===this.rowsPerPageOptions.indexOf(this.currentPerPage)&&(this.currentPerPage=e)}}},computed:{pagesCount:function(){if(-1===this.currentPerPage)return 1;var e=Math.floor(this.total/this.currentPerPage);return 0===this.total%this.currentPerPage?e:e+1},firstIsPossible:function(){return this.currentPage>1},lastIsPossible:function(){return this.currentPage<Math.ceil(this.total/this.currentPerPage)},nextIsPossible:function(){return this.currentPage<this.pagesCount},prevIsPossible:function(){return this.currentPage>1}},methods:{getId:function(){return"vgt-select-rpp-".concat(Math.floor(Math.random()*Date.now()))},changePage:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e>0&&this.total>this.currentPerPage*(e-1)&&(this.prevPage=this.currentPage,this.currentPage=e,this.pageChanged(t))},firstPage:function(){this.firstIsPossible&&(this.currentPage=1,this.prevPage=0,this.pageChanged())},lastPage:function(){this.lastIsPossible&&(this.currentPage=this.pagesCount,this.prev=this.currentPage-1,this.pageChanged())},nextPage:function(){this.nextIsPossible&&(this.prevPage=this.currentPage,++this.currentPage,this.pageChanged())},previousPage:function(){this.prevIsPossible&&(this.prevPage=this.currentPage,--this.currentPage,this.pageChanged())},pageChanged:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={currentPage:this.currentPage,prevPage:this.prevPage};e||(t.noEmit=!0),this.$emit("page-changed",t)},perPageChanged:function(e){e&&this.$emit("per-page-changed",{currentPerPage:this.currentPerPage}),this.changePage(1,!1)},handlePerPage:function(){if(null!==this.customRowsPerPageDropdown&&Array.isArray(this.customRowsPerPageDropdown)&&0!==this.customRowsPerPageDropdown.length?this.rowsPerPageOptions=JSON.parse(JSON.stringify(this.customRowsPerPageDropdown)):this.rowsPerPageOptions=JSON.parse(JSON.stringify(f)),this.perPage){this.currentPerPage=this.perPage;for(var e=!1,t=0;t<this.rowsPerPageOptions.length;t++)this.rowsPerPageOptions[t]===this.perPage&&(e=!0);e||-1===this.perPage||this.rowsPerPageOptions.unshift(this.perPage)}else this.currentPerPage=10}},mounted:function(){},components:{"pagination-page-info":P({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"footer__navigation__page-info"},[e.infoFn?n("div",[e._v("\n    "+e._s(e.infoFn(e.infoParams))+"\n  ")]):"pages"===e.mode?n("form",{on:{submit:function(e){e.preventDefault()}}},[n("label",{staticClass:"page-info__label",attrs:{for:e.id}},[n("span",[e._v(e._s(e.pageText))]),e._v(" "),n("input",{staticClass:"footer__navigation__page-info__current-entry",attrs:{id:e.id,"aria-describedby":"change-page-hint","aria-controls":"vgb-table",type:"text"},domProps:{value:e.currentPage},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.stopPropagation(),e.changePage(t))}}}),e._v(" "),n("span",[e._v(e._s(e.pageInfo))])]),e._v(" "),n("span",{staticStyle:{display:"none"},attrs:{id:"change-page-hint"}},[e._v("\n      Type a page number and press Enter to change the page.\n    ")])]):n("div",[e._v("\n    "+e._s(e.recordInfo)+"\n  ")])])},staticRenderFns:[]},void 0,{name:"VgtPaginationPageInfo",props:{currentPage:{default:1},lastPage:{default:1},totalRecords:{default:0},ofText:{default:"of",type:String},pageText:{default:"page",type:String},currentPerPage:{},mode:{default:h},infoFn:{default:null}},data:function(){return{id:this.getId()}},computed:{pageInfo:function(){return"".concat(this.ofText," ").concat(this.lastPage)},firstRecordOnPage:function(){return(this.currentPage-1)*this.currentPerPage+1},lastRecordOnPage:function(){return-1===this.currentPerPage?this.totalRecords:Math.min(this.totalRecords,this.currentPage*this.currentPerPage)},recordInfo:function(){var e=this.firstRecordOnPage,t=this.lastRecordOnPage;return 0===t&&(e=0),"".concat(e," - ").concat(t," ").concat(this.ofText," ").concat(this.totalRecords)},infoParams:function(){var e=this.firstRecordOnPage,t=this.lastRecordOnPage;return 0===t&&(e=0),{firstRecordOnPage:e,lastRecordOnPage:t,totalRecords:this.totalRecords,currentPage:this.currentPage,totalPage:this.lastPage}}},methods:{getId:function(){return"vgt-page-input-".concat(Math.floor(Math.random()*Date.now()))},changePage:function(e){var t=parseInt(e.target.value,10);if(Number.isNaN(t)||t>this.lastPage||t<1)return e.target.value=this.currentPage,!1;e.target.value=t,this.$emit("page-changed",t)}},mounted:function(){},components:{}},"data-v-347cbcfa",!1,void 0,!1,void 0,void 0,void 0)}},void 0,!1,void 0,!1,void 0,void 0,void 0),x=P({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.showControlBar?n("div",{staticClass:"vgt-global-search vgt-clearfix"},[n("div",{staticClass:"vgt-global-search__input vgt-pull-left"},[e.searchEnabled?n("form",{attrs:{role:"search"},on:{submit:function(e){e.preventDefault()}}},[n("label",{attrs:{for:e.id}},[e._m(0),e._v(" "),n("span",{staticClass:"sr-only"},[e._v("Search")])]),e._v(" "),n("input",{staticClass:"vgt-input vgt-pull-left",attrs:{id:e.id,type:"text",placeholder:e.globalSearchPlaceholder},domProps:{value:e.value},on:{input:function(t){return e.updateValue(t.target.value)},keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.entered(t.target.value)}}})]):e._e()]),e._v(" "),n("div",{staticClass:"vgt-global-search__actions vgt-pull-right"},[e._t("internal-table-actions")],2)]):e._e()},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("span",{staticClass:"input__icon",attrs:{"aria-hidden":"true"}},[t("div",{staticClass:"magnifying-glass"})])}]},void 0,{name:"VgtGlobalSearch",props:["value","searchEnabled","globalSearchPlaceholder"],data:function(){return{globalSearchTerm:null,id:this.getId()}},computed:{showControlBar:function(){return!!this.searchEnabled||!(!this.$slots||!this.$slots["internal-table-actions"])}},methods:{updateValue:function(e){this.$emit("input",e),this.$emit("on-keyup",e)},entered:function(e){this.$emit("on-enter",e)},getId:function(){return"vgt-search-".concat(Math.floor(Math.random()*Date.now()))}}},void 0,!1,void 0,!1,void 0,void 0,void 0);function T(e){return e.firstSortType||"asc"}function S(e,t){return c===T(t)&&e===u?d:e===u?c:c===T(t)&&e===c?u:e===c?d:c===T(t)&&e===d?c:u}var k=P({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("thead",[n("tr",[e.lineNumbers?n("th",{staticClass:"line-numbers",attrs:{scope:"col"}}):e._e(),e._v(" "),e.selectable?n("th",{staticClass:"vgt-checkbox-col",attrs:{scope:"col"}},[n("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected,indeterminate:e.allSelectedIndeterminate},on:{change:e.toggleSelectAll}})]):e._e(),e._v(" "),e._l(e.columns,(function(t,r){return t.hidden?e._e():n("th",{key:r,class:e.getHeaderClasses(t,r),style:e.columnStyles[r],attrs:{scope:"col",title:t.tooltip,"aria-sort":e.getColumnSortLong(t),"aria-controls":"col-"+r}},[e._t("table-column",[e._v("\n        "+e._s(t.label)+"\n      ")],{column:t}),e._v(" "),e.isSortableColumn(t)?n("button",{on:{click:function(n){return e.sort(n,t)}}},[n("span",{staticClass:"sr-only"},[e._v("\n          Sort table by "+e._s(t.label)+" in "+e._s(e.getColumnSortLong(t))+" order\n          ")])]):e._e()],2)}))],2),e._v(" "),n("vgt-filter-row",{ref:"filter-row",tag:"tr",attrs:{"global-search-enabled":e.searchEnabled,"line-numbers":e.lineNumbers,selectable:e.selectable,columns:e.columns,mode:e.mode,"typed-columns":e.typedColumns},on:{"filter-changed":e.filterRows},scopedSlots:e._u([{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)})],1)},staticRenderFns:[]},void 0,{name:"VgtTableHeader",props:{lineNumbers:{default:!1,type:Boolean},selectable:{default:!1,type:Boolean},allSelected:{default:!1,type:Boolean},allSelectedIndeterminate:{default:!1,type:Boolean},columns:{type:Array},mode:{type:String},typedColumns:{},sortable:{type:Boolean},multipleColumnSort:{type:Boolean,default:!0},getClasses:{type:Function},searchEnabled:{type:Boolean},tableRef:{},paginated:{}},watch:{columns:{handler:function(){this.setColumnStyles()},immediate:!0},tableRef:{handler:function(){this.setColumnStyles()},immediate:!0},paginated:{handler:function(){this.tableRef&&this.setColumnStyles()},deep:!0}},data:function(){return{checkBoxThStyle:{},lineNumberThStyle:{},columnStyles:[],sorts:[],ro:null}},computed:{},methods:{reset:function(){this.$refs["filter-row"].reset(!0)},toggleSelectAll:function(){this.$emit("on-toggle-select-all")},isSortableColumn:function(e){var t=e.sortable;return"boolean"==typeof t?t:this.sortable},sort:function(e,t){this.isSortableColumn(t)&&(e.shiftKey&&this.multipleColumnSort?this.sorts=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++)if(t.field===e[n].field)return n;return-1}(e,t);return-1===n?e.push({field:t.field,type:T(t)}):e[n].type=S(e[n].type,t),e}(this.sorts,t):this.sorts=function(e,t){var n=function(e,t){return 1===e.length&&e[0].field===t.field?e[0].type:void 0}(e,t),r=S(n,t);return[{field:t.field,type:n?r:T(t)}]}(this.sorts,t),this.$emit("on-sort-change",this.sorts))},setInitialSort:function(e){this.sorts=e,this.$emit("on-sort-change",this.sorts)},getColumnSort:function(e){for(var t=0;t<this.sorts.length;t+=1)if(this.sorts[t].field===e.field)return this.sorts[t].type||"asc";return null},getColumnSortLong:function(e){return"asc"===this.getColumnSort(e)?"ascending":"descending"},getHeaderClasses:function(e,t){return Object.assign({},this.getClasses(t,"th"),{sortable:this.isSortableColumn(e),"sorting sorting-desc":"desc"===this.getColumnSort(e),"sorting sorting-asc":"asc"===this.getColumnSort(e)})},filterRows:function(e){this.$emit("filter-changed",e)},getWidthStyle:function(e){return window&&window.getComputedStyle&&e?{width:window.getComputedStyle(e,null).width}:{width:"auto"}},setColumnStyles:function(){for(var e=[],t=0;t<this.columns.length;t++)if(this.tableRef){var n=0;this.selectable&&n++,this.lineNumbers&&n++;var r=this.tableRef.rows[0].cells[t+n];e.push(this.getWidthStyle(r))}else e.push({minWidth:this.columns[t].width?this.columns[t].width:"auto",maxWidth:this.columns[t].width?this.columns[t].width:"auto",width:this.columns[t].width?this.columns[t].width:"auto"});this.columnStyles=e},getColumnStyle:function(e,t){var n={minWidth:e.width?e.width:"auto",maxWidth:e.width?e.width:"auto",width:e.width?e.width:"auto"};if(this.tableRef){this.selectable&&t++,this.lineNumbers&&t++;var r=this.tableRef.rows[0].cells[t],a=window.getComputedStyle(r,null);n.width=a.width}return n}},mounted:function(){var e=this;this.$nextTick((function(){"ResizeObserver"in window&&(e.ro=new ResizeObserver((function(){e.setColumnStyles()})),e.ro.observe(e.$parent.$el),e.tableRef&&Array.from(e.$parent.$refs["table-header-primary"].$el.children[0].children).forEach((function(t){e.ro.observe(t)})))}))},beforeDestroy:function(){this.ro&&this.ro.disconnect()},components:{"vgt-filter-row":P({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.hasFilterRow?n("tr",[e.lineNumbers?n("th"):e._e(),e._v(" "),e.selectable?n("th"):e._e(),e._v(" "),e._l(e.columns,(function(t,r){return t.hidden?e._e():n("th",{key:r,class:e.getClasses(t)},[e._t("column-filter",[e.isFilterable(t)?n("div",[e.isDropdown(t)?e._e():n("input",{staticClass:"vgt-input",attrs:{name:e.getName(t),type:"text",placeholder:e.getPlaceholder(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{keyup:function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"enter",13,n.key,"Enter")?null:e.updateFiltersOnEnter(t,n.target.value)},input:function(n){return e.updateFiltersOnKeyup(t,n.target.value)}}}),e._v(" "),e.isDropdownArray(t)?n("select",{staticClass:"vgt-select",attrs:{name:e.getName(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{change:function(n){return e.updateFiltersImmediately(t.field,n.target.value)}}},[n("option",{key:"-1",attrs:{value:""}},[e._v(e._s(e.getPlaceholder(t)))]),e._v(" "),e._l(t.filterOptions.filterDropdownItems,(function(t,r){return n("option",{key:r,domProps:{value:t}},[e._v("\n              "+e._s(t)+"\n            ")])}))],2):e._e(),e._v(" "),e.isDropdownObjects(t)?n("select",{staticClass:"vgt-select",attrs:{name:e.getName(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{change:function(n){return e.updateFiltersImmediately(t.field,n.target.value)}}},[n("option",{key:"-1",attrs:{value:""}},[e._v(e._s(e.getPlaceholder(t)))]),e._v(" "),e._l(t.filterOptions.filterDropdownItems,(function(t,r){return n("option",{key:r,domProps:{value:t.value}},[e._v(e._s(t.text))])}))],2):e._e()]):e._e()],{column:t,updateFilters:e.updateSlotFilter})],2)}))],2):e._e()},staticRenderFns:[]},void 0,{name:"VgtFilterRow",props:["lineNumbers","columns","typedColumns","globalSearchEnabled","selectable","mode"],watch:{columns:{handler:function(e,t){this.populateInitialFilters()},deep:!0,immediate:!0}},data:function(){return{columnFilters:{},timer:null}},computed:{hasFilterRow:function(){for(var e=0;e<this.columns.length;e++){var t=this.columns[e];if(t.filterOptions&&t.filterOptions.enabled)return!0}return!1}},methods:{fieldKey:function(e){return"function"==typeof e&&e.name?e.name:e},reset:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.columnFilters={},e&&this.$emit("filter-changed",this.columnFilters)},isFilterable:function(e){return e.filterOptions&&e.filterOptions.enabled},isDropdown:function(e){return this.isFilterable(e)&&e.filterOptions.filterDropdownItems&&e.filterOptions.filterDropdownItems.length},isDropdownObjects:function(e){return this.isDropdown(e)&&"object"===r(e.filterOptions.filterDropdownItems[0])},isDropdownArray:function(e){return this.isDropdown(e)&&"object"!==r(e.filterOptions.filterDropdownItems[0])},getClasses:function(e){var t="filter-th";return e.filterOptions&&e.filterOptions.styleClass?[t].concat(i(e.filterOptions.styleClass.split(" "))).join(" "):t},getPlaceholder:function(e){return this.isFilterable(e)&&e.filterOptions.placeholder||"Filter ".concat(e.label)},getName:function(e){return"vgt-".concat(this.fieldKey(e.field))},updateFiltersOnEnter:function(e,t){this.timer&&clearTimeout(this.timer),this.updateFiltersImmediately(e.field,t)},updateFiltersOnKeyup:function(e,t){"enter"!==e.filterOptions.trigger&&this.updateFilters(e,t)},updateSlotFilter:function(e,t){var n=e.filterOptions.slotFilterField||e.field;"function"==typeof e.filterOptions.formatValue&&(t=e.filterOptions.formatValue(t)),this.updateFiltersImmediately(n,t)},updateFilters:function(e,t){var n=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){n.updateFiltersImmediately(e.field,t)}),400)},updateFiltersImmediately:function(e,t){this.$set(this.columnFilters,this.fieldKey(e),t),this.$emit("filter-changed",this.columnFilters)},populateInitialFilters:function(){for(var e=0;e<this.columns.length;e++){var t=this.columns[e];this.isFilterable(t)&&void 0!==t.filterOptions.filterValue&&null!==t.filterOptions.filterValue&&this.$set(this.columnFilters,this.fieldKey(t.field),t.filterOptions.filterValue)}this.$emit("filter-changed",this.columnFilters)}}},"data-v-6869bf1c",!1,void 0,!1,void 0,void 0,void 0)}},void 0,!1,void 0,!1,void 0,void 0,void 0),O=P({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("tr",["span"===e.headerRow.mode?n("th",{staticClass:"vgt-left-align vgt-row-header",attrs:{colspan:e.fullColspan}},[e.selectAllByGroup?[e._t("table-header-group-select",[n("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected},on:{change:function(t){return e.toggleSelectGroup(t)}}})],{columns:e.columns,row:e.headerRow})]:e._e(),e._v(" "),n("span",{on:{click:function(t){e.collapsable&&e.$emit("vgtExpand",!e.headerRow.vgtIsExpanded)}}},[e.collapsable?n("span",{staticClass:"triangle",class:{expand:e.headerRow.vgtIsExpanded}}):e._e(),e._v(" "),e._t("table-header-row",[e.headerRow.html?n("span",{domProps:{innerHTML:e._s(e.headerRow.label)}}):n("span",[e._v("\n          "+e._s(e.headerRow.label)+"\n        ")])],{row:e.headerRow})],2)],2):e._e(),e._v(" "),"span"!==e.headerRow.mode&&e.lineNumbers?n("th",{staticClass:"vgt-row-header"}):e._e(),e._v(" "),"span"!==e.headerRow.mode&&e.selectable?n("th",{staticClass:"vgt-row-header"},[e.selectAllByGroup?[e._t("table-header-group-select",[n("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected},on:{change:function(t){return e.toggleSelectGroup(t)}}})],{columns:e.columns,row:e.headerRow})]:e._e()],2):e._e(),e._v(" "),e._l(e.columns,(function(t,r){return"span"===e.headerRow.mode||t.hidden?e._e():n("th",{key:r,staticClass:"vgt-row-header",class:e.getClasses(r,"td"),on:{click:function(t){e.columnCollapsable(r)&&e.$emit("vgtExpand",!e.headerRow.vgtIsExpanded)}}},[e.columnCollapsable(r)?n("span",{staticClass:"triangle",class:{expand:e.headerRow.vgtIsExpanded}}):e._e(),e._v(" "),e._t("table-header-row",[t.html?e._e():n("span",[e._v("\n        "+e._s(e.collectFormatted(e.headerRow,t,!0))+"\n      ")]),e._v(" "),t.html?n("span",{domProps:{innerHTML:e._s(e.collectFormatted(e.headerRow,t,!0))}}):e._e()],{row:e.headerRow,column:t,formattedRow:e.formattedRow(e.headerRow,!0)})],2)}))],2)},staticRenderFns:[]},void 0,{name:"VgtHeaderRow",props:{headerRow:{type:Object},columns:{type:Array},lineNumbers:{type:Boolean},selectable:{type:Boolean},selectAllByGroup:{type:Boolean},collapsable:{type:[Boolean,Number],default:!1},collectFormatted:{type:Function},formattedRow:{type:Function},getClasses:{type:Function},fullColspan:{type:Number},groupIndex:{type:Number}},data:function(){return{}},computed:{allSelected:function(){var e=this.headerRow;this.groupChildObject;return e.children.filter((function(e){return e.vgtSelected})).length===e.children.length}},methods:{columnCollapsable:function(e){return!0===this.collapsable?0===e:e===this.collapsable},toggleSelectGroup:function(e){this.$emit("on-select-group-change",{groupIndex:this.groupIndex,checked:e.target.checked})}},mounted:function(){},components:{}},void 0,!1,void 0,!1,void 0,void 0,void 0);function R(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function D(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function E(e){D(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):("string"!=typeof e&&"[object String]"!==t||"undefined"==typeof console||(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://git.io/fjule"),console.warn((new Error).stack)),new Date(NaN))}function F(e,t){D(2,arguments);var n=E(e).getTime(),r=R(t);return new Date(n+r)}function M(e){return e.getTime()%6e4}function I(e){var t=new Date(e.getTime()),n=Math.ceil(t.getTimezoneOffset());return t.setSeconds(0,0),6e4*n+(n>0?(6e4+M(t))%6e4:M(t))}function U(e){D(1,arguments);var t=E(e);return!isNaN(t)}var j={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function N(e){return function(t){var n=t||{},r=n.width?String(n.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}var A={date:N({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:N({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:N({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},$={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function L(e){return function(t,n){var r,a=n||{};if("formatting"===(a.context?String(a.context):"standalone")&&e.formattingValues){var o=e.defaultFormattingWidth||e.defaultWidth,i=a.width?String(a.width):o;r=e.formattingValues[i]||e.formattingValues[o]}else{var s=e.defaultWidth,l=a.width?String(a.width):e.defaultWidth;r=e.values[l]||e.values[s]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function B(e){return function(t,n){var r=String(t),a=n||{},o=a.width,i=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],s=r.match(i);if(!s)return null;var l,u=s[0],c=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth];return l="[object Array]"===Object.prototype.toString.call(c)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(c,(function(e){return e.test(u)})):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(c,(function(e){return e.test(u)})),l=e.valueCallback?e.valueCallback(l):l,{value:l=a.valueCallback?a.valueCallback(l):l,rest:r.slice(u.length)}}}var H,q={code:"en-US",formatDistance:function(e,t,n){var r;return n=n||{},r="string"==typeof j[e]?j[e]:1===t?j[e].one:j[e].other.replace("{{count}}",t),n.addSuffix?n.comparison>0?"in "+r:r+" ago":r},formatLong:A,formatRelative:function(e,t,n,r){return $[e]},localize:{ordinalNumber:function(e,t){var n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:L({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:L({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return Number(e)-1}}),month:L({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:L({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:L({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(H={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}},function(e,t){var n=String(e),r=t||{},a=n.match(H.matchPattern);if(!a)return null;var o=a[0],i=n.match(H.parsePattern);if(!i)return null;var s=H.valueCallback?H.valueCallback(i[0]):i[0];return{value:s=r.valueCallback?r.valueCallback(s):s,rest:n.slice(o.length)}}),era:B({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:B({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:B({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:B({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:B({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function Y(e,t){D(2,arguments);var n=R(t);return F(e,-n)}function z(e,t){for(var n=e<0?"-":"",r=Math.abs(e).toString();r.length<t;)r="0"+r;return n+r}var W=function(e,t){var n=e.getUTCFullYear(),r=n>0?n:1-n;return z("yy"===t?r%100:r,t.length)},Q=function(e,t){var n=e.getUTCMonth();return"M"===t?String(n+1):z(n+1,2)},G=function(e,t){return z(e.getUTCDate(),t.length)},X=function(e,t){return z(e.getUTCHours()%12||12,t.length)},K=function(e,t){return z(e.getUTCHours(),t.length)},J=function(e,t){return z(e.getUTCMinutes(),t.length)},V=function(e,t){return z(e.getUTCSeconds(),t.length)},Z=function(e,t){var n=t.length,r=e.getUTCMilliseconds();return z(Math.floor(r*Math.pow(10,n-3)),t.length)};function ee(e){D(1,arguments);var t=1,n=E(e),r=n.getUTCDay(),a=(r<t?7:0)+r-t;return n.setUTCDate(n.getUTCDate()-a),n.setUTCHours(0,0,0,0),n}function te(e){D(1,arguments);var t=E(e),n=t.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var a=ee(r),o=new Date(0);o.setUTCFullYear(n,0,4),o.setUTCHours(0,0,0,0);var i=ee(o);return t.getTime()>=a.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function ne(e){D(1,arguments);var t=te(e),n=new Date(0);n.setUTCFullYear(t,0,4),n.setUTCHours(0,0,0,0);var r=ee(n);return r}function re(e){D(1,arguments);var t=E(e),n=ee(t).getTime()-ne(t).getTime();return Math.round(n/6048e5)+1}function ae(e,t){D(1,arguments);var n=t||{},r=n.locale,a=r&&r.options&&r.options.weekStartsOn,o=null==a?0:R(a),i=null==n.weekStartsOn?o:R(n.weekStartsOn);if(!(i>=0&&i<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var s=E(e),l=s.getUTCDay(),u=(l<i?7:0)+l-i;return s.setUTCDate(s.getUTCDate()-u),s.setUTCHours(0,0,0,0),s}function oe(e,t){D(1,arguments);var n=E(e,t),r=n.getUTCFullYear(),a=t||{},o=a.locale,i=o&&o.options&&o.options.firstWeekContainsDate,s=null==i?1:R(i),l=null==a.firstWeekContainsDate?s:R(a.firstWeekContainsDate);if(!(l>=1&&l<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var u=new Date(0);u.setUTCFullYear(r+1,0,l),u.setUTCHours(0,0,0,0);var c=ae(u,t),d=new Date(0);d.setUTCFullYear(r,0,l),d.setUTCHours(0,0,0,0);var h=ae(d,t);return n.getTime()>=c.getTime()?r+1:n.getTime()>=h.getTime()?r:r-1}function ie(e,t){D(1,arguments);var n=t||{},r=n.locale,a=r&&r.options&&r.options.firstWeekContainsDate,o=null==a?1:R(a),i=null==n.firstWeekContainsDate?o:R(n.firstWeekContainsDate),s=oe(e,t),l=new Date(0);l.setUTCFullYear(s,0,i),l.setUTCHours(0,0,0,0);var u=ae(l,t);return u}function se(e,t){D(1,arguments);var n=E(e),r=ae(n,t).getTime()-ie(n,t).getTime();return Math.round(r/6048e5)+1}var le="midnight",ue="noon",ce="morning",de="afternoon",he="evening",fe="night",ge={G:function(e,t,n){var r=e.getUTCFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){var r=e.getUTCFullYear(),a=r>0?r:1-r;return n.ordinalNumber(a,{unit:"year"})}return W(e,t)},Y:function(e,t,n,r){var a=oe(e,r),o=a>0?a:1-a;return"YY"===t?z(o%100,2):"Yo"===t?n.ordinalNumber(o,{unit:"year"}):z(o,t.length)},R:function(e,t){return z(te(e),t.length)},u:function(e,t){return z(e.getUTCFullYear(),t.length)},Q:function(e,t,n){var r=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return z(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){var r=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return z(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){var r=e.getUTCMonth();switch(t){case"M":case"MM":return Q(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){var r=e.getUTCMonth();switch(t){case"L":return String(r+1);case"LL":return z(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){var a=se(e,r);return"wo"===t?n.ordinalNumber(a,{unit:"week"}):z(a,t.length)},I:function(e,t,n){var r=re(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):z(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getUTCDate(),{unit:"date"}):G(e,t)},D:function(e,t,n){var r=function(e){D(1,arguments);var t=E(e),n=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var r=t.getTime(),a=n-r;return Math.floor(a/864e5)+1}(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):z(r,t.length)},E:function(e,t,n){var r=e.getUTCDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){var a=e.getUTCDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return z(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});case"eeee":default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){var a=e.getUTCDay(),o=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return z(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});case"cccc":default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(e,t,n){var r=e.getUTCDay(),a=0===r?7:r;switch(t){case"i":return String(a);case"ii":return z(a,t.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){var r=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){var r,a=e.getUTCHours();switch(r=12===a?ue:0===a?le:a/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){var r,a=e.getUTCHours();switch(r=a>=17?he:a>=12?de:a>=4?ce:fe,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){var r=e.getUTCHours()%12;return 0===r&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return X(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getUTCHours(),{unit:"hour"}):K(e,t)},K:function(e,t,n){var r=e.getUTCHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):z(r,t.length)},k:function(e,t,n){var r=e.getUTCHours();return 0===r&&(r=24),"ko"===t?n.ordinalNumber(r,{unit:"hour"}):z(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):J(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):V(e,t)},S:function(e,t){return Z(e,t)},X:function(e,t,n,r){var a=(r._originalDate||e).getTimezoneOffset();if(0===a)return"Z";switch(t){case"X":return me(a);case"XXXX":case"XX":return ve(a);case"XXXXX":case"XXX":default:return ve(a,":")}},x:function(e,t,n,r){var a=(r._originalDate||e).getTimezoneOffset();switch(t){case"x":return me(a);case"xxxx":case"xx":return ve(a);case"xxxxx":case"xxx":default:return ve(a,":")}},O:function(e,t,n,r){var a=(r._originalDate||e).getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+pe(a,":");case"OOOO":default:return"GMT"+ve(a,":")}},z:function(e,t,n,r){var a=(r._originalDate||e).getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+pe(a,":");case"zzzz":default:return"GMT"+ve(a,":")}},t:function(e,t,n,r){var a=r._originalDate||e;return z(Math.floor(a.getTime()/1e3),t.length)},T:function(e,t,n,r){return z((r._originalDate||e).getTime(),t.length)}};function pe(e,t){var n=e>0?"-":"+",r=Math.abs(e),a=Math.floor(r/60),o=r%60;if(0===o)return n+String(a);var i=t||"";return n+String(a)+i+z(o,2)}function me(e,t){return e%60==0?(e>0?"-":"+")+z(Math.abs(e)/60,2):ve(e,t)}function ve(e,t){var n=t||"",r=e>0?"-":"+",a=Math.abs(e);return r+z(Math.floor(a/60),2)+n+z(a%60,2)}function we(e,t){switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}}function be(e,t){switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}}var ye={p:be,P:function(e,t){var n,r=e.match(/(P+)(p+)?/),a=r[1],o=r[2];if(!o)return we(e,t);switch(a){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;case"PPPP":default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",we(a,t)).replace("{{time}}",be(o,t))}},_e=["D","DD"],Pe=["YY","YYYY"];function Ce(e){return-1!==_e.indexOf(e)}function xe(e){return-1!==Pe.indexOf(e)}function Te(e,t,n){if("YYYY"===e)throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://git.io/fxCyr"));if("YY"===e)throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://git.io/fxCyr"));if("D"===e)throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://git.io/fxCyr"));if("DD"===e)throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://git.io/fxCyr"))}var Se=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ke=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Oe=/^'([^]*?)'?$/,Re=/''/g,De=/[a-zA-Z]/;function Ee(e){return e.match(Oe)[1].replace(Re,"'")}function Fe(e,t){if(null==e)throw new TypeError("assign requires that input parameter not be null or undefined");for(var n in t=t||{})t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function Me(e,t,n){D(2,arguments);var r=n||{},a=r.locale,o=a&&a.options&&a.options.weekStartsOn,i=null==o?0:R(o),s=null==r.weekStartsOn?i:R(r.weekStartsOn);if(!(s>=0&&s<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var l=E(e),u=R(t),c=l.getUTCDay(),d=u%7,h=(d+7)%7,f=(h<s?7:0)+u-c;return l.setUTCDate(l.getUTCDate()+f),l}var Ie=/^(1[0-2]|0?\d)/,Ue=/^(3[0-1]|[0-2]?\d)/,je=/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,Ne=/^(5[0-3]|[0-4]?\d)/,Ae=/^(2[0-3]|[0-1]?\d)/,$e=/^(2[0-4]|[0-1]?\d)/,Le=/^(1[0-1]|0?\d)/,Be=/^(1[0-2]|0?\d)/,He=/^[0-5]?\d/,qe=/^[0-5]?\d/,Ye=/^\d/,ze=/^\d{1,2}/,We=/^\d{1,3}/,Qe=/^\d{1,4}/,Ge=/^-?\d+/,Xe=/^-?\d/,Ke=/^-?\d{1,2}/,Je=/^-?\d{1,3}/,Ve=/^-?\d{1,4}/,Ze=/^([+-])(\d{2})(\d{2})?|Z/,et=/^([+-])(\d{2})(\d{2})|Z/,tt=/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,nt=/^([+-])(\d{2}):(\d{2})|Z/,rt=/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/;function at(e,t,n){var r=t.match(e);if(!r)return null;var a=parseInt(r[0],10);return{value:n?n(a):a,rest:t.slice(r[0].length)}}function ot(e,t){var n=t.match(e);return n?"Z"===n[0]?{value:0,rest:t.slice(1)}:{value:("+"===n[1]?1:-1)*(36e5*(n[2]?parseInt(n[2],10):0)+6e4*(n[3]?parseInt(n[3],10):0)+1e3*(n[5]?parseInt(n[5],10):0)),rest:t.slice(n[0].length)}:null}function it(e,t){return at(Ge,e,t)}function st(e,t,n){switch(e){case 1:return at(Ye,t,n);case 2:return at(ze,t,n);case 3:return at(We,t,n);case 4:return at(Qe,t,n);default:return at(new RegExp("^\\d{1,"+e+"}"),t,n)}}function lt(e,t,n){switch(e){case 1:return at(Xe,t,n);case 2:return at(Ke,t,n);case 3:return at(Je,t,n);case 4:return at(Ve,t,n);default:return at(new RegExp("^-?\\d{1,"+e+"}"),t,n)}}function ut(e){switch(e){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function ct(e,t){var n,r=t>0,a=r?t:1-t;if(a<=50)n=e||100;else{var o=a+50;n=e+100*Math.floor(o/100)-(e>=o%100?100:0)}return r?n:1-n}var dt=[31,28,31,30,31,30,31,31,30,31,30,31],ht=[31,29,31,30,31,30,31,31,30,31,30,31];function ft(e){return e%400==0||e%4==0&&e%100!=0}var gt={G:{priority:140,parse:function(e,t,n,r){switch(t){case"G":case"GG":case"GGG":return n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"});case"GGGGG":return n.era(e,{width:"narrow"});case"GGGG":default:return n.era(e,{width:"wide"})||n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"})}},set:function(e,t,n,r){return t.era=n,e.setUTCFullYear(n,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["R","u","t","T"]},y:{priority:130,parse:function(e,t,n,r){var a=function(e){return{year:e,isTwoDigitYear:"yy"===t}};switch(t){case"y":return st(4,e,a);case"yo":return n.ordinalNumber(e,{unit:"year",valueCallback:a});default:return st(t.length,e,a)}},validate:function(e,t,n){return t.isTwoDigitYear||t.year>0},set:function(e,t,n,r){var a=e.getUTCFullYear();if(n.isTwoDigitYear){var o=ct(n.year,a);return e.setUTCFullYear(o,0,1),e.setUTCHours(0,0,0,0),e}var i="era"in t&&1!==t.era?1-n.year:n.year;return e.setUTCFullYear(i,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","u","w","I","i","e","c","t","T"]},Y:{priority:130,parse:function(e,t,n,r){var a=function(e){return{year:e,isTwoDigitYear:"YY"===t}};switch(t){case"Y":return st(4,e,a);case"Yo":return n.ordinalNumber(e,{unit:"year",valueCallback:a});default:return st(t.length,e,a)}},validate:function(e,t,n){return t.isTwoDigitYear||t.year>0},set:function(e,t,n,r){var a=oe(e,r);if(n.isTwoDigitYear){var o=ct(n.year,a);return e.setUTCFullYear(o,0,r.firstWeekContainsDate),e.setUTCHours(0,0,0,0),ae(e,r)}var i="era"in t&&1!==t.era?1-n.year:n.year;return e.setUTCFullYear(i,0,r.firstWeekContainsDate),e.setUTCHours(0,0,0,0),ae(e,r)},incompatibleTokens:["y","R","u","Q","q","M","L","I","d","D","i","t","T"]},R:{priority:130,parse:function(e,t,n,r){return lt("R"===t?4:t.length,e)},set:function(e,t,n,r){var a=new Date(0);return a.setUTCFullYear(n,0,4),a.setUTCHours(0,0,0,0),ee(a)},incompatibleTokens:["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"]},u:{priority:130,parse:function(e,t,n,r){return lt("u"===t?4:t.length,e)},set:function(e,t,n,r){return e.setUTCFullYear(n,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["G","y","Y","R","w","I","i","e","c","t","T"]},Q:{priority:120,parse:function(e,t,n,r){switch(t){case"Q":case"QQ":return st(t.length,e);case"Qo":return n.ordinalNumber(e,{unit:"quarter"});case"QQQ":return n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(e,{width:"wide",context:"formatting"})||n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=1&&t<=4},set:function(e,t,n,r){return e.setUTCMonth(3*(n-1),1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"]},q:{priority:120,parse:function(e,t,n,r){switch(t){case"q":case"qq":return st(t.length,e);case"qo":return n.ordinalNumber(e,{unit:"quarter"});case"qqq":return n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return n.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(e,{width:"wide",context:"standalone"})||n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,n){return t>=1&&t<=4},set:function(e,t,n,r){return e.setUTCMonth(3*(n-1),1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"]},M:{priority:110,parse:function(e,t,n,r){var a=function(e){return e-1};switch(t){case"M":return at(Ie,e,a);case"MM":return st(2,e,a);case"Mo":return n.ordinalNumber(e,{unit:"month",valueCallback:a});case"MMM":return n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return n.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(e,{width:"wide",context:"formatting"})||n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=0&&t<=11},set:function(e,t,n,r){return e.setUTCMonth(n,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]},L:{priority:110,parse:function(e,t,n,r){var a=function(e){return e-1};switch(t){case"L":return at(Ie,e,a);case"LL":return st(2,e,a);case"Lo":return n.ordinalNumber(e,{unit:"month",valueCallback:a});case"LLL":return n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return n.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(e,{width:"wide",context:"standalone"})||n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,n){return t>=0&&t<=11},set:function(e,t,n,r){return e.setUTCMonth(n,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","M","w","I","D","i","e","c","t","T"]},w:{priority:100,parse:function(e,t,n,r){switch(t){case"w":return at(Ne,e);case"wo":return n.ordinalNumber(e,{unit:"week"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=53},set:function(e,t,n,r){return ae(function(e,t,n){D(2,arguments);var r=E(e),a=R(t),o=se(r,n)-a;return r.setUTCDate(r.getUTCDate()-7*o),r}(e,n,r),r)},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","i","t","T"]},I:{priority:100,parse:function(e,t,n,r){switch(t){case"I":return at(Ne,e);case"Io":return n.ordinalNumber(e,{unit:"week"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=53},set:function(e,t,n,r){return ee(function(e,t){D(2,arguments);var n=E(e),r=R(t),a=re(n)-r;return n.setUTCDate(n.getUTCDate()-7*a),n}(e,n,r),r)},incompatibleTokens:["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"]},d:{priority:90,subPriority:1,parse:function(e,t,n,r){switch(t){case"d":return at(Ue,e);case"do":return n.ordinalNumber(e,{unit:"date"});default:return st(t.length,e)}},validate:function(e,t,n){var r=ft(e.getUTCFullYear()),a=e.getUTCMonth();return r?t>=1&&t<=ht[a]:t>=1&&t<=dt[a]},set:function(e,t,n,r){return e.setUTCDate(n),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","w","I","D","i","e","c","t","T"]},D:{priority:90,subPriority:1,parse:function(e,t,n,r){switch(t){case"D":case"DD":return at(je,e);case"Do":return n.ordinalNumber(e,{unit:"date"});default:return st(t.length,e)}},validate:function(e,t,n){return ft(e.getUTCFullYear())?t>=1&&t<=366:t>=1&&t<=365},set:function(e,t,n,r){return e.setUTCMonth(0,n),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"]},E:{priority:90,parse:function(e,t,n,r){switch(t){case"E":case"EE":case"EEE":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return n.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEE":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=0&&t<=6},set:function(e,t,n,r){return(e=Me(e,n,r)).setUTCHours(0,0,0,0),e},incompatibleTokens:["D","i","e","c","t","T"]},e:{priority:90,parse:function(e,t,n,r){var a=function(e){var t=7*Math.floor((e-1)/7);return(e+r.weekStartsOn+6)%7+t};switch(t){case"e":case"ee":return st(t.length,e,a);case"eo":return n.ordinalNumber(e,{unit:"day",valueCallback:a});case"eee":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeeee":return n.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeee":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=0&&t<=6},set:function(e,t,n,r){return(e=Me(e,n,r)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"]},c:{priority:90,parse:function(e,t,n,r){var a=function(e){var t=7*Math.floor((e-1)/7);return(e+r.weekStartsOn+6)%7+t};switch(t){case"c":case"cc":return st(t.length,e,a);case"co":return n.ordinalNumber(e,{unit:"day",valueCallback:a});case"ccc":return n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"ccccc":return n.day(e,{width:"narrow",context:"standalone"});case"cccccc":return n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"cccc":default:return n.day(e,{width:"wide",context:"standalone"})||n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,n){return t>=0&&t<=6},set:function(e,t,n,r){return(e=Me(e,n,r)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"]},i:{priority:90,parse:function(e,t,n,r){var a=function(e){return 0===e?7:e};switch(t){case"i":case"ii":return st(t.length,e);case"io":return n.ordinalNumber(e,{unit:"day"});case"iii":return n.day(e,{width:"abbreviated",context:"formatting",valueCallback:a})||n.day(e,{width:"short",context:"formatting",valueCallback:a})||n.day(e,{width:"narrow",context:"formatting",valueCallback:a});case"iiiii":return n.day(e,{width:"narrow",context:"formatting",valueCallback:a});case"iiiiii":return n.day(e,{width:"short",context:"formatting",valueCallback:a})||n.day(e,{width:"narrow",context:"formatting",valueCallback:a});case"iiii":default:return n.day(e,{width:"wide",context:"formatting",valueCallback:a})||n.day(e,{width:"abbreviated",context:"formatting",valueCallback:a})||n.day(e,{width:"short",context:"formatting",valueCallback:a})||n.day(e,{width:"narrow",context:"formatting",valueCallback:a})}},validate:function(e,t,n){return t>=1&&t<=7},set:function(e,t,n,r){return(e=function(e,t){D(2,arguments);var n=R(t);n%7==0&&(n-=7);var r=1,a=E(e),o=a.getUTCDay(),i=n%7,s=(i+7)%7,l=(s<r?7:0)+n-o;return a.setUTCDate(a.getUTCDate()+l),a}(e,n,r)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"]},a:{priority:80,parse:function(e,t,n,r){switch(t){case"a":case"aa":case"aaa":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,n,r){return e.setUTCHours(ut(n),0,0,0),e},incompatibleTokens:["b","B","H","K","k","t","T"]},b:{priority:80,parse:function(e,t,n,r){switch(t){case"b":case"bb":case"bbb":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,n,r){return e.setUTCHours(ut(n),0,0,0),e},incompatibleTokens:["a","B","H","K","k","t","T"]},B:{priority:80,parse:function(e,t,n,r){switch(t){case"B":case"BB":case"BBB":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,n,r){return e.setUTCHours(ut(n),0,0,0),e},incompatibleTokens:["a","b","t","T"]},h:{priority:70,parse:function(e,t,n,r){switch(t){case"h":return at(Be,e);case"ho":return n.ordinalNumber(e,{unit:"hour"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=12},set:function(e,t,n,r){var a=e.getUTCHours()>=12;return a&&n<12?e.setUTCHours(n+12,0,0,0):a||12!==n?e.setUTCHours(n,0,0,0):e.setUTCHours(0,0,0,0),e},incompatibleTokens:["H","K","k","t","T"]},H:{priority:70,parse:function(e,t,n,r){switch(t){case"H":return at(Ae,e);case"Ho":return n.ordinalNumber(e,{unit:"hour"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=23},set:function(e,t,n,r){return e.setUTCHours(n,0,0,0),e},incompatibleTokens:["a","b","h","K","k","t","T"]},K:{priority:70,parse:function(e,t,n,r){switch(t){case"K":return at(Le,e);case"Ko":return n.ordinalNumber(e,{unit:"hour"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=11},set:function(e,t,n,r){return e.getUTCHours()>=12&&n<12?e.setUTCHours(n+12,0,0,0):e.setUTCHours(n,0,0,0),e},incompatibleTokens:["a","b","h","H","k","t","T"]},k:{priority:70,parse:function(e,t,n,r){switch(t){case"k":return at($e,e);case"ko":return n.ordinalNumber(e,{unit:"hour"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=24},set:function(e,t,n,r){var a=n<=24?n%24:n;return e.setUTCHours(a,0,0,0),e},incompatibleTokens:["a","b","h","H","K","t","T"]},m:{priority:60,parse:function(e,t,n,r){switch(t){case"m":return at(He,e);case"mo":return n.ordinalNumber(e,{unit:"minute"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=59},set:function(e,t,n,r){return e.setUTCMinutes(n,0,0),e},incompatibleTokens:["t","T"]},s:{priority:50,parse:function(e,t,n,r){switch(t){case"s":return at(qe,e);case"so":return n.ordinalNumber(e,{unit:"second"});default:return st(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=59},set:function(e,t,n,r){return e.setUTCSeconds(n,0),e},incompatibleTokens:["t","T"]},S:{priority:30,parse:function(e,t,n,r){return st(t.length,e,(function(e){return Math.floor(e*Math.pow(10,3-t.length))}))},set:function(e,t,n,r){return e.setUTCMilliseconds(n),e},incompatibleTokens:["t","T"]},X:{priority:10,parse:function(e,t,n,r){switch(t){case"X":return ot(Ze,e);case"XX":return ot(et,e);case"XXXX":return ot(tt,e);case"XXXXX":return ot(rt,e);case"XXX":default:return ot(nt,e)}},set:function(e,t,n,r){return t.timestampIsSet?e:new Date(e.getTime()-n)},incompatibleTokens:["t","T","x"]},x:{priority:10,parse:function(e,t,n,r){switch(t){case"x":return ot(Ze,e);case"xx":return ot(et,e);case"xxxx":return ot(tt,e);case"xxxxx":return ot(rt,e);case"xxx":default:return ot(nt,e)}},set:function(e,t,n,r){return t.timestampIsSet?e:new Date(e.getTime()-n)},incompatibleTokens:["t","T","X"]},t:{priority:40,parse:function(e,t,n,r){return it(e)},set:function(e,t,n,r){return[new Date(1e3*n),{timestampIsSet:!0}]},incompatibleTokens:"*"},T:{priority:20,parse:function(e,t,n,r){return it(e)},set:function(e,t,n,r){return[new Date(n),{timestampIsSet:!0}]},incompatibleTokens:"*"}},pt=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,mt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,vt=/^'([^]*?)'?$/,wt=/''/g,bt=/\S/,yt=/[a-zA-Z]/;function _t(e,t,n,r){D(3,arguments);var a=String(e),o=String(t),i=r||{},s=i.locale||q;if(!s.match)throw new RangeError("locale must contain match property");var l=s.options&&s.options.firstWeekContainsDate,u=null==l?1:R(l),c=null==i.firstWeekContainsDate?u:R(i.firstWeekContainsDate);if(!(c>=1&&c<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var d=s.options&&s.options.weekStartsOn,h=null==d?0:R(d),f=null==i.weekStartsOn?h:R(i.weekStartsOn);if(!(f>=0&&f<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(""===o)return""===a?E(n):new Date(NaN);var g,p={firstWeekContainsDate:c,weekStartsOn:f,locale:s},m=[{priority:10,subPriority:-1,set:Pt,index:0}],v=o.match(mt).map((function(e){var t=e[0];return"p"===t||"P"===t?(0,ye[t])(e,s.formatLong,p):e})).join("").match(pt),w=[];for(g=0;g<v.length;g++){var b=v[g];!i.useAdditionalWeekYearTokens&&xe(b)&&Te(b,o,e),!i.useAdditionalDayOfYearTokens&&Ce(b)&&Te(b,o,e);var y=b[0],_=gt[y];if(_){var P=_.incompatibleTokens;if(Array.isArray(P)){for(var C=void 0,x=0;x<w.length;x++){var T=w[x].token;if(-1!==P.indexOf(T)||T===y){C=w[x];break}}if(C)throw new RangeError("The format string mustn't contain `".concat(C.fullToken,"` and `").concat(b,"` at the same time"))}else if("*"===_.incompatibleTokens&&w.length)throw new RangeError("The format string mustn't contain `".concat(b,"` and any other token at the same time"));w.push({token:y,fullToken:b});var S=_.parse(a,b,s.match,p);if(!S)return new Date(NaN);m.push({priority:_.priority,subPriority:_.subPriority||0,set:_.set,validate:_.validate,value:S.value,index:m.length}),a=S.rest}else{if(y.match(yt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+y+"`");if("''"===b?b="'":"'"===y&&(b=Ct(b)),0!==a.indexOf(b))return new Date(NaN);a=a.slice(b.length)}}if(a.length>0&&bt.test(a))return new Date(NaN);var k=m.map((function(e){return e.priority})).sort((function(e,t){return t-e})).filter((function(e,t,n){return n.indexOf(e)===t})).map((function(e){return m.filter((function(t){return t.priority===e})).sort((function(e,t){return t.subPriority-e.subPriority}))})).map((function(e){return e[0]})),O=E(n);if(isNaN(O))return new Date(NaN);var F=Y(O,I(O)),M={};for(g=0;g<k.length;g++){var U=k[g];if(U.validate&&!U.validate(F,U.value,p))return new Date(NaN);var j=U.set(F,M,U.value,p);j[0]?(F=j[0],Fe(M,j[1])):F=j}return F}function Pt(e,t){if(t.timestampIsSet)return e;var n=new Date(0);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}function Ct(e){return e.match(vt)[1].replace(wt,"'")}var xt=Object.assign({},_);xt.isRight=!0,xt.compare=function(e,t,n){function r(e){return n&&n.dateInputFormat?_t("".concat(e),"".concat(n.dateInputFormat),new Date):e}return e=r(e),t=r(t),U(e)?U(t)?function(e,t){D(2,arguments);var n=E(e),r=E(t),a=n.getTime()-r.getTime();return a<0?-1:a>0?1:a}(e,t):1:-1},xt.format=function(e,t){if(null==e)return"";var n=_t(e,t.dateInputFormat,new Date);return U(n)?function(e,t,n){D(2,arguments);var r=String(t),a=n||{},o=a.locale||q,i=o.options&&o.options.firstWeekContainsDate,s=null==i?1:R(i),l=null==a.firstWeekContainsDate?s:R(a.firstWeekContainsDate);if(!(l>=1&&l<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var u=o.options&&o.options.weekStartsOn,c=null==u?0:R(u),d=null==a.weekStartsOn?c:R(a.weekStartsOn);if(!(d>=0&&d<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!o.localize)throw new RangeError("locale must contain localize property");if(!o.formatLong)throw new RangeError("locale must contain formatLong property");var h=E(e);if(!U(h))throw new RangeError("Invalid time value");var f=I(h),g=Y(h,f),p={firstWeekContainsDate:l,weekStartsOn:d,locale:o,_originalDate:h},m=r.match(ke).map((function(e){var t=e[0];return"p"===t||"P"===t?(0,ye[t])(e,o.formatLong,p):e})).join("").match(Se).map((function(n){if("''"===n)return"'";var r=n[0];if("'"===r)return Ee(n);var i=ge[r];if(i)return!a.useAdditionalWeekYearTokens&&xe(n)&&Te(n,t,e),!a.useAdditionalDayOfYearTokens&&Ce(n)&&Te(n,t,e),i(g,n,o.localize,p);if(r.match(De))throw new RangeError("Format string contains an unescaped latin alphabet character `"+r+"`");return n})).join("");return m}(n,t.dateOutputFormat):(console.error('Not a valid date: "'.concat(e,'"')),null)};var Tt=Object.freeze({__proto__:null,default:xt}),St=Object.assign({},_);St.isRight=!0,St.filterPredicate=function(e,t){return 0===St.compare(e,t)},St.compare=function(e,t){function n(e){return null==e?-1/0:e.indexOf(".")>=0?parseFloat(e):parseInt(e,10)}return(e="number"==typeof e?e:n(e))<(t="number"==typeof t?t:n(t))?-1:e>t?1:0};var kt=Object.freeze({__proto__:null,default:St}),Ot=Object.assign({},St);Ot.format=function(e){return null==e?"":parseFloat(Math.round(100*e)/100).toFixed(2)};var Rt=Object.freeze({__proto__:null,default:Ot}),Dt=Object.assign({},St);Dt.format=function(e){return null==e?"":"".concat(parseFloat(100*e).toFixed(2),"%")};var Et=Object.freeze({__proto__:null,default:Dt}),Ft=Object.assign({},_);Ft.isRight=!0,Ft.filterPredicate=function(e,t){return 0===Ft.compare(e,t)},Ft.compare=function(e,t){function n(e){return"boolean"==typeof e?e?1:0:"string"==typeof e?"true"===e?1:0:-1/0}return(e=n(e))<(t=n(t))?-1:e>t?1:0};var Mt={},It={date:Tt,decimal:Rt,number:kt,percentage:Et,boolean:Object.freeze({__proto__:null,default:Ft})};Object.keys(It).forEach((function(e){var t=e.replace(/^\.\//,"").replace(/\.js/,"");Mt[t]=It[e].default}));var Ut=P({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:e.wrapStyleClasses},[e.isLoading?n("div",{staticClass:"vgt-loading vgt-center-align"},[e._t("loadingContent",[n("span",{staticClass:"vgt-loading__content"},[e._v("\n        Loading...\n      ")])])],2):e._e(),e._v(" "),n("div",{staticClass:"vgt-inner-wrap",class:{"is-loading":e.isLoading}},[e.paginate&&e.paginateOnTop?e._t("pagination-top",[n("vgt-pagination",{ref:"paginationTop",attrs:{perPage:e.perPage,rtl:e.rtl,total:e.totalRows||e.totalRowCount,mode:e.paginationMode,jumpFirstOrLast:e.paginationOptions.jumpFirstOrLast,firstText:e.firstText,lastText:e.lastText,nextText:e.nextText,prevText:e.prevText,rowsPerPageText:e.rowsPerPageText,perPageDropdownEnabled:e.paginationOptions.perPageDropdownEnabled,customRowsPerPageDropdown:e.customRowsPerPageDropdown,paginateDropdownAllowAll:e.paginateDropdownAllowAll,ofText:e.ofText,pageText:e.pageText,allText:e.allText,"info-fn":e.paginationInfoFn},on:{"page-changed":e.pageChanged,"per-page-changed":e.perPageChanged}})],{pageChanged:e.pageChanged,perPageChanged:e.perPageChanged,total:e.totalRows||e.totalRowCount}):e._e(),e._v(" "),n("vgt-global-search",{attrs:{"search-enabled":e.searchEnabled&&null==e.externalSearchQuery,"global-search-placeholder":e.searchPlaceholder},on:{"on-keyup":e.searchTableOnKeyUp,"on-enter":e.searchTableOnEnter},model:{value:e.globalSearchTerm,callback:function(t){e.globalSearchTerm=t},expression:"globalSearchTerm"}},[n("template",{slot:"internal-table-actions"},[e._t("table-actions")],2)],2),e._v(" "),e.selectedRowCount&&!e.disableSelectInfo?n("div",{staticClass:"vgt-selection-info-row clearfix",class:e.selectionInfoClass},[e._v("\n      "+e._s(e.selectionInfo)+"\n      "),n("a",{attrs:{href:""},on:{click:function(t){return t.preventDefault(),e.unselectAllInternal(!0)}}},[e._v("\n        "+e._s(e.clearSelectionText)+"\n      ")]),e._v(" "),n("div",{staticClass:"vgt-selection-info-row__actions vgt-pull-right"},[e._t("selected-row-actions")],2)]):e._e(),e._v(" "),n("div",{staticClass:"vgt-fixed-header"},[e.fixedHeader?n("table",{class:e.tableStyleClasses,attrs:{id:"vgt-table"}},[n("colgroup",e._l(e.columns,(function(e,t){return n("col",{key:t,attrs:{id:"col-"+t}})})),0),e._v(" "),n("vgt-table-header",{ref:"table-header-secondary",tag:"thead",attrs:{columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"all-selected":e.allSelected,"all-selected-indeterminate":e.allSelectedIndeterminate,mode:e.mode,sortable:e.sortable,"multiple-column-sort":e.multipleColumnSort,"typed-columns":e.typedColumns,getClasses:e.getClasses,searchEnabled:e.searchEnabled,paginated:e.paginated,"table-ref":e.$refs.table},on:{"on-toggle-select-all":e.toggleSelectAll,"on-sort-change":e.changeSort,"filter-changed":e.filterRows},scopedSlots:e._u([{key:"table-column",fn:function(t){return[e._t("table-column",[n("span",[e._v(e._s(t.column.label))])],{column:t.column})]}},{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)})],1):e._e()]),e._v(" "),n("div",{class:{"vgt-responsive":e.responsive},style:e.wrapperStyles},[n("table",{ref:"table",class:e.tableStyles,attrs:{id:"vgt-table"}},[n("colgroup",e._l(e.columns,(function(e,t){return n("col",{key:t,attrs:{id:"col-"+t}})})),0),e._v(" "),n("vgt-table-header",{ref:"table-header-primary",tag:"thead",attrs:{columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"all-selected":e.allSelected,"all-selected-indeterminate":e.allSelectedIndeterminate,mode:e.mode,sortable:e.sortable,"multiple-column-sort":e.multipleColumnSort,"typed-columns":e.typedColumns,getClasses:e.getClasses,searchEnabled:e.searchEnabled},on:{"on-toggle-select-all":e.toggleSelectAll,"on-sort-change":e.changeSort,"filter-changed":e.filterRows},scopedSlots:e._u([{key:"table-column",fn:function(t){return[e._t("table-column",[n("span",[e._v(e._s(t.column.label))])],{column:t.column})]}},{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)}),e._v(" "),e._l(e.paginated,(function(t,r){return n("tbody",{key:r},[e.groupHeaderOnTop?n("vgt-header-row",{class:e.getRowStyleClass(t),attrs:{"header-row":t,columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"select-all-by-group":e.selectAllByGroup,collapsable:e.groupOptions.collapsable,"collect-formatted":e.collectFormatted,"formatted-row":e.formattedRow,"get-classes":e.getClasses,"full-colspan":e.fullColspan,groupIndex:r},on:{vgtExpand:function(n){return e.toggleExpand(t[e.rowKeyField])},"on-select-group-change":function(n){return e.toggleSelectGroup(n,t)}},scopedSlots:e._u([{key:"table-header-row",fn:function(t){return e.hasHeaderRowTemplate?[e._t("table-header-row",null,{column:t.column,formattedRow:t.formattedRow,row:t.row})]:void 0}}],null,!0)}):e._e(),e._v(" "),e._l(t.children,(function(r,a){return!e.groupOptions.collapsable||t.vgtIsExpanded?n("tr",{key:r.originalIndex,class:e.getRowStyleClass(r),on:{mouseenter:function(t){return e.onMouseenter(r,a)},mouseleave:function(t){return e.onMouseleave(r,a)},dblclick:function(t){return e.onRowDoubleClicked(r,a,t)},click:function(t){return e.onRowClicked(r,a,t)},auxclick:function(t){return e.onRowAuxClicked(r,a,t)}}},[e.lineNumbers?n("th",{staticClass:"line-numbers"},[e._v("\n              "+e._s(e.getCurrentIndex(r.originalIndex))+"\n            ")]):e._e(),e._v(" "),e.selectable?n("th",{staticClass:"vgt-checkbox-col",on:{click:function(t){return t.stopPropagation(),e.onCheckboxClicked(r,a,t)}}},[n("input",{attrs:{type:"checkbox",disabled:r.vgtDisabled},domProps:{checked:r.vgtSelected}})]):e._e(),e._v(" "),e._l(e.columns,(function(t,o){return!t.hidden&&t.field?n("td",{key:o,class:e.getClasses(o,"td",r),attrs:{"data-label":e.compactMode?t.label:void 0},on:{click:function(n){return e.onCellClicked(r,t,a,n)}}},[e._t("table-row",[t.html?n("span",{domProps:{innerHTML:e._s(e.collect(r,t.field))}}):n("span",[e._v("\n                  "+e._s(e.collectFormatted(r,t))+"\n                ")])],{row:r,column:t,formattedRow:e.formattedRow(r),index:a})],2):e._e()}))],2):e._e()})),e._v(" "),e.groupHeaderOnBottom?n("vgt-header-row",{attrs:{"header-row":t,columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"select-all-by-group":e.selectAllByGroup,"collect-formatted":e.collectFormatted,"formatted-row":e.formattedRow,"get-classes":e.getClasses,"full-colspan":e.fullColspan,groupIndex:e.index},on:{"on-select-group-change":function(n){return e.toggleSelectGroup(n,t)}},scopedSlots:e._u([{key:"table-header-row",fn:function(t){return e.hasHeaderRowTemplate?[e._t("table-header-row",null,{column:t.column,formattedRow:t.formattedRow,row:t.row})]:void 0}}],null,!0)}):e._e()],2)})),e._v(" "),e.showEmptySlot?n("tbody",[n("tr",[n("td",{attrs:{colspan:e.fullColspan}},[e._t("emptystate",[n("div",{staticClass:"vgt-center-align vgt-text-disabled"},[e._v("\n                  No data for table\n                ")])])],2)])]):e._e()],2)]),e._v(" "),e.hasFooterSlot?n("div",{staticClass:"vgt-wrap__actions-footer"},[e._t("table-actions-bottom")],2):e._e(),e._v(" "),e.paginate&&e.paginateOnBottom?e._t("pagination-bottom",[n("vgt-pagination",{ref:"paginationBottom",attrs:{perPage:e.perPage,rtl:e.rtl,total:e.totalRows||e.totalRowCount,mode:e.paginationMode,jumpFirstOrLast:e.paginationOptions.jumpFirstOrLast,firstText:e.firstText,lastText:e.lastText,nextText:e.nextText,prevText:e.prevText,rowsPerPageText:e.rowsPerPageText,perPageDropdownEnabled:e.paginationOptions.perPageDropdownEnabled,customRowsPerPageDropdown:e.customRowsPerPageDropdown,paginateDropdownAllowAll:e.paginateDropdownAllowAll,ofText:e.ofText,pageText:e.pageText,allText:e.allText,"info-fn":e.paginationInfoFn},on:{"page-changed":e.pageChanged,"per-page-changed":e.perPageChanged}})],{pageChanged:e.pageChanged,perPageChanged:e.perPageChanged,total:e.totalRows||e.totalRowCount}):e._e()],2)])},staticRenderFns:[]},void 0,{name:"vue-good-table",props:{isLoading:{default:null,type:Boolean},maxHeight:{default:null,type:String},fixedHeader:Boolean,theme:{default:""},mode:{default:"local"},totalRows:{},styleClass:{default:"vgt-table bordered"},columns:{},rows:{},lineNumbers:Boolean,responsive:{default:!0,type:Boolean},rtl:Boolean,rowStyleClass:{default:null,type:[Function,String]},compactMode:Boolean,groupOptions:{default:function(){return{enabled:!1,collapsable:!1,rowKey:null}}},selectOptions:{default:function(){return{enabled:!1,selectionInfoClass:"",selectionText:"rows selected",clearSelectionText:"clear",disableSelectInfo:!1,selectAllByGroup:!1}}},sortOptions:{default:function(){return{enabled:!0,multipleColumns:!0,initialSortBy:{}}}},paginationOptions:{default:function(){var e;return a(e={enabled:!1,position:"bottom",perPage:10,perPageDropdown:null,perPageDropdownEnabled:!0},"position","bottom"),a(e,"dropdownAllowAll",!0),a(e,"mode","records"),a(e,"infoFn",null),a(e,"jumpFirstOrLast",!1),e}},searchOptions:{default:function(){return{enabled:!1,trigger:null,externalQuery:null,searchFn:null,placeholder:"Search Table"}}}},data:function(){return{tableLoading:!1,firstText:"First",lastText:"Last",nextText:"Next",prevText:"Previous",rowsPerPageText:"Rows per page",ofText:"of",allText:"All",pageText:"page",selectable:!1,selectOnCheckboxOnly:!1,selectAllByPage:!0,disableSelectInfo:!1,selectionInfoClass:"",selectionText:"rows selected",clearSelectionText:"clear",maintainExpanded:!0,expandedRowKeys:new Set,sortable:!0,defaultSortBy:null,multipleColumnSort:!0,searchEnabled:!1,searchTrigger:null,externalSearchQuery:null,searchFn:null,searchPlaceholder:"Search Table",searchSkipDiacritics:!1,perPage:null,paginate:!1,paginateOnTop:!1,paginateOnBottom:!0,customRowsPerPageDropdown:[],paginateDropdownAllowAll:!0,paginationMode:"records",paginationInfoFn:null,currentPage:1,currentPerPage:10,sorts:[],globalSearchTerm:"",filteredRows:[],columnFilters:{},forceSearch:!1,sortChanged:!1,dataTypes:Mt||{}}},watch:{rows:{handler:function(){this.$emit("update:isLoading",!1),this.filterRows(this.columnFilters,!1)},deep:!0,immediate:!0},selectOptions:{handler:function(){this.initializeSelect()},deep:!0,immediate:!0},paginationOptions:{handler:function(e,t){p(e,t)||this.initializePagination()},deep:!0,immediate:!0},searchOptions:{handler:function(){void 0!==this.searchOptions.externalQuery&&this.searchOptions.externalQuery!==this.searchTerm&&(this.externalSearchQuery=this.searchOptions.externalQuery,this.handleSearch()),this.initializeSearch()},deep:!0,immediate:!0},sortOptions:{handler:function(e,t){p(e,t)||this.initializeSort()},deep:!0},selectedRows:function(e,t){p(e,t)||this.$emit("on-selected-rows-change",{selectedRows:this.selectedRows})}},computed:{tableStyles:function(){return this.compactMode?this.tableStyleClasses+"vgt-compact":this.tableStyleClasses},hasFooterSlot:function(){return!!this.$slots["table-actions-bottom"]},wrapperStyles:function(){return{overflow:"scroll-y",maxHeight:this.maxHeight?this.maxHeight:"auto"}},rowKeyField:function(){return this.groupOptions.rowKey||"vgt_header_id"},hasHeaderRowTemplate:function(){return!!this.$slots["table-header-row"]||!!this.$scopedSlots["table-header-row"]},showEmptySlot:function(){return!this.paginated.length||"no groups"===this.paginated[0].label&&!this.paginated[0].children.length},allSelected:function(){return this.selectedRowCount>0&&(this.selectAllByPage&&this.selectedPageRowsCount===this.totalPageRowCount||!this.selectAllByPage&&this.selectedRowCount===this.totalRowCount)},allSelectedIndeterminate:function(){return!this.allSelected&&(this.selectAllByPage&&this.selectedPageRowsCount>0||!this.selectAllByPage&&this.selectedRowCount>0)},selectionInfo:function(){return"".concat(this.selectedRowCount," ").concat(this.selectionText)},selectedRowCount:function(){return this.selectedRows.length},selectedPageRowsCount:function(){return this.selectedPageRows.length},selectedPageRows:function(){var e=[];return this.paginated.forEach((function(t){t.children.forEach((function(t){t.vgtSelected&&e.push(t)}))})),e},selectedRows:function(){var e=[];return this.processedRows.forEach((function(t){t.children.forEach((function(t){t.vgtSelected&&e.push(t)}))})),e.sort((function(e,t){return e.originalIndex-t.originalIndex}))},fullColspan:function(){for(var e=0,t=0;t<this.columns.length;t+=1)this.columns[t].hidden||(e+=1);return this.lineNumbers&&e++,this.selectable&&e++,e},groupHeaderOnTop:function(){return!(this.groupOptions&&this.groupOptions.enabled&&this.groupOptions.headerPosition&&"bottom"===this.groupOptions.headerPosition)&&!(!this.groupOptions||!this.groupOptions.enabled)},groupHeaderOnBottom:function(){return!!(this.groupOptions&&this.groupOptions.enabled&&this.groupOptions.headerPosition&&"bottom"===this.groupOptions.headerPosition)},totalRowCount:function(){return this.processedRows.reduce((function(e,t){return e+(t.children?t.children.length:0)}),0)},totalPageRowCount:function(){return this.paginated.reduce((function(e,t){return e+(t.children?t.children.length:0)}),0)},wrapStyleClasses:function(){var e="vgt-wrap";return this.rtl&&(e+=" rtl"),e+=" ".concat(this.theme)},tableStyleClasses:function(){var e=this.styleClass;return e+=" ".concat(this.theme)},searchTerm:function(){return null!=this.externalSearchQuery?this.externalSearchQuery:this.globalSearchTerm},globalSearchAllowed:function(){return!(!this.searchEnabled||!this.globalSearchTerm||"enter"===this.searchTrigger)||(null!=this.externalSearchQuery&&"enter"!==this.searchTrigger||!!this.forceSearch&&(this.forceSearch=!1,!0))},processedRows:function(){var e=this,t=this.filteredRows;if("remote"===this.mode)return t;if(this.globalSearchAllowed){var n=[];this.filteredRows.forEach((function(e){n.push.apply(n,i(e.children))}));var r=[];n.forEach((function(t){for(var n=0;n<e.columns.length;n+=1){var a=e.columns[n];if(!a.globalSearchDisabled)if(e.searchFn){if(e.searchFn(t,a,e.collectFormatted(t,a),e.searchTerm)){r.push(t);break}}else if(_.filterPredicate(e.collectFormatted(t,a),e.searchTerm,e.searchSkipDiacritics)){r.push(t);break}}})),this.$emit("on-search",{searchTerm:this.searchTerm,rowCount:r.length}),t=[],this.filteredRows.forEach((function(e){var n=e.vgt_header_id,a=r.filter((function(e){return e.vgt_id===n}));if(a.length){var o=JSON.parse(JSON.stringify(e));o.children=a,t.push(o)}}))}return this.sorts.length&&t.forEach((function(t){t.children.sort((function(t,n){for(var r,a=0;a<e.sorts.length;a+=1){var o=e.sorts[a];if(o.type===d)r=r||t.originalIndex-n.originalIndex;else{var i=e.getColumnForField(o.field),s=e.collect(t,o.field),l=e.collect(n,o.field),u=i.sortFn;r=u&&"function"==typeof u?r||u(s,l,i,t,n)*(o.type===c?-1:1):r||i.typeDef.compare(s,l,i)*(o.type===c?-1:1)}}return r}))})),"enter"===this.searchTrigger&&(this.filteredRows=t),t},paginated:function(){var e=this;if(!this.processedRows.length)return[];if("remote"===this.mode)return this.processedRows;var t=[];if(this.processedRows.forEach((function(n){var r;e.groupOptions.enabled&&t.push(n),(r=t).push.apply(r,i(n.children))})),this.paginate){var n=(this.currentPage-1)*this.currentPerPage;(n>=t.length||-1===this.currentPerPage)&&(this.currentPage=1,n=0);var r=t.length+1;-1!==this.currentPerPage&&(r=this.currentPage*this.currentPerPage),t=t.slice(n,r)}var a=[];return t.forEach((function(t){if(void 0!==t.vgt_header_id){e.handleExpanded(t);var n=JSON.parse(JSON.stringify(t));n.children=[],a.push(n)}else{var r=a.find((function(e){return e.vgt_header_id===t.vgt_id}));r||(r=e.processedRows.find((function(e){return e.vgt_header_id===t.vgt_id})))&&((r=JSON.parse(JSON.stringify(r))).children=[],a.push(r)),r.children.push(t)}})),a},originalRows:function(){var e=this.rows&&this.rows.length?JSON.parse(JSON.stringify(this.rows)):[],t=[];t=this.groupOptions.enabled?this.handleGrouped(e):this.handleGrouped([{label:"no groups",children:e}]);var n=0;return t.forEach((function(e){e.children.forEach((function(e){e.originalIndex=n++}))})),t},typedColumns:function(){for(var e=this.columns,t=0;t<this.columns.length;t++){var n=e[t];n.typeDef=this.dataTypes[n.type]||_}return e},hasRowClickListener:function(){return this.$listeners&&this.$listeners["on-row-click"]}},methods:{handleExpanded:function(e){this.maintainExpanded&&this.expandedRowKeys.has(e[this.rowKeyField])?this.$set(e,"vgtIsExpanded",!0):this.$set(e,"vgtIsExpanded",!1)},toggleExpand:function(e){var t=this,n=this.filteredRows.find((function(n){return n[t.rowKeyField]===e}));n&&this.$set(n,"vgtIsExpanded",!n.vgtIsExpanded),this.maintainExpanded&&n.vgtIsExpanded?this.expandedRowKeys.add(n[this.rowKeyField]):this.expandedRowKeys.delete(n[this.rowKeyField])},expandAll:function(){var e=this;this.filteredRows.forEach((function(t){e.$set(t,"vgtIsExpanded",!0),e.maintainExpanded&&e.expandedRowKeys.add(t[e.rowKeyField])}))},collapseAll:function(){var e=this;this.filteredRows.forEach((function(t){e.$set(t,"vgtIsExpanded",!1),e.expandedRowKeys.clear()}))},getColumnForField:function(e){for(var t=0;t<this.typedColumns.length;t+=1)if(this.typedColumns[t].field===e)return this.typedColumns[t]},handleSearch:function(){this.resetTable(),"remote"===this.mode&&this.$emit("on-search",{searchTerm:this.searchTerm})},reset:function(){this.initializeSort(),this.changePage(1),this.$refs["table-header-primary"].reset(!0),this.$refs["table-header-secondary"]&&this.$refs["table-header-secondary"].reset(!0)},emitSelectedRows:function(){this.$emit("on-select-all",{selected:this.selectedRowCount===this.totalRowCount,selectedRows:this.selectedRows})},unselectAllInternal:function(e){var t=this;(this.selectAllByPage&&!e?this.paginated:this.filteredRows).forEach((function(e,n){e.children.forEach((function(e,n){t.$set(e,"vgtSelected",!1)}))})),this.emitSelectedRows()},toggleSelectAll:function(){var e=this;this.allSelected?this.unselectAllInternal():((this.selectAllByPage?this.paginated:this.filteredRows).forEach((function(t){t.children.forEach((function(t){e.$set(t,"vgtSelected",!0)}))})),this.emitSelectedRows())},toggleSelectGroup:function(e,t){var n=this;t.children.forEach((function(t){n.$set(t,"vgtSelected",e.checked)}))},changePage:function(e){var t=this.paginate,n=this.$refs,r=n.paginationBottom,a=n.paginationTop;t&&(this.paginateOnTop&&a&&(a.currentPage=e),this.paginateOnBottom&&r&&(r.currentPage=e),this.currentPage=e)},pageChangedEvent:function(){return{currentPage:this.currentPage,currentPerPage:this.currentPerPage,total:Math.floor(this.totalRowCount/this.currentPerPage)}},pageChanged:function(e){if(this.currentPage=e.currentPage,!e.noEmit){var t=this.pageChangedEvent();t.prevPage=e.prevPage,this.$emit("on-page-change",t),"remote"===this.mode&&this.$emit("update:isLoading",!0)}},perPageChanged:function(e){this.currentPerPage=e.currentPerPage;var t=this.paginationOptions.position;!this.$refs.paginationTop||"top"!==t&&"both"!==t||(this.$refs.paginationTop.currentPerPage=this.currentPerPage),!this.$refs.paginationBottom||"bottom"!==t&&"both"!==t||(this.$refs.paginationBottom.currentPerPage=this.currentPerPage);var n=this.pageChangedEvent();this.$emit("on-per-page-change",n),"remote"===this.mode&&this.$emit("update:isLoading",!0)},changeSort:function(e){this.sorts=e,this.$emit("on-sort-change",e),this.changePage(1),"remote"!==this.mode?this.sortChanged=!0:this.$emit("update:isLoading",!0)},onCheckboxClicked:function(e,t,n){this.$set(e,"vgtSelected",!e.vgtSelected),this.$emit("on-row-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onRowDoubleClicked:function(e,t,n){this.$emit("on-row-dblclick",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onRowClicked:function(e,t,n){this.selectable&&!this.selectOnCheckboxOnly&&this.$set(e,"vgtSelected",!e.vgtSelected),this.$emit("on-row-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onRowAuxClicked:function(e,t,n){this.$emit("on-row-aux-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onCellClicked:function(e,t,n,r){this.$emit("on-cell-click",{row:e,column:t,rowIndex:n,event:r})},onMouseenter:function(e,t){this.$emit("on-row-mouseenter",{row:e,pageIndex:t})},onMouseleave:function(e,t){this.$emit("on-row-mouseleave",{row:e,pageIndex:t})},searchTableOnEnter:function(){"enter"===this.searchTrigger&&(this.handleSearch(),this.filteredRows=JSON.parse(JSON.stringify(this.originalRows)),this.forceSearch=!0,this.sortChanged=!0)},searchTableOnKeyUp:function(){"enter"!==this.searchTrigger&&this.handleSearch()},resetTable:function(){this.unselectAllInternal(!0),this.changePage(1)},collect:function(e,t){return"function"==typeof t?t(e):"string"==typeof t?function(e,t){for(var n=e,r=t.split("."),a=0;a<r.length;a++){if(null==n)return;n=n[r[a]]}return n}(e,t):void 0},collectFormatted:function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(void 0===(n=r&&t.headerField?this.collect(e,t.headerField):this.collect(e,t.field)))return"";if(t.formatFn&&"function"==typeof t.formatFn)return t.formatFn(n,e);var a=t.typeDef;a||(a=this.dataTypes[t.type]||_);var o=a.format(n,t);return!this.compactMode||""!=o&&null!=o?o:"-"},formattedRow:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n={},r=0;r<this.typedColumns.length;r++){var a=this.typedColumns[r];a.field&&(n[a.field]=this.collectFormatted(e,a,t))}return n},getClasses:function(e,t,n){var r=this.typedColumns[e],a=r.typeDef,o=r["".concat(t,"Class")],i=a.isRight;this.rtl&&(i=!0);var s={"vgt-right-align":i,"vgt-left-align":!i};return"function"==typeof o?s[o(n)]=!0:"string"==typeof o&&(s[o]=!0),s},filterRows:function(e){var t=this,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.columnFilters=e;var a=JSON.parse(JSON.stringify(this.originalRows)),o=!1;if(this.columnFilters&&Object.keys(this.columnFilters).length){var i=function(){if(("remote"!==t.mode||n)&&t.changePage(1),n&&t.$emit("on-column-filter",{columnFilters:t.columnFilters}),"remote"===t.mode)return n?t.$emit("update:isLoading",!0):t.filteredRows=a,{v:void 0};for(var e=function(e){return"function"==typeof e&&e.name?e.name:e},i=function(n){var i=t.typedColumns[n];t.columnFilters[e(i.field)]&&(o=!0,a.forEach((function(n){var a=n.children.filter((function(n){return i.filterOptions&&"function"==typeof i.filterOptions.filterFn?i.filterOptions.filterFn(t.collect(n,i.field),t.columnFilters[e(i.field)]):i.typeDef.filterPredicate(t.collect(n,i.field),t.columnFilters[e(i.field)],!1,i.filterOptions&&"object"===r(i.filterOptions.filterDropdownItems))}));n.children=a})))},s=0;s<t.typedColumns.length;s++)i(s)}();if("object"===r(i))return i.v}this.filteredRows=o?a.filter((function(e){return e.children&&e.children.length})):a},getCurrentIndex:function(e){for(var t=0,n=!1,r=0;r<this.paginated.length;r+=1){var a=this.paginated[r].children;if(a&&a.length)for(var o=0;o<a.length;o+=1){if(a[o].originalIndex===e){n=!0;break}t+=1}if(n)break}return(this.currentPage-1)*this.currentPerPage+t+1},getRowStyleClass:function(e){var t,n="";return this.hasRowClickListener&&(n+="clickable"),(t="function"==typeof this.rowStyleClass?this.rowStyleClass(e):this.rowStyleClass)&&(n+=" ".concat(t)),n},handleGrouped:function(e){var t=this;return e.forEach((function(e,n){e.vgt_header_id=n,t.groupOptions.maintainExpanded&&t.expandedRowKeys.has(e[t.groupOptions.rowKey])&&t.$set(e,"vgtIsExpanded",!0),e.children.forEach((function(e){e.vgt_id=n}))})),e},initializePagination:function(){var e=this,t=this.paginationOptions,n=t.enabled,r=t.perPage,a=t.position,i=t.perPageDropdown,s=t.perPageDropdownEnabled,l=t.dropdownAllowAll,u=t.firstLabel,c=t.lastLabel,d=t.nextLabel,h=t.prevLabel,f=t.rowsPerPageLabel,g=t.ofLabel,p=t.pageLabel,m=t.allLabel,v=t.setCurrentPage,w=t.mode,b=t.infoFn;if("boolean"==typeof n&&(this.paginate=n),"number"==typeof r&&(this.perPage=r),"top"===a?(this.paginateOnTop=!0,this.paginateOnBottom=!1):"both"===a&&(this.paginateOnTop=!0,this.paginateOnBottom=!0),Array.isArray(i)&&i.length&&(this.customRowsPerPageDropdown=i,!this.perPage)){var y=o(i,1);this.perPage=y[0]}"boolean"==typeof s&&(this.perPageDropdownEnabled=s),"boolean"==typeof l&&(this.paginateDropdownAllowAll=l),"string"==typeof w&&(this.paginationMode=w),"string"==typeof u&&(this.firstText=u),"string"==typeof c&&(this.lastText=c),"string"==typeof d&&(this.nextText=d),"string"==typeof h&&(this.prevText=h),"string"==typeof f&&(this.rowsPerPageText=f),"string"==typeof g&&(this.ofText=g),"string"==typeof p&&(this.pageText=p),"string"==typeof m&&(this.allText=m),"number"==typeof v&&setTimeout((function(){e.changePage(v)}),500),"function"==typeof b&&(this.paginationInfoFn=b)},initializeSearch:function(){var e=this.searchOptions,t=e.enabled,n=e.trigger,r=e.externalQuery,a=e.searchFn,o=e.placeholder,i=e.skipDiacritics;"boolean"==typeof t&&(this.searchEnabled=t),"enter"===n&&(this.searchTrigger=n),"string"==typeof r&&(this.externalSearchQuery=r),"function"==typeof a&&(this.searchFn=a),"string"==typeof o&&(this.searchPlaceholder=o),"boolean"==typeof i&&(this.searchSkipDiacritics=i)},initializeSort:function(){var e=this.sortOptions,t=e.enabled,n=e.initialSortBy,a=e.multipleColumns,o=JSON.parse(JSON.stringify(n||{}));if("boolean"==typeof t&&(this.sortable=t),"boolean"==typeof a&&(this.multipleColumnSort=a),"object"===r(o)){var i=this.fixedHeader?this.$refs["table-header-secondary"]:this.$refs["table-header-primary"];if(Array.isArray(o))i.setInitialSort(o);else Object.prototype.hasOwnProperty.call(o,"field")&&i.setInitialSort([o])}},initializeSelect:function(){var e=this.selectOptions,t=e.enabled,n=e.selectionInfoClass,r=e.selectionText,a=e.clearSelectionText,o=e.selectOnCheckboxOnly,i=e.selectAllByPage,s=e.disableSelectInfo,l=e.selectAllByGroup;"boolean"==typeof t&&(this.selectable=t),"boolean"==typeof o&&(this.selectOnCheckboxOnly=o),"boolean"==typeof i&&(this.selectAllByPage=i),"boolean"==typeof l&&(this.selectAllByGroup=l),"boolean"==typeof s&&(this.disableSelectInfo=s),"string"==typeof n&&(this.selectionInfoClass=n),"string"==typeof r&&(this.selectionText=r),"string"==typeof a&&(this.clearSelectionText=a)}},mounted:function(){this.perPage&&(this.currentPerPage=this.perPage),this.initializeSort()},components:{"vgt-pagination":C,"vgt-global-search":x,"vgt-header-row":O,"vgt-table-header":k}},void 0,!1,void 0,!1,void 0,void 0,void 0),jt={install:function(e,t){e.component(Ut.name,Ut)}};"undefined"!=typeof window&&window.Vue&&window.Vue.use(jt)}).call(this,n("yLpj"))}}]);