<?php
/**
 * تطبيق الكوبون
 * Apply Coupon API
 */

require_once '../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

// التحقق من المصادقة
$user = requireAuth('user');

$data = getRequestData();

// التحقق من البيانات المطلوبة
$requiredFields = ['coupon'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$couponCode = $data['coupon'];
$deviceId = $data['device_id'] ?? null;

// البحث عن الكوبون
$couponSql = "SELECT * FROM coupons WHERE coupon = ? AND active = 1";
$coupon = $db->selectOne($couponSql, [$couponCode]);

if (!$coupon) {
    errorResponse('الكوبون غير موجود أو غير نشط', 404);
}

// التحقق من انتهاء صلاحية الكوبون
if ($coupon['expire_date'] && strtotime($coupon['expire_date']) < time()) {
    errorResponse('انتهت صلاحية الكوبون', 400);
}

// التحقق من استخدام الكوبون مسبقاً
$usedCouponSql = "SELECT id FROM used_coupons WHERE c_id = ? AND u_id = ?";
$usedCoupon = $db->selectOne($usedCouponSql, [$coupon['id'], $user['id']]);

if ($usedCoupon) {
    errorResponse('تم استخدام هذا الكوبون من قبل', 400);
}

// التحقق من البلد إذا كان الكوبون مخصص لبلد معين
if ($coupon['country'] && $coupon['country'] !== $user['country']) {
    errorResponse('هذا الكوبون غير متاح في بلدك', 400);
}

$db->beginTransaction();

try {
    // تسجيل استخدام الكوبون
    $insertUsedCouponSql = "INSERT INTO used_coupons (c_id, u_id, u_name, device_id, date) 
                           VALUES (?, ?, ?, ?, CURDATE())";
    $db->insert($insertUsedCouponSql, [$coupon['id'], $user['id'], $user['name'], $deviceId]);
    
    // التحقق من نوع الكوبون
    if ($coupon['days'] && $coupon['days'] > 0) {
        // كوبون عادي - إضافة اشتراك
        $days = (int)$coupon['days'];
        $startDate = date('Y-m-d');
        $expireDate = date('Y-m-d', strtotime("+{$days} days"));
        
        // البحث عن مسوق الكوبون
        $marketerSql = "SELECT * FROM marketers WHERE coupon = ? AND active = 1";
        $marketer = $db->selectOne($marketerSql, [$couponCode]);
        
        $marketerName = $marketer ? $marketer['full_name'] : null;
        
        // إضافة الاشتراك
        $insertSubscriptionSql = "INSERT INTO subscriptions (user_id, amount, start_date, expire_date, marketer_name, pay_method, active) 
                                 VALUES (?, ?, ?, ?, ?, 'coupon', 1)";
        $subscriptionId = $db->insert($insertSubscriptionSql, [
            $user['id'], 
            0, // مجاني عبر الكوبون
            $startDate, 
            $expireDate, 
            $marketerName
        ]);
        
        // إذا كان هناك مسوق، سجل عمولة الاشتراك
        if ($marketer && $marketer['sub_commission']) {
            $insertMarketerDetailsSql = "INSERT INTO marketer_details (marketer_id, user_id, name, email, country, type, amount, date) 
                                        VALUES (?, ?, ?, ?, ?, 'subscription', ?, CURDATE())";
            $db->insert($insertMarketerDetailsSql, [
                $marketer['id'],
                $user['id'],
                $user['name'],
                $user['email'],
                $user['country'],
                $marketer['sub_commission']
            ]);
        }
        
        $db->commit();
        
        successResponse([
            'type' => 'subscription',
            'subscription_id' => $subscriptionId,
            'days' => $days,
            'expire_date' => $expireDate,
            'formatted_expire_date' => formatArabicDate($expireDate),
            'marketer_name' => $marketerName
        ], 'تم تطبيق الكوبون بنجاح وإضافة الاشتراك');
        
    } else {
        // كوبون مسوق - تسجيل عمولة فقط
        $marketerSql = "SELECT * FROM marketers WHERE coupon = ? AND active = 1";
        $marketer = $db->selectOne($marketerSql, [$couponCode]);
        
        if ($marketer && $marketer['reg_commission']) {
            $insertMarketerDetailsSql = "INSERT INTO marketer_details (marketer_id, user_id, name, email, country, type, amount, date) 
                                        VALUES (?, ?, ?, ?, ?, 'coupon_use', ?, CURDATE())";
            $db->insert($insertMarketerDetailsSql, [
                $marketer['id'],
                $user['id'],
                $user['name'],
                $user['email'],
                $user['country'],
                $marketer['reg_commission']
            ]);
        }
        
        $db->commit();
        
        successResponse([
            'type' => 'marketer',
            'marketer_name' => $marketer ? $marketer['full_name'] : null
        ], 'تم تطبيق الكوبون بنجاح');
    }
    
} catch (Exception $e) {
    $db->rollback();
    errorResponse('حدث خطأ أثناء تطبيق الكوبون: ' . $e->getMessage());
}
