<?php
/**
 * تسجيل دخول المستخدمين
 * User Login API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$data = getRequestData();

// التحقق من البيانات المطلوبة
$requiredFields = ['email', 'password'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$email = $data['email'];
$password = $data['password'];

// التحقق من صحة البريد الإلكتروني
if (!validateEmail($email)) {
    errorResponse('البريد الإلكتروني غير صحيح');
}

// محاولة تسجيل الدخول
$result = $auth->loginUser($email, $password);

if ($result['success']) {
    // إضافة بيانات الاشتراك إذا كانت موجودة
    $user = $result['user'];
    
    // التحقق من الاشتراك النشط
    $subscriptionSql = "SELECT * FROM subscriptions WHERE user_id = ? AND active = 1 AND expire_date >= CURDATE()";
    $subscription = $db->selectOne($subscriptionSql, [$user['id']]);
    
    if ($subscription) {
        $user['subscription'] = $subscription;
    }
    
    successResponse($user, 'تم تسجيل الدخول بنجاح');
} else {
    errorResponse($result['message'], 404);
}
