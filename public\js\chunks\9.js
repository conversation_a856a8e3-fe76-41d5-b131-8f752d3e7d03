/*! For license information please see 9.js.LICENSE.txt */
(window.webpackJsonp=window.webpackJsonp||[]).push([[9,7],{"+s0g":function(e,t,a){!function(e){"use strict";var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),a="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),n=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],r=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i;e.defineLocale("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,n){return e?/-MMM-/.test(n)?a[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",w:"één week",ww:"%d weken",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(a("wd/R"))},"//9w":function(e,t,a){!function(e){"use strict";e.defineLocale("se",{months:"ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu".split("_"),monthsShort:"ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov".split("_"),weekdays:"sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat".split("_"),weekdaysShort:"sotn_vuos_maŋ_gask_duor_bear_láv".split("_"),weekdaysMin:"s_v_m_g_d_b_L".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"MMMM D. [b.] YYYY",LLL:"MMMM D. [b.] YYYY [ti.] HH:mm",LLLL:"dddd, MMMM D. [b.] YYYY [ti.] HH:mm"},calendar:{sameDay:"[otne ti] LT",nextDay:"[ihttin ti] LT",nextWeek:"dddd [ti] LT",lastDay:"[ikte ti] LT",lastWeek:"[ovddit] dddd [ti] LT",sameElse:"L"},relativeTime:{future:"%s geažes",past:"maŋit %s",s:"moadde sekunddat",ss:"%d sekunddat",m:"okta minuhta",mm:"%d minuhtat",h:"okta diimmu",hh:"%d diimmut",d:"okta beaivi",dd:"%d beaivvit",M:"okta mánnu",MM:"%d mánut",y:"okta jahki",yy:"%d jagit"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},"/X5v":function(e,t,a){!function(e){"use strict";e.defineLocale("x-pseudo",{months:"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér".split("_"),monthsShort:"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc".split("_"),monthsParseExact:!0,weekdays:"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý".split("_"),weekdaysShort:"S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát".split("_"),weekdaysMin:"S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[T~ódá~ý át] LT",nextDay:"[T~ómó~rró~w át] LT",nextWeek:"dddd [át] LT",lastDay:"[Ý~ést~érdá~ý át] LT",lastWeek:"[L~ást] dddd [át] LT",sameElse:"L"},relativeTime:{future:"í~ñ %s",past:"%s á~gó",s:"á ~féw ~sécó~ñds",ss:"%d s~écóñ~ds",m:"á ~míñ~úté",mm:"%d m~íñú~tés",h:"á~ñ hó~úr",hh:"%d h~óúrs",d:"á ~dáý",dd:"%d d~áýs",M:"á ~móñ~th",MM:"%d m~óñt~hs",y:"á ~ýéár",yy:"%d ý~éárs"},dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(a("wd/R"))},"0mo+":function(e,t,a){!function(e){"use strict";var t={1:"༡",2:"༢",3:"༣",4:"༤",5:"༥",6:"༦",7:"༧",8:"༨",9:"༩",0:"༠"},a={"༡":"1","༢":"2","༣":"3","༤":"4","༥":"5","༦":"6","༧":"7","༨":"8","༩":"9","༠":"0"};e.defineLocale("bo",{months:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),monthsShort:"ཟླ་1_ཟླ་2_ཟླ་3_ཟླ་4_ཟླ་5_ཟླ་6_ཟླ་7_ཟླ་8_ཟླ་9_ཟླ་10_ཟླ་11_ཟླ་12".split("_"),monthsShortRegex:/^(ཟླ་\d{1,2})/,monthsParseExact:!0,weekdays:"གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་".split("_"),weekdaysShort:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),weekdaysMin:"ཉི_ཟླ_མིག_ལྷག_ཕུར_སངས_སྤེན".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[དི་རིང] LT",nextDay:"[སང་ཉིན] LT",nextWeek:"[བདུན་ཕྲག་རྗེས་མ], LT",lastDay:"[ཁ་སང] LT",lastWeek:"[བདུན་ཕྲག་མཐའ་མ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ལ་",past:"%s སྔན་ལ",s:"ལམ་སང",ss:"%d སྐར་ཆ།",m:"སྐར་མ་གཅིག",mm:"%d སྐར་མ",h:"ཆུ་ཚོད་གཅིག",hh:"%d ཆུ་ཚོད",d:"ཉིན་གཅིག",dd:"%d ཉིན་",M:"ཟླ་བ་གཅིག",MM:"%d ཟླ་བ",y:"ལོ་གཅིག",yy:"%d ལོ"},preparse:function(e){return e.replace(/[༡༢༣༤༥༦༧༨༩༠]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,meridiemHour:function(e,t){return 12===e&&(e=0),"མཚན་མོ"===t&&e>=4||"ཉིན་གུང"===t&&e<5||"དགོང་དག"===t?e+12:e},meridiem:function(e,t,a){return e<4?"མཚན་མོ":e<10?"ཞོགས་ཀས":e<17?"ཉིན་གུང":e<20?"དགོང་དག":"མཚན་མོ"},week:{dow:0,doy:6}})}(a("wd/R"))},"0tRk":function(e,t,a){!function(e){"use strict";e.defineLocale("pt-br",{months:"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_sáb".split("_"),weekdaysMin:"do_2ª_3ª_4ª_5ª_6ª_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [às] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [às] HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",invalidDate:"Data inválida"})}(a("wd/R"))},"1ppg":function(e,t,a){!function(e){"use strict";e.defineLocale("fil",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}})}(a("wd/R"))},"1rYy":function(e,t,a){!function(e){"use strict";e.defineLocale("hy-am",{months:{format:"հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի".split("_"),standalone:"հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր".split("_")},monthsShort:"հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ".split("_"),weekdays:"կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ".split("_"),weekdaysShort:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),weekdaysMin:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY թ.",LLL:"D MMMM YYYY թ., HH:mm",LLLL:"dddd, D MMMM YYYY թ., HH:mm"},calendar:{sameDay:"[այսօր] LT",nextDay:"[վաղը] LT",lastDay:"[երեկ] LT",nextWeek:function(){return"dddd [օրը ժամը] LT"},lastWeek:function(){return"[անցած] dddd [օրը ժամը] LT"},sameElse:"L"},relativeTime:{future:"%s հետո",past:"%s առաջ",s:"մի քանի վայրկյան",ss:"%d վայրկյան",m:"րոպե",mm:"%d րոպե",h:"ժամ",hh:"%d ժամ",d:"օր",dd:"%d օր",M:"ամիս",MM:"%d ամիս",y:"տարի",yy:"%d տարի"},meridiemParse:/գիշերվա|առավոտվա|ցերեկվա|երեկոյան/,isPM:function(e){return/^(ցերեկվա|երեկոյան)$/.test(e)},meridiem:function(e){return e<4?"գիշերվա":e<12?"առավոտվա":e<17?"ցերեկվա":"երեկոյան"},dayOfMonthOrdinalParse:/\d{1,2}|\d{1,2}-(ին|րդ)/,ordinal:function(e,t){switch(t){case"DDD":case"w":case"W":case"DDDo":return 1===e?e+"-ին":e+"-րդ";default:return e}},week:{dow:1,doy:7}})}(a("wd/R"))},"1xZ4":function(e,t,a){!function(e){"use strict";e.defineLocale("ca",{months:{standalone:"gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre".split("_"),format:"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.".split("_"),monthsParseExact:!0,weekdays:"diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dt._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dt_dc_dj_dv_ds".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a les] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a les] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:function(){return"[avui a "+(1!==this.hours()?"les":"la")+"] LT"},nextDay:function(){return"[demà a "+(1!==this.hours()?"les":"la")+"] LT"},nextWeek:function(){return"dddd [a "+(1!==this.hours()?"les":"la")+"] LT"},lastDay:function(){return"[ahir a "+(1!==this.hours()?"les":"la")+"] LT"},lastWeek:function(){return"[el] dddd [passat a "+(1!==this.hours()?"les":"la")+"] LT"},sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"uns segons",ss:"%d segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|è|a)/,ordinal:function(e,t){var a=1===e?"r":2===e?"n":3===e?"r":4===e?"t":"è";return"w"!==t&&"W"!==t||(a="a"),e+a},week:{dow:1,doy:4}})}(a("wd/R"))},"2B1R":function(e,t,a){"use strict";var n=a("I+eb"),r=a("tycR").map,s=a("Hd5f"),i=a("rkAj"),o=s("map"),d=i("map");n({target:"Array",proto:!0,forced:!o||!d},{map:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}})},"2fjn":function(e,t,a){!function(e){"use strict";e.defineLocale("fr-ca",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}}})}(a("wd/R"))},"2ykv":function(e,t,a){!function(e){"use strict";var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),a="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),n=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],r=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i;e.defineLocale("nl-be",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,n){return e?/-MMM-/.test(n)?a[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(a("wd/R"))},"3E1r":function(e,t,a){!function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},a={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"},n=[/^जन/i,/^फ़र|फर/i,/^मार्च/i,/^अप्रै/i,/^मई/i,/^जून/i,/^जुल/i,/^अग/i,/^सितं|सित/i,/^अक्टू/i,/^नव|नवं/i,/^दिसं|दिस/i];e.defineLocale("hi",{months:{format:"जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर".split("_"),standalone:"जनवरी_फरवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितंबर_अक्टूबर_नवंबर_दिसंबर".split("_")},monthsShort:"जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.".split("_"),weekdays:"रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm बजे",LTS:"A h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm बजे",LLLL:"dddd, D MMMM YYYY, A h:mm बजे"},monthsParse:n,longMonthsParse:n,shortMonthsParse:[/^जन/i,/^फ़र/i,/^मार्च/i,/^अप्रै/i,/^मई/i,/^जून/i,/^जुल/i,/^अग/i,/^सित/i,/^अक्टू/i,/^नव/i,/^दिस/i],monthsRegex:/^(जनवरी|जन\.?|फ़रवरी|फरवरी|फ़र\.?|मार्च?|अप्रैल|अप्रै\.?|मई?|जून?|जुलाई|जुल\.?|अगस्त|अग\.?|सितम्बर|सितंबर|सित\.?|अक्टूबर|अक्टू\.?|नवम्बर|नवंबर|नव\.?|दिसम्बर|दिसंबर|दिस\.?)/i,monthsShortRegex:/^(जनवरी|जन\.?|फ़रवरी|फरवरी|फ़र\.?|मार्च?|अप्रैल|अप्रै\.?|मई?|जून?|जुलाई|जुल\.?|अगस्त|अग\.?|सितम्बर|सितंबर|सित\.?|अक्टूबर|अक्टू\.?|नवम्बर|नवंबर|नव\.?|दिसम्बर|दिसंबर|दिस\.?)/i,monthsStrictRegex:/^(जनवरी?|फ़रवरी|फरवरी?|मार्च?|अप्रैल?|मई?|जून?|जुलाई?|अगस्त?|सितम्बर|सितंबर|सित?\.?|अक्टूबर|अक्टू\.?|नवम्बर|नवंबर?|दिसम्बर|दिसंबर?)/i,monthsShortStrictRegex:/^(जन\.?|फ़र\.?|मार्च?|अप्रै\.?|मई?|जून?|जुल\.?|अग\.?|सित\.?|अक्टू\.?|नव\.?|दिस\.?)/i,calendar:{sameDay:"[आज] LT",nextDay:"[कल] LT",nextWeek:"dddd, LT",lastDay:"[कल] LT",lastWeek:"[पिछले] dddd, LT",sameElse:"L"},relativeTime:{future:"%s में",past:"%s पहले",s:"कुछ ही क्षण",ss:"%d सेकंड",m:"एक मिनट",mm:"%d मिनट",h:"एक घंटा",hh:"%d घंटे",d:"एक दिन",dd:"%d दिन",M:"एक महीने",MM:"%d महीने",y:"एक वर्ष",yy:"%d वर्ष"},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/रात|सुबह|दोपहर|शाम/,meridiemHour:function(e,t){return 12===e&&(e=0),"रात"===t?e<4?e:e+12:"सुबह"===t?e:"दोपहर"===t?e>=10?e:e+12:"शाम"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"रात":e<10?"सुबह":e<17?"दोपहर":e<20?"शाम":"रात"},week:{dow:0,doy:6}})}(a("wd/R"))},"4MV3":function(e,t,a){!function(e){"use strict";var t={1:"૧",2:"૨",3:"૩",4:"૪",5:"૫",6:"૬",7:"૭",8:"૮",9:"૯",0:"૦"},a={"૧":"1","૨":"2","૩":"3","૪":"4","૫":"5","૬":"6","૭":"7","૮":"8","૯":"9","૦":"0"};e.defineLocale("gu",{months:"જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર".split("_"),monthsShort:"જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.".split("_"),monthsParseExact:!0,weekdays:"રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર".split("_"),weekdaysShort:"રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ".split("_"),weekdaysMin:"ર_સો_મં_બુ_ગુ_શુ_શ".split("_"),longDateFormat:{LT:"A h:mm વાગ્યે",LTS:"A h:mm:ss વાગ્યે",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm વાગ્યે",LLLL:"dddd, D MMMM YYYY, A h:mm વાગ્યે"},calendar:{sameDay:"[આજ] LT",nextDay:"[કાલે] LT",nextWeek:"dddd, LT",lastDay:"[ગઇકાલે] LT",lastWeek:"[પાછલા] dddd, LT",sameElse:"L"},relativeTime:{future:"%s મા",past:"%s પહેલા",s:"અમુક પળો",ss:"%d સેકંડ",m:"એક મિનિટ",mm:"%d મિનિટ",h:"એક કલાક",hh:"%d કલાક",d:"એક દિવસ",dd:"%d દિવસ",M:"એક મહિનો",MM:"%d મહિનો",y:"એક વર્ષ",yy:"%d વર્ષ"},preparse:function(e){return e.replace(/[૧૨૩૪૫૬૭૮૯૦]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/રાત|બપોર|સવાર|સાંજ/,meridiemHour:function(e,t){return 12===e&&(e=0),"રાત"===t?e<4?e:e+12:"સવાર"===t?e:"બપોર"===t?e>=10?e:e+12:"સાંજ"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"રાત":e<10?"સવાર":e<17?"બપોર":e<20?"સાંજ":"રાત"},week:{dow:0,doy:6}})}(a("wd/R"))},"4dOw":function(e,t,a){!function(e){"use strict";e.defineLocale("en-ie",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(a("wd/R"))},"6+QB":function(e,t,a){!function(e){"use strict";e.defineLocale("ms",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?e>=11?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,a){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}})}(a("wd/R"))},"6B0Y":function(e,t,a){!function(e){"use strict";var t={1:"១",2:"២",3:"៣",4:"៤",5:"៥",6:"៦",7:"៧",8:"៨",9:"៩",0:"០"},a={"១":"1","២":"2","៣":"3","៤":"4","៥":"5","៦":"6","៧":"7","៨":"8","៩":"9","០":"0"};e.defineLocale("km",{months:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),monthsShort:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),weekdays:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),weekdaysShort:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysMin:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ព្រឹក|ល្ងាច/,isPM:function(e){return"ល្ងាច"===e},meridiem:function(e,t,a){return e<12?"ព្រឹក":"ល្ងាច"},calendar:{sameDay:"[ថ្ងៃនេះ ម៉ោង] LT",nextDay:"[ស្អែក ម៉ោង] LT",nextWeek:"dddd [ម៉ោង] LT",lastDay:"[ម្សិលមិញ ម៉ោង] LT",lastWeek:"dddd [សប្តាហ៍មុន] [ម៉ោង] LT",sameElse:"L"},relativeTime:{future:"%sទៀត",past:"%sមុន",s:"ប៉ុន្មានវិនាទី",ss:"%d វិនាទី",m:"មួយនាទី",mm:"%d នាទី",h:"មួយម៉ោង",hh:"%d ម៉ោង",d:"មួយថ្ងៃ",dd:"%d ថ្ងៃ",M:"មួយខែ",MM:"%d ខែ",y:"មួយឆ្នាំ",yy:"%d ឆ្នាំ"},dayOfMonthOrdinalParse:/ទី\d{1,2}/,ordinal:"ទី%d",preparse:function(e){return e.replace(/[១២៣៤៥៦៧៨៩០]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},week:{dow:1,doy:4}})}(a("wd/R"))},"7BjC":function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={s:["mõne sekundi","mõni sekund","paar sekundit"],ss:[e+"sekundi",e+"sekundit"],m:["ühe minuti","üks minut"],mm:[e+" minuti",e+" minutit"],h:["ühe tunni","tund aega","üks tund"],hh:[e+" tunni",e+" tundi"],d:["ühe päeva","üks päev"],M:["kuu aja","kuu aega","üks kuu"],MM:[e+" kuu",e+" kuud"],y:["ühe aasta","aasta","üks aasta"],yy:[e+" aasta",e+" aastat"]};return t?r[a][2]?r[a][2]:r[a][1]:n?r[a][0]:r[a][1]}e.defineLocale("et",{months:"jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember".split("_"),monthsShort:"jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets".split("_"),weekdays:"pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev".split("_"),weekdaysShort:"P_E_T_K_N_R_L".split("_"),weekdaysMin:"P_E_T_K_N_R_L".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[Täna,] LT",nextDay:"[Homme,] LT",nextWeek:"[Järgmine] dddd LT",lastDay:"[Eile,] LT",lastWeek:"[Eelmine] dddd LT",sameElse:"L"},relativeTime:{future:"%s pärast",past:"%s tagasi",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:"%d päeva",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},"7C5Q":function(e,t,a){!function(e){"use strict";e.defineLocale("en-in",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:0,doy:6}})}(a("wd/R"))},"7aV9":function(e,t,a){!function(e){"use strict";e.defineLocale("si",{months:"ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්".split("_"),monthsShort:"ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ".split("_"),weekdays:"ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා".split("_"),weekdaysShort:"ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන".split("_"),weekdaysMin:"ඉ_ස_අ_බ_බ්‍ර_සි_සෙ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"a h:mm",LTS:"a h:mm:ss",L:"YYYY/MM/DD",LL:"YYYY MMMM D",LLL:"YYYY MMMM D, a h:mm",LLLL:"YYYY MMMM D [වැනි] dddd, a h:mm:ss"},calendar:{sameDay:"[අද] LT[ට]",nextDay:"[හෙට] LT[ට]",nextWeek:"dddd LT[ට]",lastDay:"[ඊයේ] LT[ට]",lastWeek:"[පසුගිය] dddd LT[ට]",sameElse:"L"},relativeTime:{future:"%sකින්",past:"%sකට පෙර",s:"තත්පර කිහිපය",ss:"තත්පර %d",m:"මිනිත්තුව",mm:"මිනිත්තු %d",h:"පැය",hh:"පැය %d",d:"දිනය",dd:"දින %d",M:"මාසය",MM:"මාස %d",y:"වසර",yy:"වසර %d"},dayOfMonthOrdinalParse:/\d{1,2} වැනි/,ordinal:function(e){return e+" වැනි"},meridiemParse:/පෙර වරු|පස් වරු|පෙ.ව|ප.ව./,isPM:function(e){return"ප.ව."===e||"පස් වරු"===e},meridiem:function(e,t,a){return e>11?a?"ප.ව.":"පස් වරු":a?"පෙ.ව.":"පෙර වරු"}})}(a("wd/R"))},"8/+R":function(e,t,a){!function(e){"use strict";var t={1:"੧",2:"੨",3:"੩",4:"੪",5:"੫",6:"੬",7:"੭",8:"੮",9:"੯",0:"੦"},a={"੧":"1","੨":"2","੩":"3","੪":"4","੫":"5","੬":"6","੭":"7","੮":"8","੯":"9","੦":"0"};e.defineLocale("pa-in",{months:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),monthsShort:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),weekdays:"ਐਤਵਾਰ_ਸੋਮਵਾਰ_ਮੰਗਲਵਾਰ_ਬੁਧਵਾਰ_ਵੀਰਵਾਰ_ਸ਼ੁੱਕਰਵਾਰ_ਸ਼ਨੀਚਰਵਾਰ".split("_"),weekdaysShort:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),weekdaysMin:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),longDateFormat:{LT:"A h:mm ਵਜੇ",LTS:"A h:mm:ss ਵਜੇ",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm ਵਜੇ",LLLL:"dddd, D MMMM YYYY, A h:mm ਵਜੇ"},calendar:{sameDay:"[ਅਜ] LT",nextDay:"[ਕਲ] LT",nextWeek:"[ਅਗਲਾ] dddd, LT",lastDay:"[ਕਲ] LT",lastWeek:"[ਪਿਛਲੇ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ਵਿੱਚ",past:"%s ਪਿਛਲੇ",s:"ਕੁਝ ਸਕਿੰਟ",ss:"%d ਸਕਿੰਟ",m:"ਇਕ ਮਿੰਟ",mm:"%d ਮਿੰਟ",h:"ਇੱਕ ਘੰਟਾ",hh:"%d ਘੰਟੇ",d:"ਇੱਕ ਦਿਨ",dd:"%d ਦਿਨ",M:"ਇੱਕ ਮਹੀਨਾ",MM:"%d ਮਹੀਨੇ",y:"ਇੱਕ ਸਾਲ",yy:"%d ਸਾਲ"},preparse:function(e){return e.replace(/[੧੨੩੪੫੬੭੮੯੦]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/ਰਾਤ|ਸਵੇਰ|ਦੁਪਹਿਰ|ਸ਼ਾਮ/,meridiemHour:function(e,t){return 12===e&&(e=0),"ਰਾਤ"===t?e<4?e:e+12:"ਸਵੇਰ"===t?e:"ਦੁਪਹਿਰ"===t?e>=10?e:e+12:"ਸ਼ਾਮ"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"ਰਾਤ":e<10?"ਸਵੇਰ":e<17?"ਦੁਪਹਿਰ":e<20?"ਸ਼ਾਮ":"ਰਾਤ"},week:{dow:0,doy:6}})}(a("wd/R"))},"8mBD":function(e,t,a){!function(e){"use strict";e.defineLocale("pt",{months:"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Do_2ª_3ª_4ª_5ª_6ª_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",w:"uma semana",ww:"%d semanas",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a("wd/R"))},"9hfn":function(e,t,a){"use strict";(function(e){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var a=[],n=!0,r=!1,s=void 0;try{for(var i,o=e[Symbol.iterator]();!(n=(i=o.next()).done)&&(a.push(i.value),!t||a.length!==t);n=!0);}catch(e){r=!0,s=e}finally{try{n||null==o.return||o.return()}finally{if(r)throw s}}return a}(e,t)||o(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||o(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){if(e){if("string"==typeof e)return d(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);return"Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,n=new Array(t);a<t;a++)n[a]=e[a];return n}a.d(t,"a",(function(){return Et}));var u="asc",l="desc",_="none",c="records",m=[10,20,30,40,50],h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{};var f=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){var a="[object Arguments]",n="[object Map]",r="[object Object]",s="[object Set]",i=/^\[object .+?Constructor\]$/,o=/^(?:0|[1-9]\d*)$/,d={};d["[object Float32Array]"]=d["[object Float64Array]"]=d["[object Int8Array]"]=d["[object Int16Array]"]=d["[object Int32Array]"]=d["[object Uint8Array]"]=d["[object Uint8ClampedArray]"]=d["[object Uint16Array]"]=d["[object Uint32Array]"]=!0,d[a]=d["[object Array]"]=d["[object ArrayBuffer]"]=d["[object Boolean]"]=d["[object DataView]"]=d["[object Date]"]=d["[object Error]"]=d["[object Function]"]=d[n]=d["[object Number]"]=d[r]=d["[object RegExp]"]=d[s]=d["[object String]"]=d["[object WeakMap]"]=!1;var u="object"==typeof h&&h&&h.Object===Object&&h,l="object"==typeof self&&self&&self.Object===Object&&self,_=u||l||Function("return this")(),c=t&&!t.nodeType&&t,m=c&&e&&!e.nodeType&&e,f=m&&m.exports===c,p=f&&u.process,M=function(){try{return p&&p.binding&&p.binding("util")}catch(e){}}(),y=M&&M.isTypedArray;function g(e,t){for(var a=-1,n=null==e?0:e.length;++a<n;)if(t(e[a],a,e))return!0;return!1}function L(e){var t=-1,a=Array(e.size);return e.forEach((function(e,n){a[++t]=[n,e]})),a}function v(e){var t=-1,a=Array(e.size);return e.forEach((function(e){a[++t]=e})),a}var Y,w,b,k=Array.prototype,D=Function.prototype,T=Object.prototype,S=_["__core-js_shared__"],j=D.toString,x=T.hasOwnProperty,H=(Y=/[^.]+$/.exec(S&&S.keys&&S.keys.IE_PROTO||""))?"Symbol(src)_1."+Y:"",P=T.toString,O=RegExp("^"+j.call(x).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),C=f?_.Buffer:void 0,E=_.Symbol,R=_.Uint8Array,F=T.propertyIsEnumerable,W=k.splice,A=E?E.toStringTag:void 0,z=Object.getOwnPropertySymbols,N=C?C.isBuffer:void 0,I=(w=Object.keys,b=Object,function(e){return w(b(e))}),U=Me(_,"DataView"),J=Me(_,"Map"),B=Me(_,"Promise"),G=Me(_,"Set"),$=Me(_,"WeakMap"),V=Me(Object,"create"),q=ve(U),Q=ve(J),K=ve(B),X=ve(G),Z=ve($),ee=E?E.prototype:void 0,te=ee?ee.valueOf:void 0;function ae(e){var t=-1,a=null==e?0:e.length;for(this.clear();++t<a;){var n=e[t];this.set(n[0],n[1])}}function ne(e){var t=-1,a=null==e?0:e.length;for(this.clear();++t<a;){var n=e[t];this.set(n[0],n[1])}}function re(e){var t=-1,a=null==e?0:e.length;for(this.clear();++t<a;){var n=e[t];this.set(n[0],n[1])}}function se(e){var t=-1,a=null==e?0:e.length;for(this.__data__=new re;++t<a;)this.add(e[t])}function ie(e){var t=this.__data__=new ne(e);this.size=t.size}function oe(e,t){var a=be(e),n=!a&&we(e),r=!a&&!n&&ke(e),s=!a&&!n&&!r&&xe(e),i=a||n||r||s,o=i?function(e,t){for(var a=-1,n=Array(e);++a<e;)n[a]=t(a);return n}(e.length,String):[],d=o.length;for(var u in e)!t&&!x.call(e,u)||i&&("length"==u||r&&("offset"==u||"parent"==u)||s&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Le(u,d))||o.push(u);return o}function de(e,t){for(var a=e.length;a--;)if(Ye(e[a][0],t))return a;return-1}function ue(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":A&&A in Object(e)?function(e){var t=x.call(e,A),a=e[A];try{e[A]=void 0;var n=!0}catch(e){}var r=P.call(e);n&&(t?e[A]=a:delete e[A]);return r}(e):function(e){return P.call(e)}(e)}function le(e){return je(e)&&ue(e)==a}function _e(e,t,i,o,d){return e===t||(null==e||null==t||!je(e)&&!je(t)?e!=e&&t!=t:function(e,t,i,o,d,u){var l=be(e),_=be(t),c=l?"[object Array]":ge(e),m=_?"[object Array]":ge(t),h=(c=c==a?r:c)==r,f=(m=m==a?r:m)==r,p=c==m;if(p&&ke(e)){if(!ke(t))return!1;l=!0,h=!1}if(p&&!h)return u||(u=new ie),l||xe(e)?he(e,t,i,o,d,u):function(e,t,a,r,i,o,d){switch(a){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!o(new R(e),new R(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Ye(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case n:var u=L;case s:var l=1&r;if(u||(u=v),e.size!=t.size&&!l)return!1;var _=d.get(e);if(_)return _==t;r|=2,d.set(e,t);var c=he(u(e),u(t),r,i,o,d);return d.delete(e),c;case"[object Symbol]":if(te)return te.call(e)==te.call(t)}return!1}(e,t,c,i,o,d,u);if(!(1&i)){var M=h&&x.call(e,"__wrapped__"),y=f&&x.call(t,"__wrapped__");if(M||y){var g=M?e.value():e,Y=y?t.value():t;return u||(u=new ie),d(g,Y,i,o,u)}}if(!p)return!1;return u||(u=new ie),function(e,t,a,n,r,s){var i=1&a,o=fe(e),d=o.length,u=fe(t).length;if(d!=u&&!i)return!1;var l=d;for(;l--;){var _=o[l];if(!(i?_ in t:x.call(t,_)))return!1}var c=s.get(e);if(c&&s.get(t))return c==t;var m=!0;s.set(e,t),s.set(t,e);var h=i;for(;++l<d;){_=o[l];var f=e[_],p=t[_];if(n)var M=i?n(p,f,_,t,e,s):n(f,p,_,e,t,s);if(!(void 0===M?f===p||r(f,p,a,n,s):M)){m=!1;break}h||(h="constructor"==_)}if(m&&!h){var y=e.constructor,g=t.constructor;y==g||!("constructor"in e)||!("constructor"in t)||"function"==typeof y&&y instanceof y&&"function"==typeof g&&g instanceof g||(m=!1)}return s.delete(e),s.delete(t),m}(e,t,i,o,d,u)}(e,t,i,o,_e,d))}function ce(e){return!(!Se(e)||function(e){return!!H&&H in e}(e))&&(De(e)?O:i).test(ve(e))}function me(e){if(a=(t=e)&&t.constructor,n="function"==typeof a&&a.prototype||T,t!==n)return I(e);var t,a,n,r=[];for(var s in Object(e))x.call(e,s)&&"constructor"!=s&&r.push(s);return r}function he(e,t,a,n,r,s){var i=1&a,o=e.length,d=t.length;if(o!=d&&!(i&&d>o))return!1;var u=s.get(e);if(u&&s.get(t))return u==t;var l=-1,_=!0,c=2&a?new se:void 0;for(s.set(e,t),s.set(t,e);++l<o;){var m=e[l],h=t[l];if(n)var f=i?n(h,m,l,t,e,s):n(m,h,l,e,t,s);if(void 0!==f){if(f)continue;_=!1;break}if(c){if(!g(t,(function(e,t){if(i=t,!c.has(i)&&(m===e||r(m,e,a,n,s)))return c.push(t);var i}))){_=!1;break}}else if(m!==h&&!r(m,h,a,n,s)){_=!1;break}}return s.delete(e),s.delete(t),_}function fe(e){return function(e,t,a){var n=t(e);return be(e)?n:function(e,t){for(var a=-1,n=t.length,r=e.length;++a<n;)e[r+a]=t[a];return e}(n,a(e))}(e,He,ye)}function pe(e,t){var a,n,r=e.__data__;return("string"==(n=typeof(a=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==a:null===a)?r["string"==typeof t?"string":"hash"]:r.map}function Me(e,t){var a=function(e,t){return null==e?void 0:e[t]}(e,t);return ce(a)?a:void 0}ae.prototype.clear=function(){this.__data__=V?V(null):{},this.size=0},ae.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ae.prototype.get=function(e){var t=this.__data__;if(V){var a=t[e];return"__lodash_hash_undefined__"===a?void 0:a}return x.call(t,e)?t[e]:void 0},ae.prototype.has=function(e){var t=this.__data__;return V?void 0!==t[e]:x.call(t,e)},ae.prototype.set=function(e,t){var a=this.__data__;return this.size+=this.has(e)?0:1,a[e]=V&&void 0===t?"__lodash_hash_undefined__":t,this},ne.prototype.clear=function(){this.__data__=[],this.size=0},ne.prototype.delete=function(e){var t=this.__data__,a=de(t,e);return!(a<0)&&(a==t.length-1?t.pop():W.call(t,a,1),--this.size,!0)},ne.prototype.get=function(e){var t=this.__data__,a=de(t,e);return a<0?void 0:t[a][1]},ne.prototype.has=function(e){return de(this.__data__,e)>-1},ne.prototype.set=function(e,t){var a=this.__data__,n=de(a,e);return n<0?(++this.size,a.push([e,t])):a[n][1]=t,this},re.prototype.clear=function(){this.size=0,this.__data__={hash:new ae,map:new(J||ne),string:new ae}},re.prototype.delete=function(e){var t=pe(this,e).delete(e);return this.size-=t?1:0,t},re.prototype.get=function(e){return pe(this,e).get(e)},re.prototype.has=function(e){return pe(this,e).has(e)},re.prototype.set=function(e,t){var a=pe(this,e),n=a.size;return a.set(e,t),this.size+=a.size==n?0:1,this},se.prototype.add=se.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},se.prototype.has=function(e){return this.__data__.has(e)},ie.prototype.clear=function(){this.__data__=new ne,this.size=0},ie.prototype.delete=function(e){var t=this.__data__,a=t.delete(e);return this.size=t.size,a},ie.prototype.get=function(e){return this.__data__.get(e)},ie.prototype.has=function(e){return this.__data__.has(e)},ie.prototype.set=function(e,t){var a=this.__data__;if(a instanceof ne){var n=a.__data__;if(!J||n.length<199)return n.push([e,t]),this.size=++a.size,this;a=this.__data__=new re(n)}return a.set(e,t),this.size=a.size,this};var ye=z?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var a=-1,n=null==e?0:e.length,r=0,s=[];++a<n;){var i=e[a];t(i,a,e)&&(s[r++]=i)}return s}(z(e),(function(t){return F.call(e,t)})))}:function(){return[]},ge=ue;function Le(e,t){return!!(t=null==t?9007199254740991:t)&&("number"==typeof e||o.test(e))&&e>-1&&e%1==0&&e<t}function ve(e){if(null!=e){try{return j.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Ye(e,t){return e===t||e!=e&&t!=t}(U&&"[object DataView]"!=ge(new U(new ArrayBuffer(1)))||J&&ge(new J)!=n||B&&"[object Promise]"!=ge(B.resolve())||G&&ge(new G)!=s||$&&"[object WeakMap]"!=ge(new $))&&(ge=function(e){var t=ue(e),a=t==r?e.constructor:void 0,i=a?ve(a):"";if(i)switch(i){case q:return"[object DataView]";case Q:return n;case K:return"[object Promise]";case X:return s;case Z:return"[object WeakMap]"}return t});var we=le(function(){return arguments}())?le:function(e){return je(e)&&x.call(e,"callee")&&!F.call(e,"callee")},be=Array.isArray;var ke=N||function(){return!1};function De(e){if(!Se(e))return!1;var t=ue(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Te(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function Se(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function je(e){return null!=e&&"object"==typeof e}var xe=y?function(e){return function(t){return e(t)}}(y):function(e){return je(e)&&Te(e.length)&&!!d[ue(e)]};function He(e){return null!=(t=e)&&Te(t.length)&&!De(t)?oe(e):me(e);var t}e.exports=function(e,t){return _e(e,t)}})),p={a:["a","à","á","â","ã","ä","å","æ","ā","ă","ą","ǎ","ǟ","ǡ","ǻ","ȁ","ȃ","ȧ","ɐ","ɑ","ɒ","ͣ","а","ӑ","ӓ","ᵃ","ᵄ","ᶏ","ḁ","ẚ","ạ","ả","ấ","ầ","ẩ","ẫ","ậ","ắ","ằ","ẳ","ẵ","ặ","ₐ","ⱥ","ａ"],b:["b","ƀ","ƃ","ɓ","ᖯ","ᵇ","ᵬ","ᶀ","ḃ","ḅ","ḇ","ｂ"],c:["c","ç","ć","ĉ","ċ","č","ƈ","ȼ","ɕ","ͨ","ᴄ","ᶜ","ḉ","ↄ","ｃ"],d:["d","ď","đ","Ƌ","ƌ","ȡ","ɖ","ɗ","ͩ","ᵈ","ᵭ","ᶁ","ᶑ","ḋ","ḍ","ḏ","ḑ","ḓ","ｄ"],e:["e","è","é","ê","ë","ē","ĕ","ė","ę","ě","ǝ","ȅ","ȇ","ȩ","ɇ","ɘ","ͤ","ᵉ","ᶒ","ḕ","ḗ","ḙ","ḛ","ḝ","ẹ","ẻ","ẽ","ế","ề","ể","ễ","ệ","ₑ","ｅ"],f:["f","ƒ","ᵮ","ᶂ","ᶠ","ḟ","ｆ"],g:["g","ĝ","ğ","ġ","ģ","ǥ","ǧ","ǵ","ɠ","ɡ","ᵍ","ᵷ","ᵹ","ᶃ","ᶢ","ḡ","ｇ"],h:["h","ĥ","ħ","ƕ","ȟ","ɥ","ɦ","ʮ","ʯ","ʰ","ʱ","ͪ","Һ","һ","ᑋ","ᶣ","ḣ","ḥ","ḧ","ḩ","ḫ","ⱨ","ｈ"],i:["i","ì","í","î","ï","ĩ","ī","ĭ","į","ǐ","ȉ","ȋ","ɨ","ͥ","ᴉ","ᵎ","ᵢ","ᶖ","ᶤ","ḭ","ḯ","ỉ","ị","ｉ"],j:["j","ĵ","ǰ","ɉ","ʝ","ʲ","ᶡ","ᶨ","ｊ"],k:["k","ķ","ƙ","ǩ","ʞ","ᵏ","ᶄ","ḱ","ḳ","ḵ","ⱪ","ｋ"],l:["l","ĺ","ļ","ľ","ŀ","ł","ƚ","ȴ","ɫ","ɬ","ɭ","ˡ","ᶅ","ᶩ","ᶪ","ḷ","ḹ","ḻ","ḽ","ℓ","ⱡ"],m:["m","ɯ","ɰ","ɱ","ͫ","ᴟ","ᵐ","ᵚ","ᵯ","ᶆ","ᶬ","ᶭ","ḿ","ṁ","ṃ","㎡","㎥","ｍ"],n:["n","ñ","ń","ņ","ň","ŉ","ƞ","ǹ","ȵ","ɲ","ɳ","ᵰ","ᶇ","ᶮ","ᶯ","ṅ","ṇ","ṉ","ṋ","ⁿ","ｎ"],o:["o","ò","ó","ô","õ","ö","ø","ō","ŏ","ő","ơ","ǒ","ǫ","ǭ","ǿ","ȍ","ȏ","ȫ","ȭ","ȯ","ȱ","ɵ","ͦ","о","ӧ","ө","ᴏ","ᴑ","ᴓ","ᴼ","ᵒ","ᶱ","ṍ","ṏ","ṑ","ṓ","ọ","ỏ","ố","ồ","ổ","ỗ","ộ","ớ","ờ","ở","ỡ","ợ","ₒ","ｏ","𐐬"],p:["p","ᵖ","ᵱ","ᵽ","ᶈ","ṕ","ṗ","ｐ"],q:["q","ɋ","ʠ","ᛩ","ｑ"],r:["r","ŕ","ŗ","ř","ȑ","ȓ","ɍ","ɹ","ɻ","ʳ","ʴ","ʵ","ͬ","ᵣ","ᵲ","ᶉ","ṙ","ṛ","ṝ","ṟ"],s:["s","ś","ŝ","ş","š","ș","ʂ","ᔆ","ᶊ","ṡ","ṣ","ṥ","ṧ","ṩ","ｓ"],t:["t","ţ","ť","ŧ","ƫ","ƭ","ț","ʇ","ͭ","ᵀ","ᵗ","ᵵ","ᶵ","ṫ","ṭ","ṯ","ṱ","ẗ","ｔ"],u:["u","ù","ú","û","ü","ũ","ū","ŭ","ů","ű","ų","ư","ǔ","ǖ","ǘ","ǚ","ǜ","ȕ","ȗ","ͧ","ߎ","ᵘ","ᵤ","ṳ","ṵ","ṷ","ṹ","ṻ","ụ","ủ","ứ","ừ","ử","ữ","ự","ｕ"],v:["v","ʋ","ͮ","ᵛ","ᵥ","ᶹ","ṽ","ṿ","ⱱ","ｖ","ⱴ"],w:["w","ŵ","ʷ","ᵂ","ẁ","ẃ","ẅ","ẇ","ẉ","ẘ","ⱳ","ｗ"],x:["x","̽","͓","ᶍ","ͯ","ẋ","ẍ","ₓ","ｘ"],y:["y","ý","ÿ","ŷ","ȳ","ɏ","ʸ","ẏ","ỳ","ỵ","ỷ","ỹ","ｙ"],z:["z","ź","ż","ž","ƶ","ȥ","ɀ","ʐ","ʑ","ᙆ","ᙇ","ᶻ","ᶼ","ᶽ","ẑ","ẓ","ẕ","ⱬ","ｚ"]},M=function(){var e={};for(var t in p){var a=p[t];for(var n in a){var r=a[n];r!==t&&(e[r]=t)}}return e}(),y=/[^a-z0-9\s,.-]/,g=function(e){if(-1===e.search(y))return e;for(var t="",a=e.length,n=0;n<a;n++){var r=e.charAt(n);t+=r in M?M[r]:r}return t},L=function(e){return e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")},v={format:function(e){return e},filterPredicate:function(e,t){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(null==e)return!1;var r=a?String(e).toLowerCase():g(L(String(e)).toLowerCase()),s=a?t.toLowerCase():g(L(t).toLowerCase());return n?r===s:r.indexOf(s)>-1},compare:function(e,t){function a(e){return null==e?"":g(String(e).toLowerCase())}return(e=a(e))<(t=a(t))?-1:e>t?1:0}};function Y(e,t,a,n,r,s,i,o,d,u){"boolean"!=typeof i&&(d=o,o=i,i=!1);const l="function"==typeof a?a.options:a;let _;if(e&&e.render&&(l.render=e.render,l.staticRenderFns=e.staticRenderFns,l._compiled=!0,r&&(l.functional=!0)),n&&(l._scopeId=n),s?(_=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,d(e)),e&&e._registeredComponents&&e._registeredComponents.add(s)},l._ssrRegister=_):t&&(_=i?function(e){t.call(this,u(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,o(e))}),_)if(l.functional){const e=l.render;l.render=function(t,a){return _.call(a),e(t,a)}}else{const e=l.beforeCreate;l.beforeCreate=e?[].concat(e,_):[_]}return a}var w=Y({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"vgt-wrap__footer vgt-clearfix"},[e.perPageDropdownEnabled?a("div",{staticClass:"footer__row-count vgt-pull-left"},[a("form",[a("label",{staticClass:"footer__row-count__label",attrs:{for:e.id}},[e._v(e._s(e.rowsPerPageText)+":")]),e._v(" "),a("select",{directives:[{name:"model",rawName:"v-model",value:e.currentPerPage,expression:"currentPerPage"}],staticClass:"footer__row-count__select",attrs:{id:e.id,autocomplete:"off",name:"perPageSelect","aria-controls":"vgt-table"},on:{change:[function(t){var a=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){return"_value"in e?e._value:e.value}));e.currentPerPage=t.target.multiple?a:a[0]},e.perPageChanged]}},[e._l(e.rowsPerPageOptions,(function(t,n){return a("option",{key:n,domProps:{value:t}},[e._v("\n          "+e._s(t)+"\n        ")])})),e._v(" "),e.paginateDropdownAllowAll?a("option",{domProps:{value:-1}},[e._v(e._s(e.allText))]):e._e()],2)])]):e._e(),e._v(" "),a("div",{staticClass:"footer__navigation vgt-pull-right"},[a("pagination-page-info",{attrs:{"total-records":e.total,"last-page":e.pagesCount,"current-page":e.currentPage,"current-per-page":e.currentPerPage,"of-text":e.ofText,"page-text":e.pageText,"info-fn":e.infoFn,mode:e.mode},on:{"page-changed":e.changePage}}),e._v(" "),e.jumpFirstOrLast?a("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.firstIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.firstPage(t)}}},[a("span",{staticClass:"chevron",class:{left:!e.rtl,right:e.rtl},attrs:{"aria-hidden":"true"}}),e._v(" "),a("span",[e._v(e._s(e.firstText))])]):e._e(),e._v(" "),a("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.prevIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.previousPage(t)}}},[a("span",{staticClass:"chevron",class:{left:!e.rtl,right:e.rtl},attrs:{"aria-hidden":"true"}}),e._v(" "),a("span",[e._v(e._s(e.prevText))])]),e._v(" "),a("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.nextIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.nextPage(t)}}},[a("span",[e._v(e._s(e.nextText))]),e._v(" "),a("span",{staticClass:"chevron",class:{right:!e.rtl,left:e.rtl},attrs:{"aria-hidden":"true"}})]),e._v(" "),e.jumpFirstOrLast?a("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.lastIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.lastPage(t)}}},[a("span",[e._v(e._s(e.lastText))]),e._v(" "),a("span",{staticClass:"chevron",class:{right:!e.rtl,left:e.rtl},attrs:{"aria-hidden":"true"}})]):e._e()],1)])},staticRenderFns:[]},void 0,{name:"VgtPagination",props:{styleClass:{default:"table table-bordered"},total:{default:null},perPage:{},rtl:{default:!1},perPageDropdownEnabled:{default:!0},customRowsPerPageDropdown:{default:function(){return[]}},paginateDropdownAllowAll:{default:!0},mode:{default:c},jumpFirstOrLast:{default:!1},firstText:{default:"First"},lastText:{default:"Last"},nextText:{default:"Next"},prevText:{default:"Prev"},rowsPerPageText:{default:"Rows per page:"},ofText:{default:"of"},pageText:{default:"page"},allText:{default:"All"},infoFn:{default:null}},data:function(){return{id:this.getId(),currentPage:1,prevPage:0,currentPerPage:10,rowsPerPageOptions:[]}},watch:{perPage:{handler:function(e,t){this.handlePerPage(),this.perPageChanged(t)},immediate:!0},customRowsPerPageDropdown:function(){this.handlePerPage()},total:{handler:function(e,t){-1===this.rowsPerPageOptions.indexOf(this.currentPerPage)&&(this.currentPerPage=e)}}},computed:{pagesCount:function(){if(-1===this.currentPerPage)return 1;var e=Math.floor(this.total/this.currentPerPage);return 0===this.total%this.currentPerPage?e:e+1},firstIsPossible:function(){return this.currentPage>1},lastIsPossible:function(){return this.currentPage<Math.ceil(this.total/this.currentPerPage)},nextIsPossible:function(){return this.currentPage<this.pagesCount},prevIsPossible:function(){return this.currentPage>1}},methods:{getId:function(){return"vgt-select-rpp-".concat(Math.floor(Math.random()*Date.now()))},changePage:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e>0&&this.total>this.currentPerPage*(e-1)&&(this.prevPage=this.currentPage,this.currentPage=e,this.pageChanged(t))},firstPage:function(){this.firstIsPossible&&(this.currentPage=1,this.prevPage=0,this.pageChanged())},lastPage:function(){this.lastIsPossible&&(this.currentPage=this.pagesCount,this.prev=this.currentPage-1,this.pageChanged())},nextPage:function(){this.nextIsPossible&&(this.prevPage=this.currentPage,++this.currentPage,this.pageChanged())},previousPage:function(){this.prevIsPossible&&(this.prevPage=this.currentPage,--this.currentPage,this.pageChanged())},pageChanged:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={currentPage:this.currentPage,prevPage:this.prevPage};e||(t.noEmit=!0),this.$emit("page-changed",t)},perPageChanged:function(e){e&&this.$emit("per-page-changed",{currentPerPage:this.currentPerPage}),this.changePage(1,!1)},handlePerPage:function(){if(null!==this.customRowsPerPageDropdown&&Array.isArray(this.customRowsPerPageDropdown)&&0!==this.customRowsPerPageDropdown.length?this.rowsPerPageOptions=JSON.parse(JSON.stringify(this.customRowsPerPageDropdown)):this.rowsPerPageOptions=JSON.parse(JSON.stringify(m)),this.perPage){this.currentPerPage=this.perPage;for(var e=!1,t=0;t<this.rowsPerPageOptions.length;t++)this.rowsPerPageOptions[t]===this.perPage&&(e=!0);e||-1===this.perPage||this.rowsPerPageOptions.unshift(this.perPage)}else this.currentPerPage=10}},mounted:function(){},components:{"pagination-page-info":Y({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"footer__navigation__page-info"},[e.infoFn?a("div",[e._v("\n    "+e._s(e.infoFn(e.infoParams))+"\n  ")]):"pages"===e.mode?a("form",{on:{submit:function(e){e.preventDefault()}}},[a("label",{staticClass:"page-info__label",attrs:{for:e.id}},[a("span",[e._v(e._s(e.pageText))]),e._v(" "),a("input",{staticClass:"footer__navigation__page-info__current-entry",attrs:{id:e.id,"aria-describedby":"change-page-hint","aria-controls":"vgb-table",type:"text"},domProps:{value:e.currentPage},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.stopPropagation(),e.changePage(t))}}}),e._v(" "),a("span",[e._v(e._s(e.pageInfo))])]),e._v(" "),a("span",{staticStyle:{display:"none"},attrs:{id:"change-page-hint"}},[e._v("\n      Type a page number and press Enter to change the page.\n    ")])]):a("div",[e._v("\n    "+e._s(e.recordInfo)+"\n  ")])])},staticRenderFns:[]},void 0,{name:"VgtPaginationPageInfo",props:{currentPage:{default:1},lastPage:{default:1},totalRecords:{default:0},ofText:{default:"of",type:String},pageText:{default:"page",type:String},currentPerPage:{},mode:{default:c},infoFn:{default:null}},data:function(){return{id:this.getId()}},computed:{pageInfo:function(){return"".concat(this.ofText," ").concat(this.lastPage)},firstRecordOnPage:function(){return(this.currentPage-1)*this.currentPerPage+1},lastRecordOnPage:function(){return-1===this.currentPerPage?this.totalRecords:Math.min(this.totalRecords,this.currentPage*this.currentPerPage)},recordInfo:function(){var e=this.firstRecordOnPage,t=this.lastRecordOnPage;return 0===t&&(e=0),"".concat(e," - ").concat(t," ").concat(this.ofText," ").concat(this.totalRecords)},infoParams:function(){var e=this.firstRecordOnPage,t=this.lastRecordOnPage;return 0===t&&(e=0),{firstRecordOnPage:e,lastRecordOnPage:t,totalRecords:this.totalRecords,currentPage:this.currentPage,totalPage:this.lastPage}}},methods:{getId:function(){return"vgt-page-input-".concat(Math.floor(Math.random()*Date.now()))},changePage:function(e){var t=parseInt(e.target.value,10);if(Number.isNaN(t)||t>this.lastPage||t<1)return e.target.value=this.currentPage,!1;e.target.value=t,this.$emit("page-changed",t)}},mounted:function(){},components:{}},"data-v-347cbcfa",!1,void 0,!1,void 0,void 0,void 0)}},void 0,!1,void 0,!1,void 0,void 0,void 0),b=Y({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.showControlBar?a("div",{staticClass:"vgt-global-search vgt-clearfix"},[a("div",{staticClass:"vgt-global-search__input vgt-pull-left"},[e.searchEnabled?a("form",{attrs:{role:"search"},on:{submit:function(e){e.preventDefault()}}},[a("label",{attrs:{for:e.id}},[e._m(0),e._v(" "),a("span",{staticClass:"sr-only"},[e._v("Search")])]),e._v(" "),a("input",{staticClass:"vgt-input vgt-pull-left",attrs:{id:e.id,type:"text",placeholder:e.globalSearchPlaceholder},domProps:{value:e.value},on:{input:function(t){return e.updateValue(t.target.value)},keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.entered(t.target.value)}}})]):e._e()]),e._v(" "),a("div",{staticClass:"vgt-global-search__actions vgt-pull-right"},[e._t("internal-table-actions")],2)]):e._e()},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("span",{staticClass:"input__icon",attrs:{"aria-hidden":"true"}},[t("div",{staticClass:"magnifying-glass"})])}]},void 0,{name:"VgtGlobalSearch",props:["value","searchEnabled","globalSearchPlaceholder"],data:function(){return{globalSearchTerm:null,id:this.getId()}},computed:{showControlBar:function(){return!!this.searchEnabled||!(!this.$slots||!this.$slots["internal-table-actions"])}},methods:{updateValue:function(e){this.$emit("input",e),this.$emit("on-keyup",e)},entered:function(e){this.$emit("on-enter",e)},getId:function(){return"vgt-search-".concat(Math.floor(Math.random()*Date.now()))}}},void 0,!1,void 0,!1,void 0,void 0,void 0);function k(e){return e.firstSortType||"asc"}function D(e,t){return l===k(t)&&e===u?_:e===u?l:l===k(t)&&e===l?u:e===l?_:l===k(t)&&e===_?l:u}var T=Y({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("thead",[a("tr",[e.lineNumbers?a("th",{staticClass:"line-numbers",attrs:{scope:"col"}}):e._e(),e._v(" "),e.selectable?a("th",{staticClass:"vgt-checkbox-col",attrs:{scope:"col"}},[a("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected,indeterminate:e.allSelectedIndeterminate},on:{change:e.toggleSelectAll}})]):e._e(),e._v(" "),e._l(e.columns,(function(t,n){return t.hidden?e._e():a("th",{key:n,class:e.getHeaderClasses(t,n),style:e.columnStyles[n],attrs:{scope:"col",title:t.tooltip,"aria-sort":e.getColumnSortLong(t),"aria-controls":"col-"+n}},[e._t("table-column",[e._v("\n        "+e._s(t.label)+"\n      ")],{column:t}),e._v(" "),e.isSortableColumn(t)?a("button",{on:{click:function(a){return e.sort(a,t)}}},[a("span",{staticClass:"sr-only"},[e._v("\n          Sort table by "+e._s(t.label)+" in "+e._s(e.getColumnSortLong(t))+" order\n          ")])]):e._e()],2)}))],2),e._v(" "),a("vgt-filter-row",{ref:"filter-row",tag:"tr",attrs:{"global-search-enabled":e.searchEnabled,"line-numbers":e.lineNumbers,selectable:e.selectable,columns:e.columns,mode:e.mode,"typed-columns":e.typedColumns},on:{"filter-changed":e.filterRows},scopedSlots:e._u([{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)})],1)},staticRenderFns:[]},void 0,{name:"VgtTableHeader",props:{lineNumbers:{default:!1,type:Boolean},selectable:{default:!1,type:Boolean},allSelected:{default:!1,type:Boolean},allSelectedIndeterminate:{default:!1,type:Boolean},columns:{type:Array},mode:{type:String},typedColumns:{},sortable:{type:Boolean},multipleColumnSort:{type:Boolean,default:!0},getClasses:{type:Function},searchEnabled:{type:Boolean},tableRef:{},paginated:{}},watch:{columns:{handler:function(){this.setColumnStyles()},immediate:!0},tableRef:{handler:function(){this.setColumnStyles()},immediate:!0},paginated:{handler:function(){this.tableRef&&this.setColumnStyles()},deep:!0}},data:function(){return{checkBoxThStyle:{},lineNumberThStyle:{},columnStyles:[],sorts:[],ro:null}},computed:{},methods:{reset:function(){this.$refs["filter-row"].reset(!0)},toggleSelectAll:function(){this.$emit("on-toggle-select-all")},isSortableColumn:function(e){var t=e.sortable;return"boolean"==typeof t?t:this.sortable},sort:function(e,t){this.isSortableColumn(t)&&(e.shiftKey&&this.multipleColumnSort?this.sorts=function(e,t){var a=function(e,t){for(var a=0;a<e.length;a++)if(t.field===e[a].field)return a;return-1}(e,t);return-1===a?e.push({field:t.field,type:k(t)}):e[a].type=D(e[a].type,t),e}(this.sorts,t):this.sorts=function(e,t){var a=function(e,t){return 1===e.length&&e[0].field===t.field?e[0].type:void 0}(e,t),n=D(a,t);return[{field:t.field,type:a?n:k(t)}]}(this.sorts,t),this.$emit("on-sort-change",this.sorts))},setInitialSort:function(e){this.sorts=e,this.$emit("on-sort-change",this.sorts)},getColumnSort:function(e){for(var t=0;t<this.sorts.length;t+=1)if(this.sorts[t].field===e.field)return this.sorts[t].type||"asc";return null},getColumnSortLong:function(e){return"asc"===this.getColumnSort(e)?"ascending":"descending"},getHeaderClasses:function(e,t){return Object.assign({},this.getClasses(t,"th"),{sortable:this.isSortableColumn(e),"sorting sorting-desc":"desc"===this.getColumnSort(e),"sorting sorting-asc":"asc"===this.getColumnSort(e)})},filterRows:function(e){this.$emit("filter-changed",e)},getWidthStyle:function(e){return window&&window.getComputedStyle&&e?{width:window.getComputedStyle(e,null).width}:{width:"auto"}},setColumnStyles:function(){for(var e=[],t=0;t<this.columns.length;t++)if(this.tableRef){var a=0;this.selectable&&a++,this.lineNumbers&&a++;var n=this.tableRef.rows[0].cells[t+a];e.push(this.getWidthStyle(n))}else e.push({minWidth:this.columns[t].width?this.columns[t].width:"auto",maxWidth:this.columns[t].width?this.columns[t].width:"auto",width:this.columns[t].width?this.columns[t].width:"auto"});this.columnStyles=e},getColumnStyle:function(e,t){var a={minWidth:e.width?e.width:"auto",maxWidth:e.width?e.width:"auto",width:e.width?e.width:"auto"};if(this.tableRef){this.selectable&&t++,this.lineNumbers&&t++;var n=this.tableRef.rows[0].cells[t],r=window.getComputedStyle(n,null);a.width=r.width}return a}},mounted:function(){var e=this;this.$nextTick((function(){"ResizeObserver"in window&&(e.ro=new ResizeObserver((function(){e.setColumnStyles()})),e.ro.observe(e.$parent.$el),e.tableRef&&Array.from(e.$parent.$refs["table-header-primary"].$el.children[0].children).forEach((function(t){e.ro.observe(t)})))}))},beforeDestroy:function(){this.ro&&this.ro.disconnect()},components:{"vgt-filter-row":Y({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return e.hasFilterRow?a("tr",[e.lineNumbers?a("th"):e._e(),e._v(" "),e.selectable?a("th"):e._e(),e._v(" "),e._l(e.columns,(function(t,n){return t.hidden?e._e():a("th",{key:n,class:e.getClasses(t)},[e._t("column-filter",[e.isFilterable(t)?a("div",[e.isDropdown(t)?e._e():a("input",{staticClass:"vgt-input",attrs:{name:e.getName(t),type:"text",placeholder:e.getPlaceholder(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.updateFiltersOnEnter(t,a.target.value)},input:function(a){return e.updateFiltersOnKeyup(t,a.target.value)}}}),e._v(" "),e.isDropdownArray(t)?a("select",{staticClass:"vgt-select",attrs:{name:e.getName(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{change:function(a){return e.updateFiltersImmediately(t.field,a.target.value)}}},[a("option",{key:"-1",attrs:{value:""}},[e._v(e._s(e.getPlaceholder(t)))]),e._v(" "),e._l(t.filterOptions.filterDropdownItems,(function(t,n){return a("option",{key:n,domProps:{value:t}},[e._v("\n              "+e._s(t)+"\n            ")])}))],2):e._e(),e._v(" "),e.isDropdownObjects(t)?a("select",{staticClass:"vgt-select",attrs:{name:e.getName(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{change:function(a){return e.updateFiltersImmediately(t.field,a.target.value)}}},[a("option",{key:"-1",attrs:{value:""}},[e._v(e._s(e.getPlaceholder(t)))]),e._v(" "),e._l(t.filterOptions.filterDropdownItems,(function(t,n){return a("option",{key:n,domProps:{value:t.value}},[e._v(e._s(t.text))])}))],2):e._e()]):e._e()],{column:t,updateFilters:e.updateSlotFilter})],2)}))],2):e._e()},staticRenderFns:[]},void 0,{name:"VgtFilterRow",props:["lineNumbers","columns","typedColumns","globalSearchEnabled","selectable","mode"],watch:{columns:{handler:function(e,t){this.populateInitialFilters()},deep:!0,immediate:!0}},data:function(){return{columnFilters:{},timer:null}},computed:{hasFilterRow:function(){for(var e=0;e<this.columns.length;e++){var t=this.columns[e];if(t.filterOptions&&t.filterOptions.enabled)return!0}return!1}},methods:{fieldKey:function(e){return"function"==typeof e&&e.name?e.name:e},reset:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.columnFilters={},e&&this.$emit("filter-changed",this.columnFilters)},isFilterable:function(e){return e.filterOptions&&e.filterOptions.enabled},isDropdown:function(e){return this.isFilterable(e)&&e.filterOptions.filterDropdownItems&&e.filterOptions.filterDropdownItems.length},isDropdownObjects:function(e){return this.isDropdown(e)&&"object"===n(e.filterOptions.filterDropdownItems[0])},isDropdownArray:function(e){return this.isDropdown(e)&&"object"!==n(e.filterOptions.filterDropdownItems[0])},getClasses:function(e){var t="filter-th";return e.filterOptions&&e.filterOptions.styleClass?[t].concat(i(e.filterOptions.styleClass.split(" "))).join(" "):t},getPlaceholder:function(e){return this.isFilterable(e)&&e.filterOptions.placeholder||"Filter ".concat(e.label)},getName:function(e){return"vgt-".concat(this.fieldKey(e.field))},updateFiltersOnEnter:function(e,t){this.timer&&clearTimeout(this.timer),this.updateFiltersImmediately(e.field,t)},updateFiltersOnKeyup:function(e,t){"enter"!==e.filterOptions.trigger&&this.updateFilters(e,t)},updateSlotFilter:function(e,t){var a=e.filterOptions.slotFilterField||e.field;"function"==typeof e.filterOptions.formatValue&&(t=e.filterOptions.formatValue(t)),this.updateFiltersImmediately(a,t)},updateFilters:function(e,t){var a=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){a.updateFiltersImmediately(e.field,t)}),400)},updateFiltersImmediately:function(e,t){this.$set(this.columnFilters,this.fieldKey(e),t),this.$emit("filter-changed",this.columnFilters)},populateInitialFilters:function(){for(var e=0;e<this.columns.length;e++){var t=this.columns[e];this.isFilterable(t)&&void 0!==t.filterOptions.filterValue&&null!==t.filterOptions.filterValue&&this.$set(this.columnFilters,this.fieldKey(t.field),t.filterOptions.filterValue)}this.$emit("filter-changed",this.columnFilters)}}},"data-v-6869bf1c",!1,void 0,!1,void 0,void 0,void 0)}},void 0,!1,void 0,!1,void 0,void 0,void 0),S=Y({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("tr",["span"===e.headerRow.mode?a("th",{staticClass:"vgt-left-align vgt-row-header",attrs:{colspan:e.fullColspan}},[e.selectAllByGroup?[e._t("table-header-group-select",[a("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected},on:{change:function(t){return e.toggleSelectGroup(t)}}})],{columns:e.columns,row:e.headerRow})]:e._e(),e._v(" "),a("span",{on:{click:function(t){e.collapsable&&e.$emit("vgtExpand",!e.headerRow.vgtIsExpanded)}}},[e.collapsable?a("span",{staticClass:"triangle",class:{expand:e.headerRow.vgtIsExpanded}}):e._e(),e._v(" "),e._t("table-header-row",[e.headerRow.html?a("span",{domProps:{innerHTML:e._s(e.headerRow.label)}}):a("span",[e._v("\n          "+e._s(e.headerRow.label)+"\n        ")])],{row:e.headerRow})],2)],2):e._e(),e._v(" "),"span"!==e.headerRow.mode&&e.lineNumbers?a("th",{staticClass:"vgt-row-header"}):e._e(),e._v(" "),"span"!==e.headerRow.mode&&e.selectable?a("th",{staticClass:"vgt-row-header"},[e.selectAllByGroup?[e._t("table-header-group-select",[a("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected},on:{change:function(t){return e.toggleSelectGroup(t)}}})],{columns:e.columns,row:e.headerRow})]:e._e()],2):e._e(),e._v(" "),e._l(e.columns,(function(t,n){return"span"===e.headerRow.mode||t.hidden?e._e():a("th",{key:n,staticClass:"vgt-row-header",class:e.getClasses(n,"td"),on:{click:function(t){e.columnCollapsable(n)&&e.$emit("vgtExpand",!e.headerRow.vgtIsExpanded)}}},[e.columnCollapsable(n)?a("span",{staticClass:"triangle",class:{expand:e.headerRow.vgtIsExpanded}}):e._e(),e._v(" "),e._t("table-header-row",[t.html?e._e():a("span",[e._v("\n        "+e._s(e.collectFormatted(e.headerRow,t,!0))+"\n      ")]),e._v(" "),t.html?a("span",{domProps:{innerHTML:e._s(e.collectFormatted(e.headerRow,t,!0))}}):e._e()],{row:e.headerRow,column:t,formattedRow:e.formattedRow(e.headerRow,!0)})],2)}))],2)},staticRenderFns:[]},void 0,{name:"VgtHeaderRow",props:{headerRow:{type:Object},columns:{type:Array},lineNumbers:{type:Boolean},selectable:{type:Boolean},selectAllByGroup:{type:Boolean},collapsable:{type:[Boolean,Number],default:!1},collectFormatted:{type:Function},formattedRow:{type:Function},getClasses:{type:Function},fullColspan:{type:Number},groupIndex:{type:Number}},data:function(){return{}},computed:{allSelected:function(){var e=this.headerRow;this.groupChildObject;return e.children.filter((function(e){return e.vgtSelected})).length===e.children.length}},methods:{columnCollapsable:function(e){return!0===this.collapsable?0===e:e===this.collapsable},toggleSelectGroup:function(e){this.$emit("on-select-group-change",{groupIndex:this.groupIndex,checked:e.target.checked})}},mounted:function(){},components:{}},void 0,!1,void 0,!1,void 0,void 0,void 0);function j(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function x(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function H(e){x(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):("string"!=typeof e&&"[object String]"!==t||"undefined"==typeof console||(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://git.io/fjule"),console.warn((new Error).stack)),new Date(NaN))}function P(e,t){x(2,arguments);var a=H(e).getTime(),n=j(t);return new Date(a+n)}function O(e){return e.getTime()%6e4}function C(e){var t=new Date(e.getTime()),a=Math.ceil(t.getTimezoneOffset());return t.setSeconds(0,0),6e4*a+(a>0?(6e4+O(t))%6e4:O(t))}function E(e){x(1,arguments);var t=H(e);return!isNaN(t)}var R={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function F(e){return function(t){var a=t||{},n=a.width?String(a.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}var W={date:F({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:F({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:F({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},A={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function z(e){return function(t,a){var n,r=a||{};if("formatting"===(r.context?String(r.context):"standalone")&&e.formattingValues){var s=e.defaultFormattingWidth||e.defaultWidth,i=r.width?String(r.width):s;n=e.formattingValues[i]||e.formattingValues[s]}else{var o=e.defaultWidth,d=r.width?String(r.width):e.defaultWidth;n=e.values[d]||e.values[o]}return n[e.argumentCallback?e.argumentCallback(t):t]}}function N(e){return function(t,a){var n=String(t),r=a||{},s=r.width,i=s&&e.matchPatterns[s]||e.matchPatterns[e.defaultMatchWidth],o=n.match(i);if(!o)return null;var d,u=o[0],l=s&&e.parsePatterns[s]||e.parsePatterns[e.defaultParseWidth];return d="[object Array]"===Object.prototype.toString.call(l)?function(e,t){for(var a=0;a<e.length;a++)if(t(e[a]))return a}(l,(function(e){return e.test(u)})):function(e,t){for(var a in e)if(e.hasOwnProperty(a)&&t(e[a]))return a}(l,(function(e){return e.test(u)})),d=e.valueCallback?e.valueCallback(d):d,{value:d=r.valueCallback?r.valueCallback(d):d,rest:n.slice(u.length)}}}var I,U={code:"en-US",formatDistance:function(e,t,a){var n;return a=a||{},n="string"==typeof R[e]?R[e]:1===t?R[e].one:R[e].other.replace("{{count}}",t),a.addSuffix?a.comparison>0?"in "+n:n+" ago":n},formatLong:W,formatRelative:function(e,t,a,n){return A[e]},localize:{ordinalNumber:function(e,t){var a=Number(e),n=a%100;if(n>20||n<10)switch(n%10){case 1:return a+"st";case 2:return a+"nd";case 3:return a+"rd"}return a+"th"},era:z({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:z({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return Number(e)-1}}),month:z({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:z({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:z({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(I={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}},function(e,t){var a=String(e),n=t||{},r=a.match(I.matchPattern);if(!r)return null;var s=r[0],i=a.match(I.parsePattern);if(!i)return null;var o=I.valueCallback?I.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:a.slice(s.length)}}),era:N({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:N({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:N({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:N({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:N({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function J(e,t){x(2,arguments);var a=j(t);return P(e,-a)}function B(e,t){for(var a=e<0?"-":"",n=Math.abs(e).toString();n.length<t;)n="0"+n;return a+n}var G=function(e,t){var a=e.getUTCFullYear(),n=a>0?a:1-a;return B("yy"===t?n%100:n,t.length)},$=function(e,t){var a=e.getUTCMonth();return"M"===t?String(a+1):B(a+1,2)},V=function(e,t){return B(e.getUTCDate(),t.length)},q=function(e,t){return B(e.getUTCHours()%12||12,t.length)},Q=function(e,t){return B(e.getUTCHours(),t.length)},K=function(e,t){return B(e.getUTCMinutes(),t.length)},X=function(e,t){return B(e.getUTCSeconds(),t.length)},Z=function(e,t){var a=t.length,n=e.getUTCMilliseconds();return B(Math.floor(n*Math.pow(10,a-3)),t.length)};function ee(e){x(1,arguments);var t=1,a=H(e),n=a.getUTCDay(),r=(n<t?7:0)+n-t;return a.setUTCDate(a.getUTCDate()-r),a.setUTCHours(0,0,0,0),a}function te(e){x(1,arguments);var t=H(e),a=t.getUTCFullYear(),n=new Date(0);n.setUTCFullYear(a+1,0,4),n.setUTCHours(0,0,0,0);var r=ee(n),s=new Date(0);s.setUTCFullYear(a,0,4),s.setUTCHours(0,0,0,0);var i=ee(s);return t.getTime()>=r.getTime()?a+1:t.getTime()>=i.getTime()?a:a-1}function ae(e){x(1,arguments);var t=te(e),a=new Date(0);a.setUTCFullYear(t,0,4),a.setUTCHours(0,0,0,0);var n=ee(a);return n}function ne(e){x(1,arguments);var t=H(e),a=ee(t).getTime()-ae(t).getTime();return Math.round(a/6048e5)+1}function re(e,t){x(1,arguments);var a=t||{},n=a.locale,r=n&&n.options&&n.options.weekStartsOn,s=null==r?0:j(r),i=null==a.weekStartsOn?s:j(a.weekStartsOn);if(!(i>=0&&i<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var o=H(e),d=o.getUTCDay(),u=(d<i?7:0)+d-i;return o.setUTCDate(o.getUTCDate()-u),o.setUTCHours(0,0,0,0),o}function se(e,t){x(1,arguments);var a=H(e,t),n=a.getUTCFullYear(),r=t||{},s=r.locale,i=s&&s.options&&s.options.firstWeekContainsDate,o=null==i?1:j(i),d=null==r.firstWeekContainsDate?o:j(r.firstWeekContainsDate);if(!(d>=1&&d<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var u=new Date(0);u.setUTCFullYear(n+1,0,d),u.setUTCHours(0,0,0,0);var l=re(u,t),_=new Date(0);_.setUTCFullYear(n,0,d),_.setUTCHours(0,0,0,0);var c=re(_,t);return a.getTime()>=l.getTime()?n+1:a.getTime()>=c.getTime()?n:n-1}function ie(e,t){x(1,arguments);var a=t||{},n=a.locale,r=n&&n.options&&n.options.firstWeekContainsDate,s=null==r?1:j(r),i=null==a.firstWeekContainsDate?s:j(a.firstWeekContainsDate),o=se(e,t),d=new Date(0);d.setUTCFullYear(o,0,i),d.setUTCHours(0,0,0,0);var u=re(d,t);return u}function oe(e,t){x(1,arguments);var a=H(e),n=re(a,t).getTime()-ie(a,t).getTime();return Math.round(n/6048e5)+1}var de="midnight",ue="noon",le="morning",_e="afternoon",ce="evening",me="night",he={G:function(e,t,a){var n=e.getUTCFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return a.era(n,{width:"abbreviated"});case"GGGGG":return a.era(n,{width:"narrow"});case"GGGG":default:return a.era(n,{width:"wide"})}},y:function(e,t,a){if("yo"===t){var n=e.getUTCFullYear(),r=n>0?n:1-n;return a.ordinalNumber(r,{unit:"year"})}return G(e,t)},Y:function(e,t,a,n){var r=se(e,n),s=r>0?r:1-r;return"YY"===t?B(s%100,2):"Yo"===t?a.ordinalNumber(s,{unit:"year"}):B(s,t.length)},R:function(e,t){return B(te(e),t.length)},u:function(e,t){return B(e.getUTCFullYear(),t.length)},Q:function(e,t,a){var n=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return B(n,2);case"Qo":return a.ordinalNumber(n,{unit:"quarter"});case"QQQ":return a.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return a.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return a.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,a){var n=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return B(n,2);case"qo":return a.ordinalNumber(n,{unit:"quarter"});case"qqq":return a.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return a.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return a.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,a){var n=e.getUTCMonth();switch(t){case"M":case"MM":return $(e,t);case"Mo":return a.ordinalNumber(n+1,{unit:"month"});case"MMM":return a.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return a.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return a.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,a){var n=e.getUTCMonth();switch(t){case"L":return String(n+1);case"LL":return B(n+1,2);case"Lo":return a.ordinalNumber(n+1,{unit:"month"});case"LLL":return a.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return a.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return a.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,a,n){var r=oe(e,n);return"wo"===t?a.ordinalNumber(r,{unit:"week"}):B(r,t.length)},I:function(e,t,a){var n=ne(e);return"Io"===t?a.ordinalNumber(n,{unit:"week"}):B(n,t.length)},d:function(e,t,a){return"do"===t?a.ordinalNumber(e.getUTCDate(),{unit:"date"}):V(e,t)},D:function(e,t,a){var n=function(e){x(1,arguments);var t=H(e),a=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var n=t.getTime(),r=a-n;return Math.floor(r/864e5)+1}(e);return"Do"===t?a.ordinalNumber(n,{unit:"dayOfYear"}):B(n,t.length)},E:function(e,t,a){var n=e.getUTCDay();switch(t){case"E":case"EE":case"EEE":return a.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return a.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return a.day(n,{width:"short",context:"formatting"});case"EEEE":default:return a.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,a,n){var r=e.getUTCDay(),s=(r-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return B(s,2);case"eo":return a.ordinalNumber(s,{unit:"day"});case"eee":return a.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return a.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return a.day(r,{width:"short",context:"formatting"});case"eeee":default:return a.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,a,n){var r=e.getUTCDay(),s=(r-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return B(s,t.length);case"co":return a.ordinalNumber(s,{unit:"day"});case"ccc":return a.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return a.day(r,{width:"narrow",context:"standalone"});case"cccccc":return a.day(r,{width:"short",context:"standalone"});case"cccc":default:return a.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,a){var n=e.getUTCDay(),r=0===n?7:n;switch(t){case"i":return String(r);case"ii":return B(r,t.length);case"io":return a.ordinalNumber(r,{unit:"day"});case"iii":return a.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return a.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return a.day(n,{width:"short",context:"formatting"});case"iiii":default:return a.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,a){var n=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return a.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return a.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return a.dayPeriod(n,{width:"narrow",context:"formatting"});case"aaaa":default:return a.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,t,a){var n,r=e.getUTCHours();switch(n=12===r?ue:0===r?de:r/12>=1?"pm":"am",t){case"b":case"bb":return a.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return a.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return a.dayPeriod(n,{width:"narrow",context:"formatting"});case"bbbb":default:return a.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,t,a){var n,r=e.getUTCHours();switch(n=r>=17?ce:r>=12?_e:r>=4?le:me,t){case"B":case"BB":case"BBB":return a.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return a.dayPeriod(n,{width:"narrow",context:"formatting"});case"BBBB":default:return a.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,t,a){if("ho"===t){var n=e.getUTCHours()%12;return 0===n&&(n=12),a.ordinalNumber(n,{unit:"hour"})}return q(e,t)},H:function(e,t,a){return"Ho"===t?a.ordinalNumber(e.getUTCHours(),{unit:"hour"}):Q(e,t)},K:function(e,t,a){var n=e.getUTCHours()%12;return"Ko"===t?a.ordinalNumber(n,{unit:"hour"}):B(n,t.length)},k:function(e,t,a){var n=e.getUTCHours();return 0===n&&(n=24),"ko"===t?a.ordinalNumber(n,{unit:"hour"}):B(n,t.length)},m:function(e,t,a){return"mo"===t?a.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):K(e,t)},s:function(e,t,a){return"so"===t?a.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):X(e,t)},S:function(e,t){return Z(e,t)},X:function(e,t,a,n){var r=(n._originalDate||e).getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return pe(r);case"XXXX":case"XX":return Me(r);case"XXXXX":case"XXX":default:return Me(r,":")}},x:function(e,t,a,n){var r=(n._originalDate||e).getTimezoneOffset();switch(t){case"x":return pe(r);case"xxxx":case"xx":return Me(r);case"xxxxx":case"xxx":default:return Me(r,":")}},O:function(e,t,a,n){var r=(n._originalDate||e).getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+fe(r,":");case"OOOO":default:return"GMT"+Me(r,":")}},z:function(e,t,a,n){var r=(n._originalDate||e).getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+fe(r,":");case"zzzz":default:return"GMT"+Me(r,":")}},t:function(e,t,a,n){var r=n._originalDate||e;return B(Math.floor(r.getTime()/1e3),t.length)},T:function(e,t,a,n){return B((n._originalDate||e).getTime(),t.length)}};function fe(e,t){var a=e>0?"-":"+",n=Math.abs(e),r=Math.floor(n/60),s=n%60;if(0===s)return a+String(r);var i=t||"";return a+String(r)+i+B(s,2)}function pe(e,t){return e%60==0?(e>0?"-":"+")+B(Math.abs(e)/60,2):Me(e,t)}function Me(e,t){var a=t||"",n=e>0?"-":"+",r=Math.abs(e);return n+B(Math.floor(r/60),2)+a+B(r%60,2)}function ye(e,t){switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}}function ge(e,t){switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}}var Le={p:ge,P:function(e,t){var a,n=e.match(/(P+)(p+)?/),r=n[1],s=n[2];if(!s)return ye(e,t);switch(r){case"P":a=t.dateTime({width:"short"});break;case"PP":a=t.dateTime({width:"medium"});break;case"PPP":a=t.dateTime({width:"long"});break;case"PPPP":default:a=t.dateTime({width:"full"})}return a.replace("{{date}}",ye(r,t)).replace("{{time}}",ge(s,t))}},ve=["D","DD"],Ye=["YY","YYYY"];function we(e){return-1!==ve.indexOf(e)}function be(e){return-1!==Ye.indexOf(e)}function ke(e,t,a){if("YYYY"===e)throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(a,"`; see: https://git.io/fxCyr"));if("YY"===e)throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(a,"`; see: https://git.io/fxCyr"));if("D"===e)throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(a,"`; see: https://git.io/fxCyr"));if("DD"===e)throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(a,"`; see: https://git.io/fxCyr"))}var De=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Te=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Se=/^'([^]*?)'?$/,je=/''/g,xe=/[a-zA-Z]/;function He(e){return e.match(Se)[1].replace(je,"'")}function Pe(e,t){if(null==e)throw new TypeError("assign requires that input parameter not be null or undefined");for(var a in t=t||{})t.hasOwnProperty(a)&&(e[a]=t[a]);return e}function Oe(e,t,a){x(2,arguments);var n=a||{},r=n.locale,s=r&&r.options&&r.options.weekStartsOn,i=null==s?0:j(s),o=null==n.weekStartsOn?i:j(n.weekStartsOn);if(!(o>=0&&o<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var d=H(e),u=j(t),l=d.getUTCDay(),_=u%7,c=(_+7)%7,m=(c<o?7:0)+u-l;return d.setUTCDate(d.getUTCDate()+m),d}var Ce=/^(1[0-2]|0?\d)/,Ee=/^(3[0-1]|[0-2]?\d)/,Re=/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,Fe=/^(5[0-3]|[0-4]?\d)/,We=/^(2[0-3]|[0-1]?\d)/,Ae=/^(2[0-4]|[0-1]?\d)/,ze=/^(1[0-1]|0?\d)/,Ne=/^(1[0-2]|0?\d)/,Ie=/^[0-5]?\d/,Ue=/^[0-5]?\d/,Je=/^\d/,Be=/^\d{1,2}/,Ge=/^\d{1,3}/,$e=/^\d{1,4}/,Ve=/^-?\d+/,qe=/^-?\d/,Qe=/^-?\d{1,2}/,Ke=/^-?\d{1,3}/,Xe=/^-?\d{1,4}/,Ze=/^([+-])(\d{2})(\d{2})?|Z/,et=/^([+-])(\d{2})(\d{2})|Z/,tt=/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,at=/^([+-])(\d{2}):(\d{2})|Z/,nt=/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/;function rt(e,t,a){var n=t.match(e);if(!n)return null;var r=parseInt(n[0],10);return{value:a?a(r):r,rest:t.slice(n[0].length)}}function st(e,t){var a=t.match(e);return a?"Z"===a[0]?{value:0,rest:t.slice(1)}:{value:("+"===a[1]?1:-1)*(36e5*(a[2]?parseInt(a[2],10):0)+6e4*(a[3]?parseInt(a[3],10):0)+1e3*(a[5]?parseInt(a[5],10):0)),rest:t.slice(a[0].length)}:null}function it(e,t){return rt(Ve,e,t)}function ot(e,t,a){switch(e){case 1:return rt(Je,t,a);case 2:return rt(Be,t,a);case 3:return rt(Ge,t,a);case 4:return rt($e,t,a);default:return rt(new RegExp("^\\d{1,"+e+"}"),t,a)}}function dt(e,t,a){switch(e){case 1:return rt(qe,t,a);case 2:return rt(Qe,t,a);case 3:return rt(Ke,t,a);case 4:return rt(Xe,t,a);default:return rt(new RegExp("^-?\\d{1,"+e+"}"),t,a)}}function ut(e){switch(e){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function lt(e,t){var a,n=t>0,r=n?t:1-t;if(r<=50)a=e||100;else{var s=r+50;a=e+100*Math.floor(s/100)-(e>=s%100?100:0)}return n?a:1-a}var _t=[31,28,31,30,31,30,31,31,30,31,30,31],ct=[31,29,31,30,31,30,31,31,30,31,30,31];function mt(e){return e%400==0||e%4==0&&e%100!=0}var ht={G:{priority:140,parse:function(e,t,a,n){switch(t){case"G":case"GG":case"GGG":return a.era(e,{width:"abbreviated"})||a.era(e,{width:"narrow"});case"GGGGG":return a.era(e,{width:"narrow"});case"GGGG":default:return a.era(e,{width:"wide"})||a.era(e,{width:"abbreviated"})||a.era(e,{width:"narrow"})}},set:function(e,t,a,n){return t.era=a,e.setUTCFullYear(a,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["R","u","t","T"]},y:{priority:130,parse:function(e,t,a,n){var r=function(e){return{year:e,isTwoDigitYear:"yy"===t}};switch(t){case"y":return ot(4,e,r);case"yo":return a.ordinalNumber(e,{unit:"year",valueCallback:r});default:return ot(t.length,e,r)}},validate:function(e,t,a){return t.isTwoDigitYear||t.year>0},set:function(e,t,a,n){var r=e.getUTCFullYear();if(a.isTwoDigitYear){var s=lt(a.year,r);return e.setUTCFullYear(s,0,1),e.setUTCHours(0,0,0,0),e}var i="era"in t&&1!==t.era?1-a.year:a.year;return e.setUTCFullYear(i,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","u","w","I","i","e","c","t","T"]},Y:{priority:130,parse:function(e,t,a,n){var r=function(e){return{year:e,isTwoDigitYear:"YY"===t}};switch(t){case"Y":return ot(4,e,r);case"Yo":return a.ordinalNumber(e,{unit:"year",valueCallback:r});default:return ot(t.length,e,r)}},validate:function(e,t,a){return t.isTwoDigitYear||t.year>0},set:function(e,t,a,n){var r=se(e,n);if(a.isTwoDigitYear){var s=lt(a.year,r);return e.setUTCFullYear(s,0,n.firstWeekContainsDate),e.setUTCHours(0,0,0,0),re(e,n)}var i="era"in t&&1!==t.era?1-a.year:a.year;return e.setUTCFullYear(i,0,n.firstWeekContainsDate),e.setUTCHours(0,0,0,0),re(e,n)},incompatibleTokens:["y","R","u","Q","q","M","L","I","d","D","i","t","T"]},R:{priority:130,parse:function(e,t,a,n){return dt("R"===t?4:t.length,e)},set:function(e,t,a,n){var r=new Date(0);return r.setUTCFullYear(a,0,4),r.setUTCHours(0,0,0,0),ee(r)},incompatibleTokens:["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"]},u:{priority:130,parse:function(e,t,a,n){return dt("u"===t?4:t.length,e)},set:function(e,t,a,n){return e.setUTCFullYear(a,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["G","y","Y","R","w","I","i","e","c","t","T"]},Q:{priority:120,parse:function(e,t,a,n){switch(t){case"Q":case"QQ":return ot(t.length,e);case"Qo":return a.ordinalNumber(e,{unit:"quarter"});case"QQQ":return a.quarter(e,{width:"abbreviated",context:"formatting"})||a.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return a.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return a.quarter(e,{width:"wide",context:"formatting"})||a.quarter(e,{width:"abbreviated",context:"formatting"})||a.quarter(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,a){return t>=1&&t<=4},set:function(e,t,a,n){return e.setUTCMonth(3*(a-1),1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"]},q:{priority:120,parse:function(e,t,a,n){switch(t){case"q":case"qq":return ot(t.length,e);case"qo":return a.ordinalNumber(e,{unit:"quarter"});case"qqq":return a.quarter(e,{width:"abbreviated",context:"standalone"})||a.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return a.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return a.quarter(e,{width:"wide",context:"standalone"})||a.quarter(e,{width:"abbreviated",context:"standalone"})||a.quarter(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,a){return t>=1&&t<=4},set:function(e,t,a,n){return e.setUTCMonth(3*(a-1),1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"]},M:{priority:110,parse:function(e,t,a,n){var r=function(e){return e-1};switch(t){case"M":return rt(Ce,e,r);case"MM":return ot(2,e,r);case"Mo":return a.ordinalNumber(e,{unit:"month",valueCallback:r});case"MMM":return a.month(e,{width:"abbreviated",context:"formatting"})||a.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return a.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return a.month(e,{width:"wide",context:"formatting"})||a.month(e,{width:"abbreviated",context:"formatting"})||a.month(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,a){return t>=0&&t<=11},set:function(e,t,a,n){return e.setUTCMonth(a,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]},L:{priority:110,parse:function(e,t,a,n){var r=function(e){return e-1};switch(t){case"L":return rt(Ce,e,r);case"LL":return ot(2,e,r);case"Lo":return a.ordinalNumber(e,{unit:"month",valueCallback:r});case"LLL":return a.month(e,{width:"abbreviated",context:"standalone"})||a.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return a.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return a.month(e,{width:"wide",context:"standalone"})||a.month(e,{width:"abbreviated",context:"standalone"})||a.month(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,a){return t>=0&&t<=11},set:function(e,t,a,n){return e.setUTCMonth(a,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","M","w","I","D","i","e","c","t","T"]},w:{priority:100,parse:function(e,t,a,n){switch(t){case"w":return rt(Fe,e);case"wo":return a.ordinalNumber(e,{unit:"week"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=1&&t<=53},set:function(e,t,a,n){return re(function(e,t,a){x(2,arguments);var n=H(e),r=j(t),s=oe(n,a)-r;return n.setUTCDate(n.getUTCDate()-7*s),n}(e,a,n),n)},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","i","t","T"]},I:{priority:100,parse:function(e,t,a,n){switch(t){case"I":return rt(Fe,e);case"Io":return a.ordinalNumber(e,{unit:"week"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=1&&t<=53},set:function(e,t,a,n){return ee(function(e,t){x(2,arguments);var a=H(e),n=j(t),r=ne(a)-n;return a.setUTCDate(a.getUTCDate()-7*r),a}(e,a,n),n)},incompatibleTokens:["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"]},d:{priority:90,subPriority:1,parse:function(e,t,a,n){switch(t){case"d":return rt(Ee,e);case"do":return a.ordinalNumber(e,{unit:"date"});default:return ot(t.length,e)}},validate:function(e,t,a){var n=mt(e.getUTCFullYear()),r=e.getUTCMonth();return n?t>=1&&t<=ct[r]:t>=1&&t<=_t[r]},set:function(e,t,a,n){return e.setUTCDate(a),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","w","I","D","i","e","c","t","T"]},D:{priority:90,subPriority:1,parse:function(e,t,a,n){switch(t){case"D":case"DD":return rt(Re,e);case"Do":return a.ordinalNumber(e,{unit:"date"});default:return ot(t.length,e)}},validate:function(e,t,a){return mt(e.getUTCFullYear())?t>=1&&t<=366:t>=1&&t<=365},set:function(e,t,a,n){return e.setUTCMonth(0,a),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"]},E:{priority:90,parse:function(e,t,a,n){switch(t){case"E":case"EE":case"EEE":return a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return a.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"EEEE":default:return a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,a){return t>=0&&t<=6},set:function(e,t,a,n){return(e=Oe(e,a,n)).setUTCHours(0,0,0,0),e},incompatibleTokens:["D","i","e","c","t","T"]},e:{priority:90,parse:function(e,t,a,n){var r=function(e){var t=7*Math.floor((e-1)/7);return(e+n.weekStartsOn+6)%7+t};switch(t){case"e":case"ee":return ot(t.length,e,r);case"eo":return a.ordinalNumber(e,{unit:"day",valueCallback:r});case"eee":return a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"eeeee":return a.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"});case"eeee":default:return a.day(e,{width:"wide",context:"formatting"})||a.day(e,{width:"abbreviated",context:"formatting"})||a.day(e,{width:"short",context:"formatting"})||a.day(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,a){return t>=0&&t<=6},set:function(e,t,a,n){return(e=Oe(e,a,n)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"]},c:{priority:90,parse:function(e,t,a,n){var r=function(e){var t=7*Math.floor((e-1)/7);return(e+n.weekStartsOn+6)%7+t};switch(t){case"c":case"cc":return ot(t.length,e,r);case"co":return a.ordinalNumber(e,{unit:"day",valueCallback:r});case"ccc":return a.day(e,{width:"abbreviated",context:"standalone"})||a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"});case"ccccc":return a.day(e,{width:"narrow",context:"standalone"});case"cccccc":return a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"});case"cccc":default:return a.day(e,{width:"wide",context:"standalone"})||a.day(e,{width:"abbreviated",context:"standalone"})||a.day(e,{width:"short",context:"standalone"})||a.day(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,a){return t>=0&&t<=6},set:function(e,t,a,n){return(e=Oe(e,a,n)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"]},i:{priority:90,parse:function(e,t,a,n){var r=function(e){return 0===e?7:e};switch(t){case"i":case"ii":return ot(t.length,e);case"io":return a.ordinalNumber(e,{unit:"day"});case"iii":return a.day(e,{width:"abbreviated",context:"formatting",valueCallback:r})||a.day(e,{width:"short",context:"formatting",valueCallback:r})||a.day(e,{width:"narrow",context:"formatting",valueCallback:r});case"iiiii":return a.day(e,{width:"narrow",context:"formatting",valueCallback:r});case"iiiiii":return a.day(e,{width:"short",context:"formatting",valueCallback:r})||a.day(e,{width:"narrow",context:"formatting",valueCallback:r});case"iiii":default:return a.day(e,{width:"wide",context:"formatting",valueCallback:r})||a.day(e,{width:"abbreviated",context:"formatting",valueCallback:r})||a.day(e,{width:"short",context:"formatting",valueCallback:r})||a.day(e,{width:"narrow",context:"formatting",valueCallback:r})}},validate:function(e,t,a){return t>=1&&t<=7},set:function(e,t,a,n){return(e=function(e,t){x(2,arguments);var a=j(t);a%7==0&&(a-=7);var n=1,r=H(e),s=r.getUTCDay(),i=a%7,o=(i+7)%7,d=(o<n?7:0)+a-s;return r.setUTCDate(r.getUTCDate()+d),r}(e,a,n)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"]},a:{priority:80,parse:function(e,t,a,n){switch(t){case"a":case"aa":case"aaa":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaa":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,a,n){return e.setUTCHours(ut(a),0,0,0),e},incompatibleTokens:["b","B","H","K","k","t","T"]},b:{priority:80,parse:function(e,t,a,n){switch(t){case"b":case"bb":case"bbb":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbb":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,a,n){return e.setUTCHours(ut(a),0,0,0),e},incompatibleTokens:["a","B","H","K","k","t","T"]},B:{priority:80,parse:function(e,t,a,n){switch(t){case"B":case"BB":case"BBB":return a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return a.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBB":default:return a.dayPeriod(e,{width:"wide",context:"formatting"})||a.dayPeriod(e,{width:"abbreviated",context:"formatting"})||a.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,a,n){return e.setUTCHours(ut(a),0,0,0),e},incompatibleTokens:["a","b","t","T"]},h:{priority:70,parse:function(e,t,a,n){switch(t){case"h":return rt(Ne,e);case"ho":return a.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=1&&t<=12},set:function(e,t,a,n){var r=e.getUTCHours()>=12;return r&&a<12?e.setUTCHours(a+12,0,0,0):r||12!==a?e.setUTCHours(a,0,0,0):e.setUTCHours(0,0,0,0),e},incompatibleTokens:["H","K","k","t","T"]},H:{priority:70,parse:function(e,t,a,n){switch(t){case"H":return rt(We,e);case"Ho":return a.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=0&&t<=23},set:function(e,t,a,n){return e.setUTCHours(a,0,0,0),e},incompatibleTokens:["a","b","h","K","k","t","T"]},K:{priority:70,parse:function(e,t,a,n){switch(t){case"K":return rt(ze,e);case"Ko":return a.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=0&&t<=11},set:function(e,t,a,n){return e.getUTCHours()>=12&&a<12?e.setUTCHours(a+12,0,0,0):e.setUTCHours(a,0,0,0),e},incompatibleTokens:["a","b","h","H","k","t","T"]},k:{priority:70,parse:function(e,t,a,n){switch(t){case"k":return rt(Ae,e);case"ko":return a.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=1&&t<=24},set:function(e,t,a,n){var r=a<=24?a%24:a;return e.setUTCHours(r,0,0,0),e},incompatibleTokens:["a","b","h","H","K","t","T"]},m:{priority:60,parse:function(e,t,a,n){switch(t){case"m":return rt(Ie,e);case"mo":return a.ordinalNumber(e,{unit:"minute"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=0&&t<=59},set:function(e,t,a,n){return e.setUTCMinutes(a,0,0),e},incompatibleTokens:["t","T"]},s:{priority:50,parse:function(e,t,a,n){switch(t){case"s":return rt(Ue,e);case"so":return a.ordinalNumber(e,{unit:"second"});default:return ot(t.length,e)}},validate:function(e,t,a){return t>=0&&t<=59},set:function(e,t,a,n){return e.setUTCSeconds(a,0),e},incompatibleTokens:["t","T"]},S:{priority:30,parse:function(e,t,a,n){return ot(t.length,e,(function(e){return Math.floor(e*Math.pow(10,3-t.length))}))},set:function(e,t,a,n){return e.setUTCMilliseconds(a),e},incompatibleTokens:["t","T"]},X:{priority:10,parse:function(e,t,a,n){switch(t){case"X":return st(Ze,e);case"XX":return st(et,e);case"XXXX":return st(tt,e);case"XXXXX":return st(nt,e);case"XXX":default:return st(at,e)}},set:function(e,t,a,n){return t.timestampIsSet?e:new Date(e.getTime()-a)},incompatibleTokens:["t","T","x"]},x:{priority:10,parse:function(e,t,a,n){switch(t){case"x":return st(Ze,e);case"xx":return st(et,e);case"xxxx":return st(tt,e);case"xxxxx":return st(nt,e);case"xxx":default:return st(at,e)}},set:function(e,t,a,n){return t.timestampIsSet?e:new Date(e.getTime()-a)},incompatibleTokens:["t","T","X"]},t:{priority:40,parse:function(e,t,a,n){return it(e)},set:function(e,t,a,n){return[new Date(1e3*a),{timestampIsSet:!0}]},incompatibleTokens:"*"},T:{priority:20,parse:function(e,t,a,n){return it(e)},set:function(e,t,a,n){return[new Date(a),{timestampIsSet:!0}]},incompatibleTokens:"*"}},ft=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,pt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Mt=/^'([^]*?)'?$/,yt=/''/g,gt=/\S/,Lt=/[a-zA-Z]/;function vt(e,t,a,n){x(3,arguments);var r=String(e),s=String(t),i=n||{},o=i.locale||U;if(!o.match)throw new RangeError("locale must contain match property");var d=o.options&&o.options.firstWeekContainsDate,u=null==d?1:j(d),l=null==i.firstWeekContainsDate?u:j(i.firstWeekContainsDate);if(!(l>=1&&l<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var _=o.options&&o.options.weekStartsOn,c=null==_?0:j(_),m=null==i.weekStartsOn?c:j(i.weekStartsOn);if(!(m>=0&&m<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(""===s)return""===r?H(a):new Date(NaN);var h,f={firstWeekContainsDate:l,weekStartsOn:m,locale:o},p=[{priority:10,subPriority:-1,set:Yt,index:0}],M=s.match(pt).map((function(e){var t=e[0];return"p"===t||"P"===t?(0,Le[t])(e,o.formatLong,f):e})).join("").match(ft),y=[];for(h=0;h<M.length;h++){var g=M[h];!i.useAdditionalWeekYearTokens&&be(g)&&ke(g,s,e),!i.useAdditionalDayOfYearTokens&&we(g)&&ke(g,s,e);var L=g[0],v=ht[L];if(v){var Y=v.incompatibleTokens;if(Array.isArray(Y)){for(var w=void 0,b=0;b<y.length;b++){var k=y[b].token;if(-1!==Y.indexOf(k)||k===L){w=y[b];break}}if(w)throw new RangeError("The format string mustn't contain `".concat(w.fullToken,"` and `").concat(g,"` at the same time"))}else if("*"===v.incompatibleTokens&&y.length)throw new RangeError("The format string mustn't contain `".concat(g,"` and any other token at the same time"));y.push({token:L,fullToken:g});var D=v.parse(r,g,o.match,f);if(!D)return new Date(NaN);p.push({priority:v.priority,subPriority:v.subPriority||0,set:v.set,validate:v.validate,value:D.value,index:p.length}),r=D.rest}else{if(L.match(Lt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+L+"`");if("''"===g?g="'":"'"===L&&(g=wt(g)),0!==r.indexOf(g))return new Date(NaN);r=r.slice(g.length)}}if(r.length>0&&gt.test(r))return new Date(NaN);var T=p.map((function(e){return e.priority})).sort((function(e,t){return t-e})).filter((function(e,t,a){return a.indexOf(e)===t})).map((function(e){return p.filter((function(t){return t.priority===e})).sort((function(e,t){return t.subPriority-e.subPriority}))})).map((function(e){return e[0]})),S=H(a);if(isNaN(S))return new Date(NaN);var P=J(S,C(S)),O={};for(h=0;h<T.length;h++){var E=T[h];if(E.validate&&!E.validate(P,E.value,f))return new Date(NaN);var R=E.set(P,O,E.value,f);R[0]?(P=R[0],Pe(O,R[1])):P=R}return P}function Yt(e,t){if(t.timestampIsSet)return e;var a=new Date(0);return a.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),a.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),a}function wt(e){return e.match(Mt)[1].replace(yt,"'")}var bt=Object.assign({},v);bt.isRight=!0,bt.compare=function(e,t,a){function n(e){return a&&a.dateInputFormat?vt("".concat(e),"".concat(a.dateInputFormat),new Date):e}return e=n(e),t=n(t),E(e)?E(t)?function(e,t){x(2,arguments);var a=H(e),n=H(t),r=a.getTime()-n.getTime();return r<0?-1:r>0?1:r}(e,t):1:-1},bt.format=function(e,t){if(null==e)return"";var a=vt(e,t.dateInputFormat,new Date);return E(a)?function(e,t,a){x(2,arguments);var n=String(t),r=a||{},s=r.locale||U,i=s.options&&s.options.firstWeekContainsDate,o=null==i?1:j(i),d=null==r.firstWeekContainsDate?o:j(r.firstWeekContainsDate);if(!(d>=1&&d<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var u=s.options&&s.options.weekStartsOn,l=null==u?0:j(u),_=null==r.weekStartsOn?l:j(r.weekStartsOn);if(!(_>=0&&_<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!s.localize)throw new RangeError("locale must contain localize property");if(!s.formatLong)throw new RangeError("locale must contain formatLong property");var c=H(e);if(!E(c))throw new RangeError("Invalid time value");var m=C(c),h=J(c,m),f={firstWeekContainsDate:d,weekStartsOn:_,locale:s,_originalDate:c},p=n.match(Te).map((function(e){var t=e[0];return"p"===t||"P"===t?(0,Le[t])(e,s.formatLong,f):e})).join("").match(De).map((function(a){if("''"===a)return"'";var n=a[0];if("'"===n)return He(a);var i=he[n];if(i)return!r.useAdditionalWeekYearTokens&&be(a)&&ke(a,t,e),!r.useAdditionalDayOfYearTokens&&we(a)&&ke(a,t,e),i(h,a,s.localize,f);if(n.match(xe))throw new RangeError("Format string contains an unescaped latin alphabet character `"+n+"`");return a})).join("");return p}(a,t.dateOutputFormat):(console.error('Not a valid date: "'.concat(e,'"')),null)};var kt=Object.freeze({__proto__:null,default:bt}),Dt=Object.assign({},v);Dt.isRight=!0,Dt.filterPredicate=function(e,t){return 0===Dt.compare(e,t)},Dt.compare=function(e,t){function a(e){return null==e?-1/0:e.indexOf(".")>=0?parseFloat(e):parseInt(e,10)}return(e="number"==typeof e?e:a(e))<(t="number"==typeof t?t:a(t))?-1:e>t?1:0};var Tt=Object.freeze({__proto__:null,default:Dt}),St=Object.assign({},Dt);St.format=function(e){return null==e?"":parseFloat(Math.round(100*e)/100).toFixed(2)};var jt=Object.freeze({__proto__:null,default:St}),xt=Object.assign({},Dt);xt.format=function(e){return null==e?"":"".concat(parseFloat(100*e).toFixed(2),"%")};var Ht=Object.freeze({__proto__:null,default:xt}),Pt=Object.assign({},v);Pt.isRight=!0,Pt.filterPredicate=function(e,t){return 0===Pt.compare(e,t)},Pt.compare=function(e,t){function a(e){return"boolean"==typeof e?e?1:0:"string"==typeof e?"true"===e?1:0:-1/0}return(e=a(e))<(t=a(t))?-1:e>t?1:0};var Ot={},Ct={date:kt,decimal:jt,number:Tt,percentage:Ht,boolean:Object.freeze({__proto__:null,default:Pt})};Object.keys(Ct).forEach((function(e){var t=e.replace(/^\.\//,"").replace(/\.js/,"");Ot[t]=Ct[e].default}));var Et=Y({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{class:e.wrapStyleClasses},[e.isLoading?a("div",{staticClass:"vgt-loading vgt-center-align"},[e._t("loadingContent",[a("span",{staticClass:"vgt-loading__content"},[e._v("\n        Loading...\n      ")])])],2):e._e(),e._v(" "),a("div",{staticClass:"vgt-inner-wrap",class:{"is-loading":e.isLoading}},[e.paginate&&e.paginateOnTop?e._t("pagination-top",[a("vgt-pagination",{ref:"paginationTop",attrs:{perPage:e.perPage,rtl:e.rtl,total:e.totalRows||e.totalRowCount,mode:e.paginationMode,jumpFirstOrLast:e.paginationOptions.jumpFirstOrLast,firstText:e.firstText,lastText:e.lastText,nextText:e.nextText,prevText:e.prevText,rowsPerPageText:e.rowsPerPageText,perPageDropdownEnabled:e.paginationOptions.perPageDropdownEnabled,customRowsPerPageDropdown:e.customRowsPerPageDropdown,paginateDropdownAllowAll:e.paginateDropdownAllowAll,ofText:e.ofText,pageText:e.pageText,allText:e.allText,"info-fn":e.paginationInfoFn},on:{"page-changed":e.pageChanged,"per-page-changed":e.perPageChanged}})],{pageChanged:e.pageChanged,perPageChanged:e.perPageChanged,total:e.totalRows||e.totalRowCount}):e._e(),e._v(" "),a("vgt-global-search",{attrs:{"search-enabled":e.searchEnabled&&null==e.externalSearchQuery,"global-search-placeholder":e.searchPlaceholder},on:{"on-keyup":e.searchTableOnKeyUp,"on-enter":e.searchTableOnEnter},model:{value:e.globalSearchTerm,callback:function(t){e.globalSearchTerm=t},expression:"globalSearchTerm"}},[a("template",{slot:"internal-table-actions"},[e._t("table-actions")],2)],2),e._v(" "),e.selectedRowCount&&!e.disableSelectInfo?a("div",{staticClass:"vgt-selection-info-row clearfix",class:e.selectionInfoClass},[e._v("\n      "+e._s(e.selectionInfo)+"\n      "),a("a",{attrs:{href:""},on:{click:function(t){return t.preventDefault(),e.unselectAllInternal(!0)}}},[e._v("\n        "+e._s(e.clearSelectionText)+"\n      ")]),e._v(" "),a("div",{staticClass:"vgt-selection-info-row__actions vgt-pull-right"},[e._t("selected-row-actions")],2)]):e._e(),e._v(" "),a("div",{staticClass:"vgt-fixed-header"},[e.fixedHeader?a("table",{class:e.tableStyleClasses,attrs:{id:"vgt-table"}},[a("colgroup",e._l(e.columns,(function(e,t){return a("col",{key:t,attrs:{id:"col-"+t}})})),0),e._v(" "),a("vgt-table-header",{ref:"table-header-secondary",tag:"thead",attrs:{columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"all-selected":e.allSelected,"all-selected-indeterminate":e.allSelectedIndeterminate,mode:e.mode,sortable:e.sortable,"multiple-column-sort":e.multipleColumnSort,"typed-columns":e.typedColumns,getClasses:e.getClasses,searchEnabled:e.searchEnabled,paginated:e.paginated,"table-ref":e.$refs.table},on:{"on-toggle-select-all":e.toggleSelectAll,"on-sort-change":e.changeSort,"filter-changed":e.filterRows},scopedSlots:e._u([{key:"table-column",fn:function(t){return[e._t("table-column",[a("span",[e._v(e._s(t.column.label))])],{column:t.column})]}},{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)})],1):e._e()]),e._v(" "),a("div",{class:{"vgt-responsive":e.responsive},style:e.wrapperStyles},[a("table",{ref:"table",class:e.tableStyles,attrs:{id:"vgt-table"}},[a("colgroup",e._l(e.columns,(function(e,t){return a("col",{key:t,attrs:{id:"col-"+t}})})),0),e._v(" "),a("vgt-table-header",{ref:"table-header-primary",tag:"thead",attrs:{columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"all-selected":e.allSelected,"all-selected-indeterminate":e.allSelectedIndeterminate,mode:e.mode,sortable:e.sortable,"multiple-column-sort":e.multipleColumnSort,"typed-columns":e.typedColumns,getClasses:e.getClasses,searchEnabled:e.searchEnabled},on:{"on-toggle-select-all":e.toggleSelectAll,"on-sort-change":e.changeSort,"filter-changed":e.filterRows},scopedSlots:e._u([{key:"table-column",fn:function(t){return[e._t("table-column",[a("span",[e._v(e._s(t.column.label))])],{column:t.column})]}},{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)}),e._v(" "),e._l(e.paginated,(function(t,n){return a("tbody",{key:n},[e.groupHeaderOnTop?a("vgt-header-row",{class:e.getRowStyleClass(t),attrs:{"header-row":t,columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"select-all-by-group":e.selectAllByGroup,collapsable:e.groupOptions.collapsable,"collect-formatted":e.collectFormatted,"formatted-row":e.formattedRow,"get-classes":e.getClasses,"full-colspan":e.fullColspan,groupIndex:n},on:{vgtExpand:function(a){return e.toggleExpand(t[e.rowKeyField])},"on-select-group-change":function(a){return e.toggleSelectGroup(a,t)}},scopedSlots:e._u([{key:"table-header-row",fn:function(t){return e.hasHeaderRowTemplate?[e._t("table-header-row",null,{column:t.column,formattedRow:t.formattedRow,row:t.row})]:void 0}}],null,!0)}):e._e(),e._v(" "),e._l(t.children,(function(n,r){return!e.groupOptions.collapsable||t.vgtIsExpanded?a("tr",{key:n.originalIndex,class:e.getRowStyleClass(n),on:{mouseenter:function(t){return e.onMouseenter(n,r)},mouseleave:function(t){return e.onMouseleave(n,r)},dblclick:function(t){return e.onRowDoubleClicked(n,r,t)},click:function(t){return e.onRowClicked(n,r,t)},auxclick:function(t){return e.onRowAuxClicked(n,r,t)}}},[e.lineNumbers?a("th",{staticClass:"line-numbers"},[e._v("\n              "+e._s(e.getCurrentIndex(n.originalIndex))+"\n            ")]):e._e(),e._v(" "),e.selectable?a("th",{staticClass:"vgt-checkbox-col",on:{click:function(t){return t.stopPropagation(),e.onCheckboxClicked(n,r,t)}}},[a("input",{attrs:{type:"checkbox",disabled:n.vgtDisabled},domProps:{checked:n.vgtSelected}})]):e._e(),e._v(" "),e._l(e.columns,(function(t,s){return!t.hidden&&t.field?a("td",{key:s,class:e.getClasses(s,"td",n),attrs:{"data-label":e.compactMode?t.label:void 0},on:{click:function(a){return e.onCellClicked(n,t,r,a)}}},[e._t("table-row",[t.html?a("span",{domProps:{innerHTML:e._s(e.collect(n,t.field))}}):a("span",[e._v("\n                  "+e._s(e.collectFormatted(n,t))+"\n                ")])],{row:n,column:t,formattedRow:e.formattedRow(n),index:r})],2):e._e()}))],2):e._e()})),e._v(" "),e.groupHeaderOnBottom?a("vgt-header-row",{attrs:{"header-row":t,columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"select-all-by-group":e.selectAllByGroup,"collect-formatted":e.collectFormatted,"formatted-row":e.formattedRow,"get-classes":e.getClasses,"full-colspan":e.fullColspan,groupIndex:e.index},on:{"on-select-group-change":function(a){return e.toggleSelectGroup(a,t)}},scopedSlots:e._u([{key:"table-header-row",fn:function(t){return e.hasHeaderRowTemplate?[e._t("table-header-row",null,{column:t.column,formattedRow:t.formattedRow,row:t.row})]:void 0}}],null,!0)}):e._e()],2)})),e._v(" "),e.showEmptySlot?a("tbody",[a("tr",[a("td",{attrs:{colspan:e.fullColspan}},[e._t("emptystate",[a("div",{staticClass:"vgt-center-align vgt-text-disabled"},[e._v("\n                  No data for table\n                ")])])],2)])]):e._e()],2)]),e._v(" "),e.hasFooterSlot?a("div",{staticClass:"vgt-wrap__actions-footer"},[e._t("table-actions-bottom")],2):e._e(),e._v(" "),e.paginate&&e.paginateOnBottom?e._t("pagination-bottom",[a("vgt-pagination",{ref:"paginationBottom",attrs:{perPage:e.perPage,rtl:e.rtl,total:e.totalRows||e.totalRowCount,mode:e.paginationMode,jumpFirstOrLast:e.paginationOptions.jumpFirstOrLast,firstText:e.firstText,lastText:e.lastText,nextText:e.nextText,prevText:e.prevText,rowsPerPageText:e.rowsPerPageText,perPageDropdownEnabled:e.paginationOptions.perPageDropdownEnabled,customRowsPerPageDropdown:e.customRowsPerPageDropdown,paginateDropdownAllowAll:e.paginateDropdownAllowAll,ofText:e.ofText,pageText:e.pageText,allText:e.allText,"info-fn":e.paginationInfoFn},on:{"page-changed":e.pageChanged,"per-page-changed":e.perPageChanged}})],{pageChanged:e.pageChanged,perPageChanged:e.perPageChanged,total:e.totalRows||e.totalRowCount}):e._e()],2)])},staticRenderFns:[]},void 0,{name:"vue-good-table",props:{isLoading:{default:null,type:Boolean},maxHeight:{default:null,type:String},fixedHeader:Boolean,theme:{default:""},mode:{default:"local"},totalRows:{},styleClass:{default:"vgt-table bordered"},columns:{},rows:{},lineNumbers:Boolean,responsive:{default:!0,type:Boolean},rtl:Boolean,rowStyleClass:{default:null,type:[Function,String]},compactMode:Boolean,groupOptions:{default:function(){return{enabled:!1,collapsable:!1,rowKey:null}}},selectOptions:{default:function(){return{enabled:!1,selectionInfoClass:"",selectionText:"rows selected",clearSelectionText:"clear",disableSelectInfo:!1,selectAllByGroup:!1}}},sortOptions:{default:function(){return{enabled:!0,multipleColumns:!0,initialSortBy:{}}}},paginationOptions:{default:function(){var e;return r(e={enabled:!1,position:"bottom",perPage:10,perPageDropdown:null,perPageDropdownEnabled:!0},"position","bottom"),r(e,"dropdownAllowAll",!0),r(e,"mode","records"),r(e,"infoFn",null),r(e,"jumpFirstOrLast",!1),e}},searchOptions:{default:function(){return{enabled:!1,trigger:null,externalQuery:null,searchFn:null,placeholder:"Search Table"}}}},data:function(){return{tableLoading:!1,firstText:"First",lastText:"Last",nextText:"Next",prevText:"Previous",rowsPerPageText:"Rows per page",ofText:"of",allText:"All",pageText:"page",selectable:!1,selectOnCheckboxOnly:!1,selectAllByPage:!0,disableSelectInfo:!1,selectionInfoClass:"",selectionText:"rows selected",clearSelectionText:"clear",maintainExpanded:!0,expandedRowKeys:new Set,sortable:!0,defaultSortBy:null,multipleColumnSort:!0,searchEnabled:!1,searchTrigger:null,externalSearchQuery:null,searchFn:null,searchPlaceholder:"Search Table",searchSkipDiacritics:!1,perPage:null,paginate:!1,paginateOnTop:!1,paginateOnBottom:!0,customRowsPerPageDropdown:[],paginateDropdownAllowAll:!0,paginationMode:"records",paginationInfoFn:null,currentPage:1,currentPerPage:10,sorts:[],globalSearchTerm:"",filteredRows:[],columnFilters:{},forceSearch:!1,sortChanged:!1,dataTypes:Ot||{}}},watch:{rows:{handler:function(){this.$emit("update:isLoading",!1),this.filterRows(this.columnFilters,!1)},deep:!0,immediate:!0},selectOptions:{handler:function(){this.initializeSelect()},deep:!0,immediate:!0},paginationOptions:{handler:function(e,t){f(e,t)||this.initializePagination()},deep:!0,immediate:!0},searchOptions:{handler:function(){void 0!==this.searchOptions.externalQuery&&this.searchOptions.externalQuery!==this.searchTerm&&(this.externalSearchQuery=this.searchOptions.externalQuery,this.handleSearch()),this.initializeSearch()},deep:!0,immediate:!0},sortOptions:{handler:function(e,t){f(e,t)||this.initializeSort()},deep:!0},selectedRows:function(e,t){f(e,t)||this.$emit("on-selected-rows-change",{selectedRows:this.selectedRows})}},computed:{tableStyles:function(){return this.compactMode?this.tableStyleClasses+"vgt-compact":this.tableStyleClasses},hasFooterSlot:function(){return!!this.$slots["table-actions-bottom"]},wrapperStyles:function(){return{overflow:"scroll-y",maxHeight:this.maxHeight?this.maxHeight:"auto"}},rowKeyField:function(){return this.groupOptions.rowKey||"vgt_header_id"},hasHeaderRowTemplate:function(){return!!this.$slots["table-header-row"]||!!this.$scopedSlots["table-header-row"]},showEmptySlot:function(){return!this.paginated.length||"no groups"===this.paginated[0].label&&!this.paginated[0].children.length},allSelected:function(){return this.selectedRowCount>0&&(this.selectAllByPage&&this.selectedPageRowsCount===this.totalPageRowCount||!this.selectAllByPage&&this.selectedRowCount===this.totalRowCount)},allSelectedIndeterminate:function(){return!this.allSelected&&(this.selectAllByPage&&this.selectedPageRowsCount>0||!this.selectAllByPage&&this.selectedRowCount>0)},selectionInfo:function(){return"".concat(this.selectedRowCount," ").concat(this.selectionText)},selectedRowCount:function(){return this.selectedRows.length},selectedPageRowsCount:function(){return this.selectedPageRows.length},selectedPageRows:function(){var e=[];return this.paginated.forEach((function(t){t.children.forEach((function(t){t.vgtSelected&&e.push(t)}))})),e},selectedRows:function(){var e=[];return this.processedRows.forEach((function(t){t.children.forEach((function(t){t.vgtSelected&&e.push(t)}))})),e.sort((function(e,t){return e.originalIndex-t.originalIndex}))},fullColspan:function(){for(var e=0,t=0;t<this.columns.length;t+=1)this.columns[t].hidden||(e+=1);return this.lineNumbers&&e++,this.selectable&&e++,e},groupHeaderOnTop:function(){return!(this.groupOptions&&this.groupOptions.enabled&&this.groupOptions.headerPosition&&"bottom"===this.groupOptions.headerPosition)&&!(!this.groupOptions||!this.groupOptions.enabled)},groupHeaderOnBottom:function(){return!!(this.groupOptions&&this.groupOptions.enabled&&this.groupOptions.headerPosition&&"bottom"===this.groupOptions.headerPosition)},totalRowCount:function(){return this.processedRows.reduce((function(e,t){return e+(t.children?t.children.length:0)}),0)},totalPageRowCount:function(){return this.paginated.reduce((function(e,t){return e+(t.children?t.children.length:0)}),0)},wrapStyleClasses:function(){var e="vgt-wrap";return this.rtl&&(e+=" rtl"),e+=" ".concat(this.theme)},tableStyleClasses:function(){var e=this.styleClass;return e+=" ".concat(this.theme)},searchTerm:function(){return null!=this.externalSearchQuery?this.externalSearchQuery:this.globalSearchTerm},globalSearchAllowed:function(){return!(!this.searchEnabled||!this.globalSearchTerm||"enter"===this.searchTrigger)||(null!=this.externalSearchQuery&&"enter"!==this.searchTrigger||!!this.forceSearch&&(this.forceSearch=!1,!0))},processedRows:function(){var e=this,t=this.filteredRows;if("remote"===this.mode)return t;if(this.globalSearchAllowed){var a=[];this.filteredRows.forEach((function(e){a.push.apply(a,i(e.children))}));var n=[];a.forEach((function(t){for(var a=0;a<e.columns.length;a+=1){var r=e.columns[a];if(!r.globalSearchDisabled)if(e.searchFn){if(e.searchFn(t,r,e.collectFormatted(t,r),e.searchTerm)){n.push(t);break}}else if(v.filterPredicate(e.collectFormatted(t,r),e.searchTerm,e.searchSkipDiacritics)){n.push(t);break}}})),this.$emit("on-search",{searchTerm:this.searchTerm,rowCount:n.length}),t=[],this.filteredRows.forEach((function(e){var a=e.vgt_header_id,r=n.filter((function(e){return e.vgt_id===a}));if(r.length){var s=JSON.parse(JSON.stringify(e));s.children=r,t.push(s)}}))}return this.sorts.length&&t.forEach((function(t){t.children.sort((function(t,a){for(var n,r=0;r<e.sorts.length;r+=1){var s=e.sorts[r];if(s.type===_)n=n||t.originalIndex-a.originalIndex;else{var i=e.getColumnForField(s.field),o=e.collect(t,s.field),d=e.collect(a,s.field),u=i.sortFn;n=u&&"function"==typeof u?n||u(o,d,i,t,a)*(s.type===l?-1:1):n||i.typeDef.compare(o,d,i)*(s.type===l?-1:1)}}return n}))})),"enter"===this.searchTrigger&&(this.filteredRows=t),t},paginated:function(){var e=this;if(!this.processedRows.length)return[];if("remote"===this.mode)return this.processedRows;var t=[];if(this.processedRows.forEach((function(a){var n;e.groupOptions.enabled&&t.push(a),(n=t).push.apply(n,i(a.children))})),this.paginate){var a=(this.currentPage-1)*this.currentPerPage;(a>=t.length||-1===this.currentPerPage)&&(this.currentPage=1,a=0);var n=t.length+1;-1!==this.currentPerPage&&(n=this.currentPage*this.currentPerPage),t=t.slice(a,n)}var r=[];return t.forEach((function(t){if(void 0!==t.vgt_header_id){e.handleExpanded(t);var a=JSON.parse(JSON.stringify(t));a.children=[],r.push(a)}else{var n=r.find((function(e){return e.vgt_header_id===t.vgt_id}));n||(n=e.processedRows.find((function(e){return e.vgt_header_id===t.vgt_id})))&&((n=JSON.parse(JSON.stringify(n))).children=[],r.push(n)),n.children.push(t)}})),r},originalRows:function(){var e=this.rows&&this.rows.length?JSON.parse(JSON.stringify(this.rows)):[],t=[];t=this.groupOptions.enabled?this.handleGrouped(e):this.handleGrouped([{label:"no groups",children:e}]);var a=0;return t.forEach((function(e){e.children.forEach((function(e){e.originalIndex=a++}))})),t},typedColumns:function(){for(var e=this.columns,t=0;t<this.columns.length;t++){var a=e[t];a.typeDef=this.dataTypes[a.type]||v}return e},hasRowClickListener:function(){return this.$listeners&&this.$listeners["on-row-click"]}},methods:{handleExpanded:function(e){this.maintainExpanded&&this.expandedRowKeys.has(e[this.rowKeyField])?this.$set(e,"vgtIsExpanded",!0):this.$set(e,"vgtIsExpanded",!1)},toggleExpand:function(e){var t=this,a=this.filteredRows.find((function(a){return a[t.rowKeyField]===e}));a&&this.$set(a,"vgtIsExpanded",!a.vgtIsExpanded),this.maintainExpanded&&a.vgtIsExpanded?this.expandedRowKeys.add(a[this.rowKeyField]):this.expandedRowKeys.delete(a[this.rowKeyField])},expandAll:function(){var e=this;this.filteredRows.forEach((function(t){e.$set(t,"vgtIsExpanded",!0),e.maintainExpanded&&e.expandedRowKeys.add(t[e.rowKeyField])}))},collapseAll:function(){var e=this;this.filteredRows.forEach((function(t){e.$set(t,"vgtIsExpanded",!1),e.expandedRowKeys.clear()}))},getColumnForField:function(e){for(var t=0;t<this.typedColumns.length;t+=1)if(this.typedColumns[t].field===e)return this.typedColumns[t]},handleSearch:function(){this.resetTable(),"remote"===this.mode&&this.$emit("on-search",{searchTerm:this.searchTerm})},reset:function(){this.initializeSort(),this.changePage(1),this.$refs["table-header-primary"].reset(!0),this.$refs["table-header-secondary"]&&this.$refs["table-header-secondary"].reset(!0)},emitSelectedRows:function(){this.$emit("on-select-all",{selected:this.selectedRowCount===this.totalRowCount,selectedRows:this.selectedRows})},unselectAllInternal:function(e){var t=this;(this.selectAllByPage&&!e?this.paginated:this.filteredRows).forEach((function(e,a){e.children.forEach((function(e,a){t.$set(e,"vgtSelected",!1)}))})),this.emitSelectedRows()},toggleSelectAll:function(){var e=this;this.allSelected?this.unselectAllInternal():((this.selectAllByPage?this.paginated:this.filteredRows).forEach((function(t){t.children.forEach((function(t){e.$set(t,"vgtSelected",!0)}))})),this.emitSelectedRows())},toggleSelectGroup:function(e,t){var a=this;t.children.forEach((function(t){a.$set(t,"vgtSelected",e.checked)}))},changePage:function(e){var t=this.paginate,a=this.$refs,n=a.paginationBottom,r=a.paginationTop;t&&(this.paginateOnTop&&r&&(r.currentPage=e),this.paginateOnBottom&&n&&(n.currentPage=e),this.currentPage=e)},pageChangedEvent:function(){return{currentPage:this.currentPage,currentPerPage:this.currentPerPage,total:Math.floor(this.totalRowCount/this.currentPerPage)}},pageChanged:function(e){if(this.currentPage=e.currentPage,!e.noEmit){var t=this.pageChangedEvent();t.prevPage=e.prevPage,this.$emit("on-page-change",t),"remote"===this.mode&&this.$emit("update:isLoading",!0)}},perPageChanged:function(e){this.currentPerPage=e.currentPerPage;var t=this.paginationOptions.position;!this.$refs.paginationTop||"top"!==t&&"both"!==t||(this.$refs.paginationTop.currentPerPage=this.currentPerPage),!this.$refs.paginationBottom||"bottom"!==t&&"both"!==t||(this.$refs.paginationBottom.currentPerPage=this.currentPerPage);var a=this.pageChangedEvent();this.$emit("on-per-page-change",a),"remote"===this.mode&&this.$emit("update:isLoading",!0)},changeSort:function(e){this.sorts=e,this.$emit("on-sort-change",e),this.changePage(1),"remote"!==this.mode?this.sortChanged=!0:this.$emit("update:isLoading",!0)},onCheckboxClicked:function(e,t,a){this.$set(e,"vgtSelected",!e.vgtSelected),this.$emit("on-row-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:a})},onRowDoubleClicked:function(e,t,a){this.$emit("on-row-dblclick",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:a})},onRowClicked:function(e,t,a){this.selectable&&!this.selectOnCheckboxOnly&&this.$set(e,"vgtSelected",!e.vgtSelected),this.$emit("on-row-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:a})},onRowAuxClicked:function(e,t,a){this.$emit("on-row-aux-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:a})},onCellClicked:function(e,t,a,n){this.$emit("on-cell-click",{row:e,column:t,rowIndex:a,event:n})},onMouseenter:function(e,t){this.$emit("on-row-mouseenter",{row:e,pageIndex:t})},onMouseleave:function(e,t){this.$emit("on-row-mouseleave",{row:e,pageIndex:t})},searchTableOnEnter:function(){"enter"===this.searchTrigger&&(this.handleSearch(),this.filteredRows=JSON.parse(JSON.stringify(this.originalRows)),this.forceSearch=!0,this.sortChanged=!0)},searchTableOnKeyUp:function(){"enter"!==this.searchTrigger&&this.handleSearch()},resetTable:function(){this.unselectAllInternal(!0),this.changePage(1)},collect:function(e,t){return"function"==typeof t?t(e):"string"==typeof t?function(e,t){for(var a=e,n=t.split("."),r=0;r<n.length;r++){if(null==a)return;a=a[n[r]]}return a}(e,t):void 0},collectFormatted:function(e,t){var a,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(void 0===(a=n&&t.headerField?this.collect(e,t.headerField):this.collect(e,t.field)))return"";if(t.formatFn&&"function"==typeof t.formatFn)return t.formatFn(a,e);var r=t.typeDef;r||(r=this.dataTypes[t.type]||v);var s=r.format(a,t);return!this.compactMode||""!=s&&null!=s?s:"-"},formattedRow:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={},n=0;n<this.typedColumns.length;n++){var r=this.typedColumns[n];r.field&&(a[r.field]=this.collectFormatted(e,r,t))}return a},getClasses:function(e,t,a){var n=this.typedColumns[e],r=n.typeDef,s=n["".concat(t,"Class")],i=r.isRight;this.rtl&&(i=!0);var o={"vgt-right-align":i,"vgt-left-align":!i};return"function"==typeof s?o[s(a)]=!0:"string"==typeof s&&(o[s]=!0),o},filterRows:function(e){var t=this,a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.columnFilters=e;var r=JSON.parse(JSON.stringify(this.originalRows)),s=!1;if(this.columnFilters&&Object.keys(this.columnFilters).length){var i=function(){if(("remote"!==t.mode||a)&&t.changePage(1),a&&t.$emit("on-column-filter",{columnFilters:t.columnFilters}),"remote"===t.mode)return a?t.$emit("update:isLoading",!0):t.filteredRows=r,{v:void 0};for(var e=function(e){return"function"==typeof e&&e.name?e.name:e},i=function(a){var i=t.typedColumns[a];t.columnFilters[e(i.field)]&&(s=!0,r.forEach((function(a){var r=a.children.filter((function(a){return i.filterOptions&&"function"==typeof i.filterOptions.filterFn?i.filterOptions.filterFn(t.collect(a,i.field),t.columnFilters[e(i.field)]):i.typeDef.filterPredicate(t.collect(a,i.field),t.columnFilters[e(i.field)],!1,i.filterOptions&&"object"===n(i.filterOptions.filterDropdownItems))}));a.children=r})))},o=0;o<t.typedColumns.length;o++)i(o)}();if("object"===n(i))return i.v}this.filteredRows=s?r.filter((function(e){return e.children&&e.children.length})):r},getCurrentIndex:function(e){for(var t=0,a=!1,n=0;n<this.paginated.length;n+=1){var r=this.paginated[n].children;if(r&&r.length)for(var s=0;s<r.length;s+=1){if(r[s].originalIndex===e){a=!0;break}t+=1}if(a)break}return(this.currentPage-1)*this.currentPerPage+t+1},getRowStyleClass:function(e){var t,a="";return this.hasRowClickListener&&(a+="clickable"),(t="function"==typeof this.rowStyleClass?this.rowStyleClass(e):this.rowStyleClass)&&(a+=" ".concat(t)),a},handleGrouped:function(e){var t=this;return e.forEach((function(e,a){e.vgt_header_id=a,t.groupOptions.maintainExpanded&&t.expandedRowKeys.has(e[t.groupOptions.rowKey])&&t.$set(e,"vgtIsExpanded",!0),e.children.forEach((function(e){e.vgt_id=a}))})),e},initializePagination:function(){var e=this,t=this.paginationOptions,a=t.enabled,n=t.perPage,r=t.position,i=t.perPageDropdown,o=t.perPageDropdownEnabled,d=t.dropdownAllowAll,u=t.firstLabel,l=t.lastLabel,_=t.nextLabel,c=t.prevLabel,m=t.rowsPerPageLabel,h=t.ofLabel,f=t.pageLabel,p=t.allLabel,M=t.setCurrentPage,y=t.mode,g=t.infoFn;if("boolean"==typeof a&&(this.paginate=a),"number"==typeof n&&(this.perPage=n),"top"===r?(this.paginateOnTop=!0,this.paginateOnBottom=!1):"both"===r&&(this.paginateOnTop=!0,this.paginateOnBottom=!0),Array.isArray(i)&&i.length&&(this.customRowsPerPageDropdown=i,!this.perPage)){var L=s(i,1);this.perPage=L[0]}"boolean"==typeof o&&(this.perPageDropdownEnabled=o),"boolean"==typeof d&&(this.paginateDropdownAllowAll=d),"string"==typeof y&&(this.paginationMode=y),"string"==typeof u&&(this.firstText=u),"string"==typeof l&&(this.lastText=l),"string"==typeof _&&(this.nextText=_),"string"==typeof c&&(this.prevText=c),"string"==typeof m&&(this.rowsPerPageText=m),"string"==typeof h&&(this.ofText=h),"string"==typeof f&&(this.pageText=f),"string"==typeof p&&(this.allText=p),"number"==typeof M&&setTimeout((function(){e.changePage(M)}),500),"function"==typeof g&&(this.paginationInfoFn=g)},initializeSearch:function(){var e=this.searchOptions,t=e.enabled,a=e.trigger,n=e.externalQuery,r=e.searchFn,s=e.placeholder,i=e.skipDiacritics;"boolean"==typeof t&&(this.searchEnabled=t),"enter"===a&&(this.searchTrigger=a),"string"==typeof n&&(this.externalSearchQuery=n),"function"==typeof r&&(this.searchFn=r),"string"==typeof s&&(this.searchPlaceholder=s),"boolean"==typeof i&&(this.searchSkipDiacritics=i)},initializeSort:function(){var e=this.sortOptions,t=e.enabled,a=e.initialSortBy,r=e.multipleColumns,s=JSON.parse(JSON.stringify(a||{}));if("boolean"==typeof t&&(this.sortable=t),"boolean"==typeof r&&(this.multipleColumnSort=r),"object"===n(s)){var i=this.fixedHeader?this.$refs["table-header-secondary"]:this.$refs["table-header-primary"];if(Array.isArray(s))i.setInitialSort(s);else Object.prototype.hasOwnProperty.call(s,"field")&&i.setInitialSort([s])}},initializeSelect:function(){var e=this.selectOptions,t=e.enabled,a=e.selectionInfoClass,n=e.selectionText,r=e.clearSelectionText,s=e.selectOnCheckboxOnly,i=e.selectAllByPage,o=e.disableSelectInfo,d=e.selectAllByGroup;"boolean"==typeof t&&(this.selectable=t),"boolean"==typeof s&&(this.selectOnCheckboxOnly=s),"boolean"==typeof i&&(this.selectAllByPage=i),"boolean"==typeof d&&(this.selectAllByGroup=d),"boolean"==typeof o&&(this.disableSelectInfo=o),"string"==typeof a&&(this.selectionInfoClass=a),"string"==typeof n&&(this.selectionText=n),"string"==typeof r&&(this.clearSelectionText=r)}},mounted:function(){this.perPage&&(this.currentPerPage=this.perPage),this.initializeSort()},components:{"vgt-pagination":w,"vgt-global-search":b,"vgt-header-row":S,"vgt-table-header":T}},void 0,!1,void 0,!1,void 0,void 0,void 0),Rt={install:function(e,t){e.component(Et.name,Et)}};"undefined"!=typeof window&&window.Vue&&window.Vue.use(Rt)}).call(this,a("yLpj"))},"9rRi":function(e,t,a){!function(e){"use strict";e.defineLocale("gd",{months:["Am Faoilleach","An Gearran","Am Màrt","An Giblean","An Cèitean","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"],monthsShort:["Faoi","Gear","Màrt","Gibl","Cèit","Ògmh","Iuch","Lùn","Sult","Dàmh","Samh","Dùbh"],monthsParseExact:!0,weekdays:["Didòmhnaich","Diluain","Dimàirt","Diciadain","Diardaoin","Dihaoine","Disathairne"],weekdaysShort:["Did","Dil","Dim","Dic","Dia","Dih","Dis"],weekdaysMin:["Dò","Lu","Mà","Ci","Ar","Ha","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",ss:"%d diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){return e+(1===e?"d":e%10==2?"na":"mh")},week:{dow:1,doy:4}})}(a("wd/R"))},"A+xa":function(e,t,a){!function(e){"use strict";e.defineLocale("cv",{months:"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав".split("_"),monthsShort:"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш".split("_"),weekdays:"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун".split("_"),weekdaysShort:"выр_тун_ытл_юн_кӗҫ_эрн_шӑм".split("_"),weekdaysMin:"вр_тн_ыт_юн_кҫ_эр_шм".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]",LLL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm",LLLL:"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm"},calendar:{sameDay:"[Паян] LT [сехетре]",nextDay:"[Ыран] LT [сехетре]",lastDay:"[Ӗнер] LT [сехетре]",nextWeek:"[Ҫитес] dddd LT [сехетре]",lastWeek:"[Иртнӗ] dddd LT [сехетре]",sameElse:"L"},relativeTime:{future:function(e){return e+(/сехет$/i.exec(e)?"рен":/ҫул$/i.exec(e)?"тан":"ран")},past:"%s каялла",s:"пӗр-ик ҫеккунт",ss:"%d ҫеккунт",m:"пӗр минут",mm:"%d минут",h:"пӗр сехет",hh:"%d сехет",d:"пӗр кун",dd:"%d кун",M:"пӗр уйӑх",MM:"%d уйӑх",y:"пӗр ҫул",yy:"%d ҫул"},dayOfMonthOrdinalParse:/\d{1,2}-мӗш/,ordinal:"%d-мӗш",week:{dow:1,doy:7}})}(a("wd/R"))},AQ68:function(e,t,a){!function(e){"use strict";e.defineLocale("uz-latn",{months:"Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr".split("_"),monthsShort:"Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek".split("_"),weekdays:"Yakshanba_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba".split("_"),weekdaysShort:"Yak_Dush_Sesh_Chor_Pay_Jum_Shan".split("_"),weekdaysMin:"Ya_Du_Se_Cho_Pa_Ju_Sha".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Bugun soat] LT [da]",nextDay:"[Ertaga] LT [da]",nextWeek:"dddd [kuni soat] LT [da]",lastDay:"[Kecha soat] LT [da]",lastWeek:"[O'tgan] dddd [kuni soat] LT [da]",sameElse:"L"},relativeTime:{future:"Yaqin %s ichida",past:"Bir necha %s oldin",s:"soniya",ss:"%d soniya",m:"bir daqiqa",mm:"%d daqiqa",h:"bir soat",hh:"%d soat",d:"bir kun",dd:"%d kun",M:"bir oy",MM:"%d oy",y:"bir yil",yy:"%d yil"},week:{dow:1,doy:7}})}(a("wd/R"))},AvvY:function(e,t,a){!function(e){"use strict";e.defineLocale("ml",{months:"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ".split("_"),monthsShort:"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.".split("_"),monthsParseExact:!0,weekdays:"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച".split("_"),weekdaysShort:"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി".split("_"),weekdaysMin:"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ".split("_"),longDateFormat:{LT:"A h:mm -നു",LTS:"A h:mm:ss -നു",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm -നു",LLLL:"dddd, D MMMM YYYY, A h:mm -നു"},calendar:{sameDay:"[ഇന്ന്] LT",nextDay:"[നാളെ] LT",nextWeek:"dddd, LT",lastDay:"[ഇന്നലെ] LT",lastWeek:"[കഴിഞ്ഞ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s കഴിഞ്ഞ്",past:"%s മുൻപ്",s:"അൽപ നിമിഷങ്ങൾ",ss:"%d സെക്കൻഡ്",m:"ഒരു മിനിറ്റ്",mm:"%d മിനിറ്റ്",h:"ഒരു മണിക്കൂർ",hh:"%d മണിക്കൂർ",d:"ഒരു ദിവസം",dd:"%d ദിവസം",M:"ഒരു മാസം",MM:"%d മാസം",y:"ഒരു വർഷം",yy:"%d വർഷം"},meridiemParse:/രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,meridiemHour:function(e,t){return 12===e&&(e=0),"രാത്രി"===t&&e>=4||"ഉച്ച കഴിഞ്ഞ്"===t||"വൈകുന്നേരം"===t?e+12:e},meridiem:function(e,t,a){return e<4?"രാത്രി":e<12?"രാവിലെ":e<17?"ഉച്ച കഴിഞ്ഞ്":e<20?"വൈകുന്നേരം":"രാത്രി"}})}(a("wd/R"))},B55N:function(e,t,a){!function(e){"use strict";e.defineLocale("ja",{eras:[{since:"2019-05-01",offset:1,name:"令和",narrow:"㋿",abbr:"R"},{since:"1989-01-08",until:"2019-04-30",offset:1,name:"平成",narrow:"㍻",abbr:"H"},{since:"1926-12-25",until:"1989-01-07",offset:1,name:"昭和",narrow:"㍼",abbr:"S"},{since:"1912-07-30",until:"1926-12-24",offset:1,name:"大正",narrow:"㍽",abbr:"T"},{since:"1873-01-01",until:"1912-07-29",offset:6,name:"明治",narrow:"㍾",abbr:"M"},{since:"0001-01-01",until:"1873-12-31",offset:1,name:"西暦",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"紀元前",narrow:"BC",abbr:"BC"}],eraYearOrdinalRegex:/(元|\d+)年/,eraYearOrdinalParse:function(e,t){return"元"===t[1]?1:parseInt(t[1]||e,10)},months:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日 dddd HH:mm",l:"YYYY/MM/DD",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日(ddd) HH:mm"},meridiemParse:/午前|午後/i,isPM:function(e){return"午後"===e},meridiem:function(e,t,a){return e<12?"午前":"午後"},calendar:{sameDay:"[今日] LT",nextDay:"[明日] LT",nextWeek:function(e){return e.week()!==this.week()?"[来週]dddd LT":"dddd LT"},lastDay:"[昨日] LT",lastWeek:function(e){return this.week()!==e.week()?"[先週]dddd LT":"dddd LT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}日/,ordinal:function(e,t){switch(t){case"y":return 1===e?"元年":e+"年";case"d":case"D":case"DDD":return e+"日";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"数秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日",dd:"%d日",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}})}(a("wd/R"))},BVg3:function(e,t,a){!function(e){"use strict";function t(e){return e%100==11||e%10!=1}function a(e,a,n,r){var s=e+" ";switch(n){case"s":return a||r?"nokkrar sekúndur":"nokkrum sekúndum";case"ss":return t(e)?s+(a||r?"sekúndur":"sekúndum"):s+"sekúnda";case"m":return a?"mínúta":"mínútu";case"mm":return t(e)?s+(a||r?"mínútur":"mínútum"):a?s+"mínúta":s+"mínútu";case"hh":return t(e)?s+(a||r?"klukkustundir":"klukkustundum"):s+"klukkustund";case"d":return a?"dagur":r?"dag":"degi";case"dd":return t(e)?a?s+"dagar":s+(r?"daga":"dögum"):a?s+"dagur":s+(r?"dag":"degi");case"M":return a?"mánuður":r?"mánuð":"mánuði";case"MM":return t(e)?a?s+"mánuðir":s+(r?"mánuði":"mánuðum"):a?s+"mánuður":s+(r?"mánuð":"mánuði");case"y":return a||r?"ár":"ári";case"yy":return t(e)?s+(a||r?"ár":"árum"):s+(a||r?"ár":"ári")}}e.defineLocale("is",{months:"janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember".split("_"),monthsShort:"jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des".split("_"),weekdays:"sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur".split("_"),weekdaysShort:"sun_mán_þri_mið_fim_fös_lau".split("_"),weekdaysMin:"Su_Má_Þr_Mi_Fi_Fö_La".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd, D. MMMM YYYY [kl.] H:mm"},calendar:{sameDay:"[í dag kl.] LT",nextDay:"[á morgun kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[í gær kl.] LT",lastWeek:"[síðasta] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"eftir %s",past:"fyrir %s síðan",s:a,ss:a,m:a,mm:a,h:"klukkustund",hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},ByF4:function(e,t,a){!function(e){"use strict";e.defineLocale("fo",{months:"januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur".split("_"),weekdaysShort:"sun_mán_týs_mik_hós_frí_ley".split("_"),weekdaysMin:"su_má_tý_mi_hó_fr_le".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D. MMMM, YYYY HH:mm"},calendar:{sameDay:"[Í dag kl.] LT",nextDay:"[Í morgin kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Í gjár kl.] LT",lastWeek:"[síðstu] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"um %s",past:"%s síðani",s:"fá sekund",ss:"%d sekundir",m:"ein minuttur",mm:"%d minuttir",h:"ein tími",hh:"%d tímar",d:"ein dagur",dd:"%d dagar",M:"ein mánaður",MM:"%d mánaðir",y:"eitt ár",yy:"%d ár"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},CjzT:function(e,t,a){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),a="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),n=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es-do",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,n){return e?/-MMM-/.test(n)?a[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a("wd/R"))},CoRJ:function(e,t,a){!function(e){"use strict";e.defineLocale("ar-ma",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اثنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}})}(a("wd/R"))},"D/JM":function(e,t,a){!function(e){"use strict";e.defineLocale("eu",{months:"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"),monthsShort:"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"),monthsParseExact:!0,weekdays:"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"),weekdaysShort:"ig._al._ar._az._og._ol._lr.".split("_"),weekdaysMin:"ig_al_ar_az_og_ol_lr".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY[ko] MMMM[ren] D[a]",LLL:"YYYY[ko] MMMM[ren] D[a] HH:mm",LLLL:"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm",l:"YYYY-M-D",ll:"YYYY[ko] MMM D[a]",lll:"YYYY[ko] MMM D[a] HH:mm",llll:"ddd, YYYY[ko] MMM D[a] HH:mm"},calendar:{sameDay:"[gaur] LT[etan]",nextDay:"[bihar] LT[etan]",nextWeek:"dddd LT[etan]",lastDay:"[atzo] LT[etan]",lastWeek:"[aurreko] dddd LT[etan]",sameElse:"L"},relativeTime:{future:"%s barru",past:"duela %s",s:"segundo batzuk",ss:"%d segundo",m:"minutu bat",mm:"%d minutu",h:"ordu bat",hh:"%d ordu",d:"egun bat",dd:"%d egun",M:"hilabete bat",MM:"%d hilabete",y:"urte bat",yy:"%d urte"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(a("wd/R"))},"DKr+":function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={s:["thoddea sekondamni","thodde sekond"],ss:[e+" sekondamni",e+" sekond"],m:["eka mintan","ek minut"],mm:[e+" mintamni",e+" mintam"],h:["eka voran","ek vor"],hh:[e+" voramni",e+" voram"],d:["eka disan","ek dis"],dd:[e+" disamni",e+" dis"],M:["eka mhoinean","ek mhoino"],MM:[e+" mhoineamni",e+" mhoine"],y:["eka vorsan","ek voros"],yy:[e+" vorsamni",e+" vorsam"]};return n?r[a][0]:r[a][1]}e.defineLocale("gom-latn",{months:{standalone:"Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr".split("_"),format:"Janerachea_Febrerachea_Marsachea_Abrilachea_Maiachea_Junachea_Julaiachea_Agostachea_Setembrachea_Otubrachea_Novembrachea_Dezembrachea".split("_"),isFormat:/MMMM(\s)+D[oD]?/},monthsShort:"Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Aitar_Somar_Mongllar_Budhvar_Birestar_Sukrar_Son'var".split("_"),weekdaysShort:"Ait._Som._Mon._Bud._Bre._Suk._Son.".split("_"),weekdaysMin:"Ai_Sm_Mo_Bu_Br_Su_Sn".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [vazta]",LTS:"A h:mm:ss [vazta]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [vazta]",LLLL:"dddd, MMMM Do, YYYY, A h:mm [vazta]",llll:"ddd, D MMM YYYY, A h:mm [vazta]"},calendar:{sameDay:"[Aiz] LT",nextDay:"[Faleam] LT",nextWeek:"[Fuddlo] dddd[,] LT",lastDay:"[Kal] LT",lastWeek:"[Fattlo] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s adim",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}(er)/,ordinal:function(e,t){switch(t){case"D":return e+"er";default:case"M":case"Q":case"DDD":case"d":case"w":case"W":return e}},week:{dow:0,doy:3},meridiemParse:/rati|sokallim|donparam|sanje/,meridiemHour:function(e,t){return 12===e&&(e=0),"rati"===t?e<4?e:e+12:"sokallim"===t?e:"donparam"===t?e>12?e:e+12:"sanje"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"rati":e<12?"sokallim":e<16?"donparam":e<20?"sanje":"rati"}})}(a("wd/R"))},Dkky:function(e,t,a){!function(e){"use strict";e.defineLocale("fr-ch",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})}(a("wd/R"))},Dmvi:function(e,t,a){!function(e){"use strict";e.defineLocale("en-au",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:0,doy:4}})}(a("wd/R"))},DoHr:function(e,t,a){!function(e){"use strict";var t={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};e.defineLocale("tr",{months:"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split("_"),monthsShort:"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pzt_Sal_Çar_Per_Cum_Cmt".split("_"),weekdaysMin:"Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),meridiem:function(e,t,a){return e<12?a?"öö":"ÖÖ":a?"ös":"ÖS"},meridiemParse:/öö|ÖÖ|ös|ÖS/,isPM:function(e){return"ös"===e||"ÖS"===e},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[yarın saat] LT",nextWeek:"[gelecek] dddd [saat] LT",lastDay:"[dün] LT",lastWeek:"[geçen] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s önce",s:"birkaç saniye",ss:"%d saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",w:"bir hafta",ww:"%d hafta",M:"bir ay",MM:"%d ay",y:"bir yıl",yy:"%d yıl"},ordinal:function(e,a){switch(a){case"d":case"D":case"Do":case"DD":return e;default:if(0===e)return e+"'ıncı";var n=e%10;return e+(t[n]||t[e%100-n]||t[e>=100?100:null])}},week:{dow:1,doy:7}})}(a("wd/R"))},DxQv:function(e,t,a){!function(e){"use strict";e.defineLocale("da",{months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"søn_man_tir_ons_tor_fre_lør".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd [d.] D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"på dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[i] dddd[s kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"få sekunder",ss:"%d sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en måned",MM:"%d måneder",y:"et år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},Dzi0:function(e,t,a){!function(e){"use strict";e.defineLocale("tl-ph",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}})}(a("wd/R"))},"E+lV":function(e,t,a){!function(e){"use strict";var t={words:{ss:["секунда","секунде","секунди"],m:["један минут","једног минута"],mm:["минут","минута","минута"],h:["један сат","једног сата"],hh:["сат","сата","сати"],d:["један дан","једног дана"],dd:["дан","дана","дана"],M:["један месец","једног месеца"],MM:["месец","месеца","месеци"],y:["једну годину","једне године"],yy:["годину","године","година"]},correctGrammaticalCase:function(e,t){return e%10>=1&&e%10<=4&&(e%100<10||e%100>=20)?e%10==1?t[0]:t[1]:t[2]},translate:function(e,a,n,r){var s,i=t.words[n];return 1===n.length?"y"===n&&a?"једна година":r||a?i[0]:i[1]:(s=t.correctGrammaticalCase(e,i),"yy"===n&&a&&"годину"===s?e+" година":e+" "+s)}};e.defineLocale("sr-cyrl",{months:"јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар".split("_"),monthsShort:"јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.".split("_"),monthsParseExact:!0,weekdays:"недеља_понедељак_уторак_среда_четвртак_петак_субота".split("_"),weekdaysShort:"нед._пон._уто._сре._чет._пет._суб.".split("_"),weekdaysMin:"не_по_ут_ср_че_пе_су".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D. M. YYYY.",LL:"D. MMMM YYYY.",LLL:"D. MMMM YYYY. H:mm",LLLL:"dddd, D. MMMM YYYY. H:mm"},calendar:{sameDay:"[данас у] LT",nextDay:"[сутра у] LT",nextWeek:function(){switch(this.day()){case 0:return"[у] [недељу] [у] LT";case 3:return"[у] [среду] [у] LT";case 6:return"[у] [суботу] [у] LT";case 1:case 2:case 4:case 5:return"[у] dddd [у] LT"}},lastDay:"[јуче у] LT",lastWeek:function(){return["[прошле] [недеље] [у] LT","[прошлог] [понедељка] [у] LT","[прошлог] [уторка] [у] LT","[прошле] [среде] [у] LT","[прошлог] [четвртка] [у] LT","[прошлог] [петка] [у] LT","[прошле] [суботе] [у] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"за %s",past:"пре %s",s:"неколико секунди",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:t.translate,dd:t.translate,M:t.translate,MM:t.translate,y:t.translate,yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(a("wd/R"))},EOgW:function(e,t,a){!function(e){"use strict";e.defineLocale("th",{months:"มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),monthsShort:"ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.".split("_"),monthsParseExact:!0,weekdays:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),weekdaysShort:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),weekdaysMin:"อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY เวลา H:mm",LLLL:"วันddddที่ D MMMM YYYY เวลา H:mm"},meridiemParse:/ก่อนเที่ยง|หลังเที่ยง/,isPM:function(e){return"หลังเที่ยง"===e},meridiem:function(e,t,a){return e<12?"ก่อนเที่ยง":"หลังเที่ยง"},calendar:{sameDay:"[วันนี้ เวลา] LT",nextDay:"[พรุ่งนี้ เวลา] LT",nextWeek:"dddd[หน้า เวลา] LT",lastDay:"[เมื่อวานนี้ เวลา] LT",lastWeek:"[วัน]dddd[ที่แล้ว เวลา] LT",sameElse:"L"},relativeTime:{future:"อีก %s",past:"%sที่แล้ว",s:"ไม่กี่วินาที",ss:"%d วินาที",m:"1 นาที",mm:"%d นาที",h:"1 ชั่วโมง",hh:"%d ชั่วโมง",d:"1 วัน",dd:"%d วัน",w:"1 สัปดาห์",ww:"%d สัปดาห์",M:"1 เดือน",MM:"%d เดือน",y:"1 ปี",yy:"%d ปี"}})}(a("wd/R"))},Ed67:function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));var n=a("XuX8"),r=a.n(n),s=a("tC49"),i=a("xjcK"),o=a("pyNs"),d=a("z3V6"),u=Object(d.d)({id:Object(d.c)(o.t),inline:Object(d.c)(o.g,!1),novalidate:Object(d.c)(o.g,!1),validated:Object(d.c)(o.g,!1)},i.v),l=r.a.extend({name:i.v,functional:!0,props:u,render:function(e,t){var a=t.props,n=t.data,r=t.children;return e("form",Object(s.a)(n,{class:{"form-inline":a.inline,"was-validated":a.validated},attrs:{id:a.id,novalidate:a.novalidate}}),r)}})},Fnuy:function(e,t,a){!function(e){"use strict";e.defineLocale("oc-lnc",{months:{standalone:"genièr_febrièr_març_abril_mai_junh_julhet_agost_setembre_octòbre_novembre_decembre".split("_"),format:"de genièr_de febrièr_de març_d'abril_de mai_de junh_de julhet_d'agost_de setembre_d'octòbre_de novembre_de decembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._mai_junh_julh._ago._set._oct._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"dimenge_diluns_dimars_dimècres_dijòus_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dm._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dm_dc_dj_dv_ds".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:"[uèi a] LT",nextDay:"[deman a] LT",nextWeek:"dddd [a] LT",lastDay:"[ièr a] LT",lastWeek:"dddd [passat a] LT",sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"unas segondas",ss:"%d segondas",m:"una minuta",mm:"%d minutas",h:"una ora",hh:"%d oras",d:"un jorn",dd:"%d jorns",M:"un mes",MM:"%d meses",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|è|a)/,ordinal:function(e,t){var a=1===e?"r":2===e?"n":3===e?"r":4===e?"t":"è";return"w"!==t&&"W"!==t||(a="a"),e+a},week:{dow:1,doy:4}})}(a("wd/R"))},G0Uy:function(e,t,a){!function(e){"use strict";e.defineLocale("mt",{months:"Jannar_Frar_Marzu_April_Mejju_Ġunju_Lulju_Awwissu_Settembru_Ottubru_Novembru_Diċembru".split("_"),monthsShort:"Jan_Fra_Mar_Apr_Mej_Ġun_Lul_Aww_Set_Ott_Nov_Diċ".split("_"),weekdays:"Il-Ħadd_It-Tnejn_It-Tlieta_L-Erbgħa_Il-Ħamis_Il-Ġimgħa_Is-Sibt".split("_"),weekdaysShort:"Ħad_Tne_Tli_Erb_Ħam_Ġim_Sib".split("_"),weekdaysMin:"Ħa_Tn_Tl_Er_Ħa_Ġi_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Illum fil-]LT",nextDay:"[Għada fil-]LT",nextWeek:"dddd [fil-]LT",lastDay:"[Il-bieraħ fil-]LT",lastWeek:"dddd [li għadda] [fil-]LT",sameElse:"L"},relativeTime:{future:"f’ %s",past:"%s ilu",s:"ftit sekondi",ss:"%d sekondi",m:"minuta",mm:"%d minuti",h:"siegħa",hh:"%d siegħat",d:"ġurnata",dd:"%d ġranet",M:"xahar",MM:"%d xhur",y:"sena",yy:"%d sni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a("wd/R"))},H8ED:function(e,t,a){!function(e){"use strict";function t(e,t,a){var n,r;return"m"===a?t?"хвіліна":"хвіліну":"h"===a?t?"гадзіна":"гадзіну":e+" "+(n=+e,r={ss:t?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:t?"хвіліна_хвіліны_хвілін":"хвіліну_хвіліны_хвілін",hh:t?"гадзіна_гадзіны_гадзін":"гадзіну_гадзіны_гадзін",dd:"дзень_дні_дзён",MM:"месяц_месяцы_месяцаў",yy:"год_гады_гадоў"}[a].split("_"),n%10==1&&n%100!=11?r[0]:n%10>=2&&n%10<=4&&(n%100<10||n%100>=20)?r[1]:r[2])}e.defineLocale("be",{months:{format:"студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня".split("_"),standalone:"студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань".split("_")},monthsShort:"студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж".split("_"),weekdays:{format:"нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу".split("_"),standalone:"нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота".split("_"),isFormat:/\[ ?[Ууў] ?(?:мінулую|наступную)? ?\] ?dddd/},weekdaysShort:"нд_пн_ат_ср_чц_пт_сб".split("_"),weekdaysMin:"нд_пн_ат_ср_чц_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., HH:mm",LLLL:"dddd, D MMMM YYYY г., HH:mm"},calendar:{sameDay:"[Сёння ў] LT",nextDay:"[Заўтра ў] LT",lastDay:"[Учора ў] LT",nextWeek:function(){return"[У] dddd [ў] LT"},lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return"[У мінулую] dddd [ў] LT";case 1:case 2:case 4:return"[У мінулы] dddd [ў] LT"}},sameElse:"L"},relativeTime:{future:"праз %s",past:"%s таму",s:"некалькі секунд",m:t,mm:t,h:t,hh:t,d:"дзень",dd:t,M:"месяц",MM:t,y:"год",yy:t},meridiemParse:/ночы|раніцы|дня|вечара/,isPM:function(e){return/^(дня|вечара)$/.test(e)},meridiem:function(e,t,a){return e<4?"ночы":e<12?"раніцы":e<17?"дня":"вечара"},dayOfMonthOrdinalParse:/\d{1,2}-(і|ы|га)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e%10!=2&&e%10!=3||e%100==12||e%100==13?e+"-ы":e+"-і";case"D":return e+"-га";default:return e}},week:{dow:1,doy:7}})}(a("wd/R"))},HP3h:function(e,t,a){!function(e){"use strict";var t={1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",0:"0"},a=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},n={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},r=function(e){return function(t,r,s,i){var o=a(t),d=n[e][a(t)];return 2===o&&(d=d[r?0:1]),d.replace(/%d/i,t)}},s=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];e.defineLocale("ar-ly",{months:s,monthsShort:s,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,a){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:r("s"),ss:r("s"),m:r("m"),mm:r("m"),h:r("h"),hh:r("h"),d:r("d"),dd:r("d"),M:r("M"),MM:r("M"),y:r("y"),yy:r("y")},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}})}(a("wd/R"))},IBtZ:function(e,t,a){!function(e){"use strict";e.defineLocale("ka",{months:"იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი".split("_"),monthsShort:"იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ".split("_"),weekdays:{standalone:"კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი".split("_"),format:"კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს".split("_"),isFormat:/(წინა|შემდეგ)/},weekdaysShort:"კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ".split("_"),weekdaysMin:"კვ_ორ_სა_ოთ_ხუ_პა_შა".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[დღეს] LT[-ზე]",nextDay:"[ხვალ] LT[-ზე]",lastDay:"[გუშინ] LT[-ზე]",nextWeek:"[შემდეგ] dddd LT[-ზე]",lastWeek:"[წინა] dddd LT-ზე",sameElse:"L"},relativeTime:{future:function(e){return e.replace(/(წამ|წუთ|საათ|წელ|დღ|თვ)(ი|ე)/,(function(e,t,a){return"ი"===a?t+"ში":t+a+"ში"}))},past:function(e){return/(წამი|წუთი|საათი|დღე|თვე)/.test(e)?e.replace(/(ი|ე)$/,"ის წინ"):/წელი/.test(e)?e.replace(/წელი$/,"წლის წინ"):e},s:"რამდენიმე წამი",ss:"%d წამი",m:"წუთი",mm:"%d წუთი",h:"საათი",hh:"%d საათი",d:"დღე",dd:"%d დღე",M:"თვე",MM:"%d თვე",y:"წელი",yy:"%d წელი"},dayOfMonthOrdinalParse:/0|1-ლი|მე-\d{1,2}|\d{1,2}-ე/,ordinal:function(e){return 0===e?e:1===e?e+"-ლი":e<20||e<=100&&e%20==0||e%100==0?"მე-"+e:e+"-ე"},week:{dow:1,doy:7}})}(a("wd/R"))},"Ivi+":function(e,t,a){!function(e){"use strict";e.defineLocale("ko",{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 A h:mm",LLLL:"YYYY년 MMMM D일 dddd A h:mm",l:"YYYY.MM.DD.",ll:"YYYY년 MMMM D일",lll:"YYYY년 MMMM D일 A h:mm",llll:"YYYY년 MMMM D일 dddd A h:mm"},calendar:{sameDay:"오늘 LT",nextDay:"내일 LT",nextWeek:"dddd LT",lastDay:"어제 LT",lastWeek:"지난주 dddd LT",sameElse:"L"},relativeTime:{future:"%s 후",past:"%s 전",s:"몇 초",ss:"%d초",m:"1분",mm:"%d분",h:"한 시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한 달",MM:"%d달",y:"일 년",yy:"%d년"},dayOfMonthOrdinalParse:/\d{1,2}(일|월|주)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"일";case"M":return e+"월";case"w":case"W":return e+"주";default:return e}},meridiemParse:/오전|오후/,isPM:function(e){return"오후"===e},meridiem:function(e,t,a){return e<12?"오전":"오후"}})}(a("wd/R"))},"JCF/":function(e,t,a){!function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},a={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},n=["کانونی دووەم","شوبات","ئازار","نیسان","ئایار","حوزەیران","تەمموز","ئاب","ئەیلوول","تشرینی یەكەم","تشرینی دووەم","كانونی یەکەم"];e.defineLocale("ku",{months:n,monthsShort:n,weekdays:"یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌".split("_"),weekdaysShort:"یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌".split("_"),weekdaysMin:"ی_د_س_چ_پ_ه_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ئێواره‌|به‌یانی/,isPM:function(e){return/ئێواره‌/.test(e)},meridiem:function(e,t,a){return e<12?"به‌یانی":"ئێواره‌"},calendar:{sameDay:"[ئه‌مرۆ كاتژمێر] LT",nextDay:"[به‌یانی كاتژمێر] LT",nextWeek:"dddd [كاتژمێر] LT",lastDay:"[دوێنێ كاتژمێر] LT",lastWeek:"dddd [كاتژمێر] LT",sameElse:"L"},relativeTime:{future:"له‌ %s",past:"%s",s:"چه‌ند چركه‌یه‌ك",ss:"چركه‌ %d",m:"یه‌ك خوله‌ك",mm:"%d خوله‌ك",h:"یه‌ك كاتژمێر",hh:"%d كاتژمێر",d:"یه‌ك ڕۆژ",dd:"%d ڕۆژ",M:"یه‌ك مانگ",MM:"%d مانگ",y:"یه‌ك ساڵ",yy:"%d ساڵ"},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return a[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}})}(a("wd/R"))},JVSJ:function(e,t,a){!function(e){"use strict";function t(e,t,a){var n=e+" ";switch(a){case"ss":return n+=1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi";case"m":return t?"jedna minuta":"jedne minute";case"mm":return n+=1===e?"minuta":2===e||3===e||4===e?"minute":"minuta";case"h":return t?"jedan sat":"jednog sata";case"hh":return n+=1===e?"sat":2===e||3===e||4===e?"sata":"sati";case"dd":return n+=1===e?"dan":"dana";case"MM":return n+=1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci";case"yy":return n+=1===e?"godina":2===e||3===e||4===e?"godine":"godina"}}e.defineLocale("bs",{months:"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:t,m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(a("wd/R"))},JtJI:function(e,t,a){"use strict";a.d(t,"a",(function(){return J}));var n,r=a("XuX8"),s=a.n(r),i=a("xjcK"),o=a("AFYn"),d=a("pyNs"),u=a("bUBZ"),l=a("kGy3"),_=a("ex6f"),c=a("qMhD"),m=a("OljW"),h=a("2C+6"),f=a("z3V6"),p=a("m/oX"),M=a("m3aq"),y=a("Iyau"),g=a("a3f1"),L=a("WPLV"),v=a("+nMp"),Y=a("aGvM"),w=a("jBgq"),b=a("qlm0");function k(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function D(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?k(Object(a),!0).forEach((function(t){T(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):k(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function T(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var S=Object(L.a)("value",{type:d.i,defaultValue:null,validator:function(e){return!(!Object(_.g)(e)&&Object(m.b)(e,0)<1)||(Object(Y.a)('"v-model" value must be a number greater than "0"',i.eb),!1)}}),j=S.mixin,x=S.props,H=S.prop,P=S.event,O=function(e){var t=Object(m.b)(e)||1;return t<1?5:t},C=function(e,t){var a=Object(m.b)(e)||1;return a>t?t:a<1?1:a},E=function(e){if(e.keyCode===p.j)return Object(g.f)(e,{immediatePropagation:!0}),e.currentTarget.click(),!1},R=Object(f.d)(Object(h.m)(D(D({},x),{},{align:Object(f.c)(d.t,"left"),ariaLabel:Object(f.c)(d.t,"Pagination"),disabled:Object(f.c)(d.g,!1),ellipsisClass:Object(f.c)(d.e),ellipsisText:Object(f.c)(d.t,"…"),firstClass:Object(f.c)(d.e),firstNumber:Object(f.c)(d.g,!1),firstText:Object(f.c)(d.t,"«"),hideEllipsis:Object(f.c)(d.g,!1),hideGotoEndButtons:Object(f.c)(d.g,!1),labelFirstPage:Object(f.c)(d.t,"Go to first page"),labelLastPage:Object(f.c)(d.t,"Go to last page"),labelNextPage:Object(f.c)(d.t,"Go to next page"),labelPage:Object(f.c)(d.l,"Go to page"),labelPrevPage:Object(f.c)(d.t,"Go to previous page"),lastClass:Object(f.c)(d.e),lastNumber:Object(f.c)(d.g,!1),lastText:Object(f.c)(d.t,"»"),limit:Object(f.c)(d.o,5,(function(e){return!(Object(m.b)(e,0)<1)||(Object(Y.a)('Prop "limit" must be a number greater than "0"',i.eb),!1)})),nextClass:Object(f.c)(d.e),nextText:Object(f.c)(d.t,"›"),pageClass:Object(f.c)(d.e),pills:Object(f.c)(d.g,!1),prevClass:Object(f.c)(d.e),prevText:Object(f.c)(d.t,"‹"),size:Object(f.c)(d.t)})),"pagination"),F=s.a.extend({mixins:[j,w.a],props:R,data:function(){var e=Object(m.b)(this[H],0);return{currentPage:e=e>0?e:-1,localNumberOfPages:1,localLimit:5}},computed:{btnSize:function(){var e=this.size;return e?"pagination-".concat(e):""},alignment:function(){var e=this.align;return"center"===e?"justify-content-center":"end"===e||"right"===e?"justify-content-end":"fill"===e?"text-center":""},styleClass:function(){return this.pills?"b-pagination-pills":""},computedCurrentPage:function(){return C(this.currentPage,this.localNumberOfPages)},paginationParams:function(){var e=this.localLimit,t=this.localNumberOfPages,a=this.computedCurrentPage,n=this.hideEllipsis,r=this.firstNumber,s=this.lastNumber,i=!1,o=!1,d=e,u=1;t<=e?d=t:a<e-1&&e>3?(n&&!s||(o=!0,d=e-(r?0:1)),d=Object(c.d)(d,e)):t-a+2<e&&e>3?(n&&!r||(i=!0,d=e-(s?0:1)),u=t-d+1):(e>3&&(d=e-(n?0:2),i=!(n&&!r),o=!(n&&!s)),u=a-Object(c.b)(d/2)),u<1?(u=1,i=!1):u>t-d&&(u=t-d+1,o=!1),i&&r&&u<4&&(d+=2,u=1,i=!1);var l=u+d-1;return o&&s&&l>t-3&&(d+=l===t-2?2:3,o=!1),e<=3&&(r&&1===u?d=Object(c.d)(d+1,t,e+1):s&&t===u+d-1&&(u=Object(c.c)(u-1,1),d=Object(c.d)(t-u+1,t,e+1))),{showFirstDots:i,showLastDots:o,numberOfLinks:d=Object(c.d)(d,t-u+1),startNumber:u}},pageList:function(){var e=this.paginationParams,t=e.numberOfLinks,a=e.startNumber,n=this.computedCurrentPage,r=function(e,t){return Object(y.c)(t,(function(t,a){return{number:e+a,classes:null}}))}(a,t);if(r.length>3){var s=n-a,i="bv-d-xs-down-none";if(0===s)for(var o=3;o<r.length;o++)r[o].classes=i;else if(s===r.length-1)for(var d=0;d<r.length-3;d++)r[d].classes=i;else{for(var u=0;u<s-1;u++)r[u].classes=i;for(var l=r.length-1;l>s+1;l--)r[l].classes=i}}return r}},watch:(n={},T(n,H,(function(e,t){e!==t&&(this.currentPage=C(e,this.localNumberOfPages))})),T(n,"currentPage",(function(e,t){e!==t&&this.$emit(P,e>0?e:null)})),T(n,"limit",(function(e,t){e!==t&&(this.localLimit=O(e))})),n),created:function(){var e=this;this.localLimit=O(this.limit),this.$nextTick((function(){e.currentPage=e.currentPage>e.localNumberOfPages?e.localNumberOfPages:e.currentPage}))},methods:{handleKeyNav:function(e){var t=e.keyCode,a=e.shiftKey;this.isNav||(t===p.f||t===p.k?(Object(g.f)(e,{propagation:!1}),a?this.focusFirst():this.focusPrev()):t!==p.i&&t!==p.a||(Object(g.f)(e,{propagation:!1}),a?this.focusLast():this.focusNext()))},getButtons:function(){return Object(l.D)("button.page-link, a.page-link",this.$el).filter((function(e){return Object(l.u)(e)}))},focusCurrent:function(){var e=this;this.$nextTick((function(){var t=e.getButtons().find((function(t){return Object(m.b)(Object(l.h)(t,"aria-posinset"),0)===e.computedCurrentPage}));Object(l.d)(t)||e.focusFirst()}))},focusFirst:function(){var e=this;this.$nextTick((function(){var t=e.getButtons().find((function(e){return!Object(l.r)(e)}));Object(l.d)(t)}))},focusLast:function(){var e=this;this.$nextTick((function(){var t=e.getButtons().reverse().find((function(e){return!Object(l.r)(e)}));Object(l.d)(t)}))},focusPrev:function(){var e=this;this.$nextTick((function(){var t=e.getButtons(),a=t.indexOf(Object(l.g)());a>0&&!Object(l.r)(t[a-1])&&Object(l.d)(t[a-1])}))},focusNext:function(){var e=this;this.$nextTick((function(){var t=e.getButtons(),a=t.indexOf(Object(l.g)());a<t.length-1&&!Object(l.r)(t[a+1])&&Object(l.d)(t[a+1])}))}},render:function(e){var t=this,a=this.disabled,n=this.labelPage,r=this.ariaLabel,s=this.isNav,i=this.localNumberOfPages,o=this.computedCurrentPage,d=this.pageList.map((function(e){return e.number})),u=this.paginationParams,l=u.showFirstDots,c=u.showLastDots,m="fill"===this.align,h=[],p=function(e){return e===o},y=this.currentPage<1,g=function(n,r,o,d,u,l,_){var c=a||p(l)||y||n<1||n>i,h=n<1?1:n>i?i:n,f={disabled:c,page:h,index:h-1},M=t.normalizeSlot(o,f)||Object(v.g)(d)||e(),g=e(c?"span":s?b.a:"button",{staticClass:"page-link",class:{"flex-grow-1":!s&&!c&&m},props:c||!s?{}:t.linkProps(n),attrs:{role:s?null:"menuitem",type:s||c?null:"button",tabindex:c||s?null:"-1","aria-label":r,"aria-controls":t.ariaControls||null,"aria-disabled":c?"true":null},on:c?{}:{"!click":function(e){t.onClick(e,n)},keydown:E}},[M]);return e("li",{key:_,staticClass:"page-item",class:[{disabled:c,"flex-fill":m,"d-flex":m&&!s&&!c},u],attrs:{role:s?null:"presentation","aria-hidden":c?"true":null}},[g])},L=function(a){return e("li",{staticClass:"page-item",class:["disabled","bv-d-xs-down-none",m?"flex-fill":"",t.ellipsisClass],attrs:{role:"separator"},key:"ellipsis-".concat(a?"last":"first")},[e("span",{staticClass:"page-link"},[t.normalizeSlot(M.k)||Object(v.g)(t.ellipsisText)||e()])])},Y=function(r,o){var d=r.number,u=p(d)&&!y,l=a?null:u||y&&0===o?"0":"-1",c={role:s?null:"menuitemradio",type:s||a?null:"button","aria-disabled":a?"true":null,"aria-controls":t.ariaControls||null,"aria-label":Object(f.b)(n)?n(d):"".concat(Object(_.f)(n)?n():n," ").concat(d),"aria-checked":s?null:u?"true":"false","aria-current":s&&u?"page":null,"aria-posinset":s?null:d,"aria-setsize":s?null:i,tabindex:s?null:l},h=Object(v.g)(t.makePage(d)),g={page:d,index:d-1,content:h,active:u,disabled:a},L=e(a?"span":s?b.a:"button",{props:a||!s?{}:t.linkProps(d),staticClass:"page-link",class:{"flex-grow-1":!s&&!a&&m},attrs:c,on:a?{}:{"!click":function(e){t.onClick(e,d)},keydown:E}},[t.normalizeSlot(M.F,g)||h]);return e("li",{staticClass:"page-item",class:[{disabled:a,active:u,"flex-fill":m,"d-flex":m&&!s&&!a},r.classes,t.pageClass],attrs:{role:s?null:"presentation"},key:"page-".concat(d)},[L])},w=e();this.firstNumber||this.hideGotoEndButtons||(w=g(1,this.labelFirstPage,M.p,this.firstText,this.firstClass,1,"pagination-goto-first")),h.push(w),h.push(g(o-1,this.labelPrevPage,M.I,this.prevText,this.prevClass,1,"pagination-goto-prev")),h.push(this.firstNumber&&1!==d[0]?Y({number:1},0):e()),h.push(l?L(!1):e()),this.pageList.forEach((function(e,a){var n=l&&t.firstNumber&&1!==d[0]?1:0;h.push(Y(e,a+n))})),h.push(c?L(!0):e()),h.push(this.lastNumber&&d[d.length-1]!==i?Y({number:i},-1):e()),h.push(g(o+1,this.labelNextPage,M.E,this.nextText,this.nextClass,i,"pagination-goto-next"));var k=e();this.lastNumber||this.hideGotoEndButtons||(k=g(i,this.labelLastPage,M.w,this.lastText,this.lastClass,i,"pagination-goto-last")),h.push(k);var D=e("ul",{staticClass:"pagination",class:["b-pagination",this.btnSize,this.alignment,this.styleClass],attrs:{role:s?null:"menubar","aria-disabled":a?"true":"false","aria-label":s?null:r||null},on:s?{}:{keydown:this.handleKeyNav},ref:"ul"},h);return s?e("nav",{attrs:{"aria-disabled":a?"true":null,"aria-hidden":a?"true":"false","aria-label":s&&r||null}},[D]):D}});function W(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function A(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?W(Object(a),!0).forEach((function(t){z(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):W(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function z(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var N=function(e){return Object(c.c)(Object(m.b)(e)||20,1)},I=function(e){return Object(c.c)(Object(m.b)(e)||0,0)},U=Object(f.d)(Object(h.m)(A(A({},R),{},{ariaControls:Object(f.c)(d.t),perPage:Object(f.c)(d.o,20),totalRows:Object(f.c)(d.o,0)})),i.eb),J=s.a.extend({name:i.eb,mixins:[F],props:U,computed:{numberOfPages:function(){var e=Object(c.a)(I(this.totalRows)/N(this.perPage));return e<1?1:e},pageSizeNumberOfPages:function(){return{perPage:N(this.perPage),totalRows:I(this.totalRows),numberOfPages:this.numberOfPages}}},watch:{pageSizeNumberOfPages:function(e,t){Object(_.p)(t)||(e.perPage!==t.perPage&&e.totalRows===t.totalRows||e.numberOfPages!==t.numberOfPages&&this.currentPage>e.numberOfPages)&&(this.currentPage=1),this.localNumberOfPages=e.numberOfPages}},created:function(){var e=this;this.localNumberOfPages=this.numberOfPages;var t=Object(m.b)(this[H],0);t>0?this.currentPage=t:this.$nextTick((function(){e.currentPage=0}))},methods:{onClick:function(e,t){var a=this;if(t!==this.currentPage){var n=e.target,r=new u.a(o.C,{cancelable:!0,vueTarget:this,target:n});this.$emit(r.type,r,t),r.defaultPrevented||(this.currentPage=t,this.$emit(o.d,this.currentPage),this.$nextTick((function(){Object(l.u)(n)&&a.$el.contains(n)?Object(l.d)(n):a.focusCurrent()})))}},makePage:function(e){return e},linkProps:function(){return{}}}})},JvlW:function(e,t,a){!function(e){"use strict";var t={ss:"sekundė_sekundžių_sekundes",m:"minutė_minutės_minutę",mm:"minutės_minučių_minutes",h:"valanda_valandos_valandą",hh:"valandos_valandų_valandas",d:"diena_dienos_dieną",dd:"dienos_dienų_dienas",M:"mėnuo_mėnesio_mėnesį",MM:"mėnesiai_mėnesių_mėnesius",y:"metai_metų_metus",yy:"metai_metų_metus"};function a(e,t,a,n){return t?r(a)[0]:n?r(a)[1]:r(a)[2]}function n(e){return e%10==0||e>10&&e<20}function r(e){return t[e].split("_")}function s(e,t,s,i){var o=e+" ";return 1===e?o+a(0,t,s[0],i):t?o+(n(e)?r(s)[1]:r(s)[0]):i?o+r(s)[1]:o+(n(e)?r(s)[1]:r(s)[2])}e.defineLocale("lt",{months:{format:"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio".split("_"),standalone:"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis".split("_"),isFormat:/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?|MMMM?(\[[^\[\]]*\]|\s)+D[oD]?/},monthsShort:"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd".split("_"),weekdays:{format:"sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį".split("_"),standalone:"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis".split("_"),isFormat:/dddd HH:mm/},weekdaysShort:"Sek_Pir_Ant_Tre_Ket_Pen_Šeš".split("_"),weekdaysMin:"S_P_A_T_K_Pn_Š".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY [m.] MMMM D [d.]",LLL:"YYYY [m.] MMMM D [d.], HH:mm [val.]",LLLL:"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]",l:"YYYY-MM-DD",ll:"YYYY [m.] MMMM D [d.]",lll:"YYYY [m.] MMMM D [d.], HH:mm [val.]",llll:"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]"},calendar:{sameDay:"[Šiandien] LT",nextDay:"[Rytoj] LT",nextWeek:"dddd LT",lastDay:"[Vakar] LT",lastWeek:"[Praėjusį] dddd LT",sameElse:"L"},relativeTime:{future:"po %s",past:"prieš %s",s:function(e,t,a,n){return t?"kelios sekundės":n?"kelių sekundžių":"kelias sekundes"},ss:s,m:a,mm:s,h:a,hh:s,d:a,dd:s,M:a,MM:s,y:a,yy:s},dayOfMonthOrdinalParse:/\d{1,2}-oji/,ordinal:function(e){return e+"-oji"},week:{dow:1,doy:4}})}(a("wd/R"))},"K/tc":function(e,t,a){!function(e){"use strict";e.defineLocale("af",{months:"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des".split("_"),weekdays:"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag".split("_"),weekdaysShort:"Son_Maa_Din_Woe_Don_Vry_Sat".split("_"),weekdaysMin:"So_Ma_Di_Wo_Do_Vr_Sa".split("_"),meridiemParse:/vm|nm/i,isPM:function(e){return/^nm$/i.test(e)},meridiem:function(e,t,a){return e<12?a?"vm":"VM":a?"nm":"NM"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Vandag om] LT",nextDay:"[Môre om] LT",nextWeek:"dddd [om] LT",lastDay:"[Gister om] LT",lastWeek:"[Laas] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oor %s",past:"%s gelede",s:"'n paar sekondes",ss:"%d sekondes",m:"'n minuut",mm:"%d minute",h:"'n uur",hh:"%d ure",d:"'n dag",dd:"%d dae",M:"'n maand",MM:"%d maande",y:"'n jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(a("wd/R"))},KSF8:function(e,t,a){!function(e){"use strict";e.defineLocale("vi",{months:"tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12".split("_"),monthsShort:"Thg 01_Thg 02_Thg 03_Thg 04_Thg 05_Thg 06_Thg 07_Thg 08_Thg 09_Thg 10_Thg 11_Thg 12".split("_"),monthsParseExact:!0,weekdays:"chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy".split("_"),weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysParseExact:!0,meridiemParse:/sa|ch/i,isPM:function(e){return/^ch$/i.test(e)},meridiem:function(e,t,a){return e<12?a?"sa":"SA":a?"ch":"CH"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [năm] YYYY",LLL:"D MMMM [năm] YYYY HH:mm",LLLL:"dddd, D MMMM [năm] YYYY HH:mm",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[Hôm nay lúc] LT",nextDay:"[Ngày mai lúc] LT",nextWeek:"dddd [tuần tới lúc] LT",lastDay:"[Hôm qua lúc] LT",lastWeek:"dddd [tuần trước lúc] LT",sameElse:"L"},relativeTime:{future:"%s tới",past:"%s trước",s:"vài giây",ss:"%d giây",m:"một phút",mm:"%d phút",h:"một giờ",hh:"%d giờ",d:"một ngày",dd:"%d ngày",w:"một tuần",ww:"%d tuần",M:"một tháng",MM:"%d tháng",y:"một năm",yy:"%d năm"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}})}(a("wd/R"))},KTz0:function(e,t,a){!function(e){"use strict";var t={words:{ss:["sekund","sekunda","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mjesec","mjeseca","mjeseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(e,t){return 1===e?t[0]:e>=2&&e<=4?t[1]:t[2]},translate:function(e,a,n){var r=t.words[n];return 1===n.length?a?r[0]:r[1]:e+" "+t.correctGrammaticalCase(e,r)}};e.defineLocale("me",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sjutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedjelje] [u] LT","[prošlog] [ponedjeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srijede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"nekoliko sekundi",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:"dan",dd:t.translate,M:"mjesec",MM:t.translate,y:"godinu",yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(a("wd/R"))},Loxo:function(e,t,a){!function(e){"use strict";e.defineLocale("uz",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба".split("_"),weekdaysShort:"Якш_Душ_Сеш_Чор_Пай_Жум_Шан".split("_"),weekdaysMin:"Як_Ду_Се_Чо_Па_Жу_Ша".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Бугун соат] LT [да]",nextDay:"[Эртага] LT [да]",nextWeek:"dddd [куни соат] LT [да]",lastDay:"[Кеча соат] LT [да]",lastWeek:"[Утган] dddd [куни соат] LT [да]",sameElse:"L"},relativeTime:{future:"Якин %s ичида",past:"Бир неча %s олдин",s:"фурсат",ss:"%d фурсат",m:"бир дакика",mm:"%d дакика",h:"бир соат",hh:"%d соат",d:"бир кун",dd:"%d кун",M:"бир ой",MM:"%d ой",y:"бир йил",yy:"%d йил"},week:{dow:1,doy:7}})}(a("wd/R"))},OIYi:function(e,t,a){!function(e){"use strict";e.defineLocale("en-ca",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"YYYY-MM-DD",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}})}(a("wd/R"))},Oaa7:function(e,t,a){!function(e){"use strict";e.defineLocale("en-gb",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(a("wd/R"))},Ob0Z:function(e,t,a){!function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},a={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};function n(e,t,a,n){var r="";if(t)switch(a){case"s":r="काही सेकंद";break;case"ss":r="%d सेकंद";break;case"m":r="एक मिनिट";break;case"mm":r="%d मिनिटे";break;case"h":r="एक तास";break;case"hh":r="%d तास";break;case"d":r="एक दिवस";break;case"dd":r="%d दिवस";break;case"M":r="एक महिना";break;case"MM":r="%d महिने";break;case"y":r="एक वर्ष";break;case"yy":r="%d वर्षे"}else switch(a){case"s":r="काही सेकंदां";break;case"ss":r="%d सेकंदां";break;case"m":r="एका मिनिटा";break;case"mm":r="%d मिनिटां";break;case"h":r="एका तासा";break;case"hh":r="%d तासां";break;case"d":r="एका दिवसा";break;case"dd":r="%d दिवसां";break;case"M":r="एका महिन्या";break;case"MM":r="%d महिन्यां";break;case"y":r="एका वर्षा";break;case"yy":r="%d वर्षां"}return r.replace(/%d/i,e)}e.defineLocale("mr",{months:"जानेवारी_फेब्रुवारी_मार्च_एप्रिल_मे_जून_जुलै_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),monthsShort:"जाने._फेब्रु._मार्च._एप्रि._मे._जून._जुलै._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:!0,weekdays:"रविवार_सोमवार_मंगळवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगळ_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm वाजता",LTS:"A h:mm:ss वाजता",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm वाजता",LLLL:"dddd, D MMMM YYYY, A h:mm वाजता"},calendar:{sameDay:"[आज] LT",nextDay:"[उद्या] LT",nextWeek:"dddd, LT",lastDay:"[काल] LT",lastWeek:"[मागील] dddd, LT",sameElse:"L"},relativeTime:{future:"%sमध्ये",past:"%sपूर्वी",s:n,ss:n,m:n,mm:n,h:n,hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/पहाटे|सकाळी|दुपारी|सायंकाळी|रात्री/,meridiemHour:function(e,t){return 12===e&&(e=0),"पहाटे"===t||"सकाळी"===t?e:"दुपारी"===t||"सायंकाळी"===t||"रात्री"===t?e>=12?e:e+12:void 0},meridiem:function(e,t,a){return e>=0&&e<6?"पहाटे":e<12?"सकाळी":e<17?"दुपारी":e<20?"सायंकाळी":"रात्री"},week:{dow:0,doy:6}})}(a("wd/R"))},Obrr:function(e,t,a){"use strict";a.d(t,"a",(function(){return x}));var n=a("xjcK"),r=a("6GPe"),s=a("AFYn"),i=a("m/oX"),o=a("mS7b"),d=a("Iyau"),u=a("kGy3"),l=a("a3f1"),_=a("ex6f"),c=a("PCFI"),m=a("2C+6"),h="__BV_toggle",f="".concat(h,"_HANDLER__"),p="".concat(h,"_CLICK__"),M="".concat(h,"_STATE__"),y="".concat(h,"_TARGETS__"),g=Object(l.d)(n.q,"toggle"),L=Object(l.e)(n.q,"state"),v=Object(l.e)(n.q,"sync-state"),Y=Object(l.d)(n.q,"request-state"),w=[i.c,i.j],b=function(e){return!Object(d.a)(["button","a"],e.tagName.toLowerCase())},k=function(e){var t=e[p];t&&(Object(l.a)(e,"click",t,s.T),Object(l.a)(e,"keydown",t,s.T)),e[p]=null},D=function(e,t){e[f]&&t.context&&t.context.$root.$off([L,v],e[f]),e[f]=null},T=function(e,t){t?(Object(u.y)(e,"collapsed"),Object(u.b)(e,"not-collapsed"),Object(u.E)(e,"aria-expanded","true")):(Object(u.y)(e,"not-collapsed"),Object(u.b)(e,"collapsed"),Object(u.E)(e,"aria-expanded","false"))},S=function(e,t){e[t]=null,delete e[t]},j=function(e,t,a){if(r.f&&a.context){b(e)&&(Object(u.o)(e,"role")||Object(u.E)(e,"role","button"),Object(u.o)(e,"tabindex")||Object(u.E)(e,"tabindex","0")),T(e,e[M]);var n=function(e,t){var a=e.modifiers,n=e.arg,r=e.value,s=Object(m.h)(a||{});if(r=Object(_.n)(r)?r.split(o.s):r,Object(u.t)(t.tagName,"a")){var i=Object(u.h)(t,"href")||"";o.i.test(i)&&s.push(i.replace(o.h,""))}return Object(d.b)(n,r).forEach((function(e){return Object(_.n)(e)&&s.push(e)})),s.filter((function(e,t,a){return e&&a.indexOf(e)===t}))}(t,e);n.length>0?(Object(u.E)(e,"aria-controls",n.join(" ")),Object(u.F)(e,"overflow-anchor","none")):(Object(u.x)(e,"aria-controls"),Object(u.A)(e,"overflow-anchor")),Object(u.B)((function(){!function(e,t){if(k(e),t.context){var a=function(a){"keydown"===a.type&&!Object(d.a)(w,a.keyCode)||Object(u.r)(e)||(e[y]||[]).forEach((function(e){t.context.$root.$emit(g,e)}))};e[p]=a,Object(l.b)(e,"click",a,s.T),b(e)&&Object(l.b)(e,"keydown",a,s.T)}}(e,a)})),Object(c.a)(n,e[y])||(e[y]=n,n.forEach((function(e){a.context.$root.$emit(Y,e)})))}},x={bind:function(e,t,a){e[M]=!1,e[y]=[],function(e,t){if(D(e,t),t.context){var a=function(t,a){Object(d.a)(e[y]||[],t)&&(e[M]=a,T(e,a))};e[f]=a,t.context.$root.$on([L,v],a)}}(e,a),j(e,t,a)},componentUpdated:j,updated:j,unbind:function(e,t,a){k(e),D(e,a),S(e,f),S(e,p),S(e,M),S(e,y),Object(u.y)(e,"collapsed"),Object(u.y)(e,"not-collapsed"),Object(u.x)(e,"aria-expanded"),Object(u.x)(e,"aria-controls"),Object(u.x)(e,"role"),Object(u.A)(e,"overflow-anchor")}}},OjkT:function(e,t,a){!function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},a={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};e.defineLocale("ne",{months:"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मई_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर".split("_"),monthsShort:"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.".split("_"),monthsParseExact:!0,weekdays:"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार".split("_"),weekdaysShort:"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.".split("_"),weekdaysMin:"आ._सो._मं._बु._बि._शु._श.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"Aको h:mm बजे",LTS:"Aको h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, Aको h:mm बजे",LLLL:"dddd, D MMMM YYYY, Aको h:mm बजे"},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/राति|बिहान|दिउँसो|साँझ/,meridiemHour:function(e,t){return 12===e&&(e=0),"राति"===t?e<4?e:e+12:"बिहान"===t?e:"दिउँसो"===t?e>=10?e:e+12:"साँझ"===t?e+12:void 0},meridiem:function(e,t,a){return e<3?"राति":e<12?"बिहान":e<16?"दिउँसो":e<20?"साँझ":"राति"},calendar:{sameDay:"[आज] LT",nextDay:"[भोलि] LT",nextWeek:"[आउँदो] dddd[,] LT",lastDay:"[हिजो] LT",lastWeek:"[गएको] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%sमा",past:"%s अगाडि",s:"केही क्षण",ss:"%d सेकेण्ड",m:"एक मिनेट",mm:"%d मिनेट",h:"एक घण्टा",hh:"%d घण्टा",d:"एक दिन",dd:"%d दिन",M:"एक महिना",MM:"%d महिना",y:"एक बर्ष",yy:"%d बर्ष"},week:{dow:0,doy:6}})}(a("wd/R"))},OmwH:function(e,t,a){!function(e){"use strict";e.defineLocale("zh-mo",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"D/M/YYYY",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,a){var n=100*e+t;return n<600?"凌晨":n<900?"早上":n<1130?"上午":n<1230?"中午":n<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})}(a("wd/R"))},Oxv6:function(e,t,a){!function(e){"use strict";var t={0:"-ум",1:"-ум",2:"-юм",3:"-юм",4:"-ум",5:"-ум",6:"-ум",7:"-ум",8:"-ум",9:"-ум",10:"-ум",12:"-ум",13:"-ум",20:"-ум",30:"-юм",40:"-ум",50:"-ум",60:"-ум",70:"-ум",80:"-ум",90:"-ум",100:"-ум"};e.defineLocale("tg",{months:{format:"январи_феврали_марти_апрели_майи_июни_июли_августи_сентябри_октябри_ноябри_декабри".split("_"),standalone:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_")},monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе".split("_"),weekdaysShort:"яшб_дшб_сшб_чшб_пшб_ҷум_шнб".split("_"),weekdaysMin:"яш_дш_сш_чш_пш_ҷм_шб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Имрӯз соати] LT",nextDay:"[Фардо соати] LT",lastDay:"[Дирӯз соати] LT",nextWeek:"dddd[и] [ҳафтаи оянда соати] LT",lastWeek:"dddd[и] [ҳафтаи гузашта соати] LT",sameElse:"L"},relativeTime:{future:"баъди %s",past:"%s пеш",s:"якчанд сония",m:"як дақиқа",mm:"%d дақиқа",h:"як соат",hh:"%d соат",d:"як рӯз",dd:"%d рӯз",M:"як моҳ",MM:"%d моҳ",y:"як сол",yy:"%d сол"},meridiemParse:/шаб|субҳ|рӯз|бегоҳ/,meridiemHour:function(e,t){return 12===e&&(e=0),"шаб"===t?e<4?e:e+12:"субҳ"===t?e:"рӯз"===t?e>=11?e:e+12:"бегоҳ"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"шаб":e<11?"субҳ":e<16?"рӯз":e<19?"бегоҳ":"шаб"},dayOfMonthOrdinalParse:/\d{1,2}-(ум|юм)/,ordinal:function(e){return e+(t[e]||t[e%10]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(a("wd/R"))},PA2r:function(e,t,a){!function(e){"use strict";var t={format:"leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_"),standalone:"ledna_února_března_dubna_května_června_července_srpna_září_října_listopadu_prosince".split("_")},a="led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_"),n=[/^led/i,/^úno/i,/^bře/i,/^dub/i,/^kvě/i,/^(čvn|červen$|června)/i,/^(čvc|červenec|července)/i,/^srp/i,/^zář/i,/^říj/i,/^lis/i,/^pro/i],r=/^(leden|únor|březen|duben|květen|červenec|července|červen|června|srpen|září|říjen|listopad|prosinec|led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i;function s(e){return e>1&&e<5&&1!=~~(e/10)}function i(e,t,a,n){var r=e+" ";switch(a){case"s":return t||n?"pár sekund":"pár sekundami";case"ss":return t||n?r+(s(e)?"sekundy":"sekund"):r+"sekundami";case"m":return t?"minuta":n?"minutu":"minutou";case"mm":return t||n?r+(s(e)?"minuty":"minut"):r+"minutami";case"h":return t?"hodina":n?"hodinu":"hodinou";case"hh":return t||n?r+(s(e)?"hodiny":"hodin"):r+"hodinami";case"d":return t||n?"den":"dnem";case"dd":return t||n?r+(s(e)?"dny":"dní"):r+"dny";case"M":return t||n?"měsíc":"měsícem";case"MM":return t||n?r+(s(e)?"měsíce":"měsíců"):r+"měsíci";case"y":return t||n?"rok":"rokem";case"yy":return t||n?r+(s(e)?"roky":"let"):r+"lety"}}e.defineLocale("cs",{months:t,monthsShort:a,monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(leden|ledna|února|únor|březen|března|duben|dubna|květen|května|červenec|července|červen|června|srpen|srpna|září|říjen|října|listopadu|listopad|prosinec|prosince)/i,monthsShortStrictRegex:/^(led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"),weekdaysShort:"ne_po_út_st_čt_pá_so".split("_"),weekdaysMin:"ne_po_út_st_čt_pá_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},calendar:{sameDay:"[dnes v] LT",nextDay:"[zítra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v neděli v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve středu v] LT";case 4:return"[ve čtvrtek v] LT";case 5:return"[v pátek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[včera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou neděli v] LT";case 1:case 2:return"[minulé] dddd [v] LT";case 3:return"[minulou středu v] LT";case 4:case 5:return"[minulý] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"před %s",s:i,ss:i,m:i,mm:i,h:i,hh:i,d:i,dd:i,M:i,MM:i,y:i,yy:i},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},PeUW:function(e,t,a){!function(e){"use strict";var t={1:"௧",2:"௨",3:"௩",4:"௪",5:"௫",6:"௬",7:"௭",8:"௮",9:"௯",0:"௦"},a={"௧":"1","௨":"2","௩":"3","௪":"4","௫":"5","௬":"6","௭":"7","௮":"8","௯":"9","௦":"0"};e.defineLocale("ta",{months:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),monthsShort:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),weekdays:"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை".split("_"),weekdaysShort:"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி".split("_"),weekdaysMin:"ஞா_தி_செ_பு_வி_வெ_ச".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, HH:mm",LLLL:"dddd, D MMMM YYYY, HH:mm"},calendar:{sameDay:"[இன்று] LT",nextDay:"[நாளை] LT",nextWeek:"dddd, LT",lastDay:"[நேற்று] LT",lastWeek:"[கடந்த வாரம்] dddd, LT",sameElse:"L"},relativeTime:{future:"%s இல்",past:"%s முன்",s:"ஒரு சில விநாடிகள்",ss:"%d விநாடிகள்",m:"ஒரு நிமிடம்",mm:"%d நிமிடங்கள்",h:"ஒரு மணி நேரம்",hh:"%d மணி நேரம்",d:"ஒரு நாள்",dd:"%d நாட்கள்",M:"ஒரு மாதம்",MM:"%d மாதங்கள்",y:"ஒரு வருடம்",yy:"%d ஆண்டுகள்"},dayOfMonthOrdinalParse:/\d{1,2}வது/,ordinal:function(e){return e+"வது"},preparse:function(e){return e.replace(/[௧௨௩௪௫௬௭௮௯௦]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,meridiem:function(e,t,a){return e<2?" யாமம்":e<6?" வைகறை":e<10?" காலை":e<14?" நண்பகல்":e<18?" எற்பாடு":e<22?" மாலை":" யாமம்"},meridiemHour:function(e,t){return 12===e&&(e=0),"யாமம்"===t?e<2?e:e+12:"வைகறை"===t||"காலை"===t||"நண்பகல்"===t&&e>=10?e:e+12},week:{dow:0,doy:6}})}(a("wd/R"))},PpIw:function(e,t,a){!function(e){"use strict";var t={1:"೧",2:"೨",3:"೩",4:"೪",5:"೫",6:"೬",7:"೭",8:"೮",9:"೯",0:"೦"},a={"೧":"1","೨":"2","೩":"3","೪":"4","೫":"5","೬":"6","೭":"7","೮":"8","೯":"9","೦":"0"};e.defineLocale("kn",{months:"ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್".split("_"),monthsShort:"ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ".split("_"),monthsParseExact:!0,weekdays:"ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ".split("_"),weekdaysShort:"ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ".split("_"),weekdaysMin:"ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[ಇಂದು] LT",nextDay:"[ನಾಳೆ] LT",nextWeek:"dddd, LT",lastDay:"[ನಿನ್ನೆ] LT",lastWeek:"[ಕೊನೆಯ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ನಂತರ",past:"%s ಹಿಂದೆ",s:"ಕೆಲವು ಕ್ಷಣಗಳು",ss:"%d ಸೆಕೆಂಡುಗಳು",m:"ಒಂದು ನಿಮಿಷ",mm:"%d ನಿಮಿಷ",h:"ಒಂದು ಗಂಟೆ",hh:"%d ಗಂಟೆ",d:"ಒಂದು ದಿನ",dd:"%d ದಿನ",M:"ಒಂದು ತಿಂಗಳು",MM:"%d ತಿಂಗಳು",y:"ಒಂದು ವರ್ಷ",yy:"%d ವರ್ಷ"},preparse:function(e){return e.replace(/[೧೨೩೪೫೬೭೮೯೦]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/ರಾತ್ರಿ|ಬೆಳಿಗ್ಗೆ|ಮಧ್ಯಾಹ್ನ|ಸಂಜೆ/,meridiemHour:function(e,t){return 12===e&&(e=0),"ರಾತ್ರಿ"===t?e<4?e:e+12:"ಬೆಳಿಗ್ಗೆ"===t?e:"ಮಧ್ಯಾಹ್ನ"===t?e>=10?e:e+12:"ಸಂಜೆ"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"ರಾತ್ರಿ":e<10?"ಬೆಳಿಗ್ಗೆ":e<17?"ಮಧ್ಯಾಹ್ನ":e<20?"ಸಂಜೆ":"ರಾತ್ರಿ"},dayOfMonthOrdinalParse:/\d{1,2}(ನೇ)/,ordinal:function(e){return e+"ನೇ"},week:{dow:0,doy:6}})}(a("wd/R"))},Qj4J:function(e,t,a){!function(e){"use strict";e.defineLocale("ar-kw",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:12}})}(a("wd/R"))},RAwQ:function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={m:["eng Minutt","enger Minutt"],h:["eng Stonn","enger Stonn"],d:["een Dag","engem Dag"],M:["ee Mount","engem Mount"],y:["ee Joer","engem Joer"]};return t?r[a][0]:r[a][1]}function a(e){if(e=parseInt(e,10),isNaN(e))return!1;if(e<0)return!0;if(e<10)return 4<=e&&e<=7;if(e<100){var t=e%10;return a(0===t?e/10:t)}if(e<1e4){for(;e>=10;)e/=10;return a(e)}return a(e/=1e3)}e.defineLocale("lb",{months:"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg".split("_"),weekdaysShort:"So._Mé._Dë._Më._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mé_Dë_Më_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm [Auer]",LTS:"H:mm:ss [Auer]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm [Auer]",LLLL:"dddd, D. MMMM YYYY H:mm [Auer]"},calendar:{sameDay:"[Haut um] LT",sameElse:"L",nextDay:"[Muer um] LT",nextWeek:"dddd [um] LT",lastDay:"[Gëschter um] LT",lastWeek:function(){switch(this.day()){case 2:case 4:return"[Leschten] dddd [um] LT";default:return"[Leschte] dddd [um] LT"}}},relativeTime:{future:function(e){return a(e.substr(0,e.indexOf(" ")))?"a "+e:"an "+e},past:function(e){return a(e.substr(0,e.indexOf(" ")))?"viru "+e:"virun "+e},s:"e puer Sekonnen",ss:"%d Sekonnen",m:t,mm:"%d Minutten",h:t,hh:"%d Stonnen",d:t,dd:"%d Deeg",M:t,MM:"%d Méint",y:t,yy:"%d Joer"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},RnhZ:function(e,t,a){var n={"./af":"K/tc","./af.js":"K/tc","./ar":"jnO4","./ar-dz":"o1bE","./ar-dz.js":"o1bE","./ar-kw":"Qj4J","./ar-kw.js":"Qj4J","./ar-ly":"HP3h","./ar-ly.js":"HP3h","./ar-ma":"CoRJ","./ar-ma.js":"CoRJ","./ar-sa":"gjCT","./ar-sa.js":"gjCT","./ar-tn":"bYM6","./ar-tn.js":"bYM6","./ar.js":"jnO4","./az":"SFxW","./az.js":"SFxW","./be":"H8ED","./be.js":"H8ED","./bg":"hKrs","./bg.js":"hKrs","./bm":"p/rL","./bm.js":"p/rL","./bn":"kEOa","./bn-bd":"loYQ","./bn-bd.js":"loYQ","./bn.js":"kEOa","./bo":"0mo+","./bo.js":"0mo+","./br":"aIdf","./br.js":"aIdf","./bs":"JVSJ","./bs.js":"JVSJ","./ca":"1xZ4","./ca.js":"1xZ4","./cs":"PA2r","./cs.js":"PA2r","./cv":"A+xa","./cv.js":"A+xa","./cy":"l5ep","./cy.js":"l5ep","./da":"DxQv","./da.js":"DxQv","./de":"tGlX","./de-at":"s+uk","./de-at.js":"s+uk","./de-ch":"u3GI","./de-ch.js":"u3GI","./de.js":"tGlX","./dv":"WYrj","./dv.js":"WYrj","./el":"jUeY","./el.js":"jUeY","./en-au":"Dmvi","./en-au.js":"Dmvi","./en-ca":"OIYi","./en-ca.js":"OIYi","./en-gb":"Oaa7","./en-gb.js":"Oaa7","./en-ie":"4dOw","./en-ie.js":"4dOw","./en-il":"czMo","./en-il.js":"czMo","./en-in":"7C5Q","./en-in.js":"7C5Q","./en-nz":"b1Dy","./en-nz.js":"b1Dy","./en-sg":"t+mt","./en-sg.js":"t+mt","./eo":"Zduo","./eo.js":"Zduo","./es":"iYuL","./es-do":"CjzT","./es-do.js":"CjzT","./es-mx":"tbfe","./es-mx.js":"tbfe","./es-us":"Vclq","./es-us.js":"Vclq","./es.js":"iYuL","./et":"7BjC","./et.js":"7BjC","./eu":"D/JM","./eu.js":"D/JM","./fa":"jfSC","./fa.js":"jfSC","./fi":"gekB","./fi.js":"gekB","./fil":"1ppg","./fil.js":"1ppg","./fo":"ByF4","./fo.js":"ByF4","./fr":"nyYc","./fr-ca":"2fjn","./fr-ca.js":"2fjn","./fr-ch":"Dkky","./fr-ch.js":"Dkky","./fr.js":"nyYc","./fy":"cRix","./fy.js":"cRix","./ga":"USCx","./ga.js":"USCx","./gd":"9rRi","./gd.js":"9rRi","./gl":"iEDd","./gl.js":"iEDd","./gom-deva":"qvJo","./gom-deva.js":"qvJo","./gom-latn":"DKr+","./gom-latn.js":"DKr+","./gu":"4MV3","./gu.js":"4MV3","./he":"x6pH","./he.js":"x6pH","./hi":"3E1r","./hi.js":"3E1r","./hr":"S6ln","./hr.js":"S6ln","./hu":"WxRl","./hu.js":"WxRl","./hy-am":"1rYy","./hy-am.js":"1rYy","./id":"UDhR","./id.js":"UDhR","./is":"BVg3","./is.js":"BVg3","./it":"bpih","./it-ch":"bxKX","./it-ch.js":"bxKX","./it.js":"bpih","./ja":"B55N","./ja.js":"B55N","./jv":"tUCv","./jv.js":"tUCv","./ka":"IBtZ","./ka.js":"IBtZ","./kk":"bXm7","./kk.js":"bXm7","./km":"6B0Y","./km.js":"6B0Y","./kn":"PpIw","./kn.js":"PpIw","./ko":"Ivi+","./ko.js":"Ivi+","./ku":"JCF/","./ku.js":"JCF/","./ky":"lgnt","./ky.js":"lgnt","./lb":"RAwQ","./lb.js":"RAwQ","./lo":"sp3z","./lo.js":"sp3z","./lt":"JvlW","./lt.js":"JvlW","./lv":"uXwI","./lv.js":"uXwI","./me":"KTz0","./me.js":"KTz0","./mi":"aIsn","./mi.js":"aIsn","./mk":"aQkU","./mk.js":"aQkU","./ml":"AvvY","./ml.js":"AvvY","./mn":"lYtQ","./mn.js":"lYtQ","./mr":"Ob0Z","./mr.js":"Ob0Z","./ms":"6+QB","./ms-my":"ZAMP","./ms-my.js":"ZAMP","./ms.js":"6+QB","./mt":"G0Uy","./mt.js":"G0Uy","./my":"honF","./my.js":"honF","./nb":"bOMt","./nb.js":"bOMt","./ne":"OjkT","./ne.js":"OjkT","./nl":"+s0g","./nl-be":"2ykv","./nl-be.js":"2ykv","./nl.js":"+s0g","./nn":"uEye","./nn.js":"uEye","./oc-lnc":"Fnuy","./oc-lnc.js":"Fnuy","./pa-in":"8/+R","./pa-in.js":"8/+R","./pl":"jVdC","./pl.js":"jVdC","./pt":"8mBD","./pt-br":"0tRk","./pt-br.js":"0tRk","./pt.js":"8mBD","./ro":"lyxo","./ro.js":"lyxo","./ru":"lXzo","./ru.js":"lXzo","./sd":"Z4QM","./sd.js":"Z4QM","./se":"//9w","./se.js":"//9w","./si":"7aV9","./si.js":"7aV9","./sk":"e+ae","./sk.js":"e+ae","./sl":"gVVK","./sl.js":"gVVK","./sq":"yPMs","./sq.js":"yPMs","./sr":"zx6S","./sr-cyrl":"E+lV","./sr-cyrl.js":"E+lV","./sr.js":"zx6S","./ss":"Ur1D","./ss.js":"Ur1D","./sv":"X709","./sv.js":"X709","./sw":"dNwA","./sw.js":"dNwA","./ta":"PeUW","./ta.js":"PeUW","./te":"XLvN","./te.js":"XLvN","./tet":"V2x9","./tet.js":"V2x9","./tg":"Oxv6","./tg.js":"Oxv6","./th":"EOgW","./th.js":"EOgW","./tk":"Wv91","./tk.js":"Wv91","./tl-ph":"Dzi0","./tl-ph.js":"Dzi0","./tlh":"z3Vd","./tlh.js":"z3Vd","./tr":"DoHr","./tr.js":"DoHr","./tzl":"z1FC","./tzl.js":"z1FC","./tzm":"wQk9","./tzm-latn":"tT3J","./tzm-latn.js":"tT3J","./tzm.js":"wQk9","./ug-cn":"YRex","./ug-cn.js":"YRex","./uk":"raLr","./uk.js":"raLr","./ur":"UpQW","./ur.js":"UpQW","./uz":"Loxo","./uz-latn":"AQ68","./uz-latn.js":"AQ68","./uz.js":"Loxo","./vi":"KSF8","./vi.js":"KSF8","./x-pseudo":"/X5v","./x-pseudo.js":"/X5v","./yo":"fzPg","./yo.js":"fzPg","./zh-cn":"XDpg","./zh-cn.js":"XDpg","./zh-hk":"SatO","./zh-hk.js":"SatO","./zh-mo":"OmwH","./zh-mo.js":"OmwH","./zh-tw":"kOpN","./zh-tw.js":"kOpN"};function r(e){var t=s(e);return a(t)}function s(e){if(!a.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}r.keys=function(){return Object.keys(n)},r.resolve=s,e.exports=r,r.id="RnhZ"},Rqgy:function(e,t,a){"use strict";a.r(t);var n=a("HaE+"),r=(a("2B1R"),a("o0o1")),s=a.n(r),i=a("wd/R"),o=a.n(i),d=a("6Ytq"),u=a("giZP"),l=a("R5cT"),_=a("JtJI"),c=a("g2Gq"),m=a("3Zo4"),h=a("nqqA"),f=a("GUe+"),p=a("uqS5"),M=a("Obrr"),y=a("Ed67"),g=a("9hfn"),L=a("vDqi"),v=a.n(L),Y={components:{BBadge:d.a,BFormGroup:u.a,BFormInput:l.a,VueGoodTable:g.a,moment:o.a,BPagination:_.a,BFormSelect:c.a,BDropdown:m.a,BDropdownItem:h.a,BButton:f.a,BSidebar:p.a,VBToggle:M.a,BForm:y.a},directives:{"b-toggle":M.a},data:function(){return{rowSelection:{length:0},pageLength:15,dir:!1,columns:[{label:"#",field:"id",hidden:!0},{label:"الاسم",field:"name",sortable:!1},{label:"البريد الالكتروني",field:"email",sortable:!1},{label:"الدولة",field:"country",sortable:!1},{label:"الصلاحية",field:"role",sortable:!1},{label:"تاريخ التسجيل",field:"register_date",sortable:!1},{label:"الاعدادات",field:"action",sortable:!1}],rows:[],searchTerm:"",form:{name:"",email:"",password:"",country:"",role:""}}},mounted:function(){var e=this;return Object(n.a)(s.a.mark((function t(){var a;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,v.a.post("/api/admin/admins",{},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}});case 3:a=t.sent,e.rows=a.data,t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0),alert("حدث خطأ ما");case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},methods:{selectionChanged:function(e){this.rowSelection=e.selectedRows},addAdmin:function(){v.a.post("/api/admin/add-admin",this.form,{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم اضافة المستخدم"),location.reload()})).catch((function(e){alert("المستخدم موجود من قبل")}))},blockAdmin:function(e){window.confirm("هل انت متأكد ؟")&&v.a.post("/api/admin/block-admin",{id:e},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم حظر المستخدم"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))},unblockAdmin:function(e){window.confirm("هل انت متأكد ؟")&&v.a.post("/api/admin/unblock-admin",{id:e},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم رفع حظر المستخدم"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))},deleteAdmin:function(e){window.confirm("هل انت متأكد ؟")&&v.a.post("/api/admin/delete-admin",{id:e},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم حذف المستخدم"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))},deleteSelection:function(){var e,t,a=this.rowSelection.length;window.confirm("هل انتا متاكد من حذف ("+a+") من المشرفين !");if(confirm){var n=(e=this.rowSelection,t="id",e.map((function(e){return e[t]})));v.a.post("/api/admin/delete-admins",{ids:n},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم حذف المستخدم"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))}}}},w=a("KHd+"),b=Object(w.a)(Y,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("b-button",{directives:[{name:"b-toggle",rawName:"v-b-toggle.sidebar-right",modifiers:{"sidebar-right":!0}}],staticClass:"btn-icon",staticStyle:{"margin-right":"auto",display:"block"},attrs:{variant:"outline-primary"}},[a("feather-icon",{staticClass:"mr-50",attrs:{icon:"PlusIcon"}}),e._v(" "),a("span",{staticClass:"align-middle"},[e._v("اضافة مستخدم")])],1),e._v(" "),a("b-sidebar",{attrs:{id:"sidebar-right","bg-variant":"white",backdrop:"",shadow:""}},[a("b-form",{staticClass:"p-2",on:{submit:function(t){return t.preventDefault(),e.addAdmin.apply(null,arguments)}}},[a("b-form-group",{attrs:{label:"الاسم","label-for":"name"}},[a("b-form-input",{attrs:{type:"text",placeholder:"الاسم",id:"name"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),e._v(" "),a("b-form-group",{attrs:{label:"البريد الالكتروني","label-for":"email"}},[a("b-form-input",{attrs:{type:"email",placeholder:"البريد الالكتروني",id:"email"},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1),e._v(" "),a("b-form-group",{attrs:{label:"كلمة المرور","label-for":"password"}},[a("b-form-input",{attrs:{type:"password",placeholder:"كلمة المرور",id:"password"},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1),e._v(" "),a("b-form-group",{attrs:{label:"الدولة","label-for":"country"}},[a("b-form-select",{attrs:{id:"country"},model:{value:e.form.country,callback:function(t){e.$set(e.form,"country",t)},expression:"form.country"}},[a("option",{attrs:{value:"أفغانستان"}},[e._v("أفغانستان")]),e._v(" "),a("option",{attrs:{value:"ألبانيا"}},[e._v("ألبانيا")]),e._v(" "),a("option",{attrs:{value:"الجزائر"}},[e._v("الجزائر")]),e._v(" "),a("option",{attrs:{value:"أندورا"}},[e._v("أندورا")]),e._v(" "),a("option",{attrs:{value:"أنغولا"}},[e._v("أنغولا")]),e._v(" "),a("option",{attrs:{value:"أنتيغوا وباربودا"}},[e._v("\n                        أنتيغوا وباربودا\n                    ")]),e._v(" "),a("option",{attrs:{value:"الأرجنتين"}},[e._v("الأرجنتين")]),e._v(" "),a("option",{attrs:{value:"أرمينيا"}},[e._v("أرمينيا")]),e._v(" "),a("option",{attrs:{value:"أستراليا"}},[e._v("أستراليا")]),e._v(" "),a("option",{attrs:{value:"النمسا"}},[e._v("النمسا")]),e._v(" "),a("option",{attrs:{value:"أذربيجان"}},[e._v("أذربيجان")]),e._v(" "),a("option",{attrs:{value:"البهاما"}},[e._v("البهاما")]),e._v(" "),a("option",{attrs:{value:"البحرين"}},[e._v("البحرين")]),e._v(" "),a("option",{attrs:{value:"بنغلاديش"}},[e._v("بنغلاديش")]),e._v(" "),a("option",{attrs:{value:"باربادوس"}},[e._v("باربادوس")]),e._v(" "),a("option",{attrs:{value:"بيلاروسيا"}},[e._v("بيلاروسيا")]),e._v(" "),a("option",{attrs:{value:"بلجيكا"}},[e._v("بلجيكا")]),e._v(" "),a("option",{attrs:{value:"بليز"}},[e._v("بليز")]),e._v(" "),a("option",{attrs:{value:"بنين"}},[e._v("بنين")]),e._v(" "),a("option",{attrs:{value:"بوتان"}},[e._v("بوتان")]),e._v(" "),a("option",{attrs:{value:"بوليفيا"}},[e._v("بوليفيا")]),e._v(" "),a("option",{attrs:{value:"البوسنة والهرسك "}},[e._v("\n                        البوسنة والهرسك\n                    ")]),e._v(" "),a("option",{attrs:{value:"بوتسوانا"}},[e._v("بوتسوانا")]),e._v(" "),a("option",{attrs:{value:"البرازيل"}},[e._v("البرازيل")]),e._v(" "),a("option",{attrs:{value:"بروناي"}},[e._v("بروناي")]),e._v(" "),a("option",{attrs:{value:"بلغاريا"}},[e._v("بلغاريا")]),e._v(" "),a("option",{attrs:{value:"بوركينا فاسو "}},[e._v("بوركينا فاسو")]),e._v(" "),a("option",{attrs:{value:"بوروندي"}},[e._v("بوروندي")]),e._v(" "),a("option",{attrs:{value:"كمبوديا"}},[e._v("كمبوديا")]),e._v(" "),a("option",{attrs:{value:"الكاميرون"}},[e._v("الكاميرون")]),e._v(" "),a("option",{attrs:{value:"كندا"}},[e._v("كندا")]),e._v(" "),a("option",{attrs:{value:"الرأس الأخضر"}},[e._v("الرأس الأخضر")]),e._v(" "),a("option",{attrs:{value:"جمهورية أفريقيا الوسطى "}},[e._v("\n                        جمهورية أفريقيا الوسطى\n                    ")]),e._v(" "),a("option",{attrs:{value:"تشاد"}},[e._v("تشاد")]),e._v(" "),a("option",{attrs:{value:"تشيلي"}},[e._v("تشيلي")]),e._v(" "),a("option",{attrs:{value:"الصين"}},[e._v("الصين")]),e._v(" "),a("option",{attrs:{value:"كولومبيا"}},[e._v("كولومبيا")]),e._v(" "),a("option",{attrs:{value:"جزر القمر"}},[e._v("جزر القمر")]),e._v(" "),a("option",{attrs:{value:"كوستاريكا"}},[e._v("كوستاريكا")]),e._v(" "),a("option",{attrs:{value:"ساحل العاج"}},[e._v("ساحل العاج")]),e._v(" "),a("option",{attrs:{value:"كرواتيا"}},[e._v("كرواتيا")]),e._v(" "),a("option",{attrs:{value:"كوبا"}},[e._v("كوبا")]),e._v(" "),a("option",{attrs:{value:"قبرص"}},[e._v("قبرص")]),e._v(" "),a("option",{attrs:{value:"التشيك"}},[e._v("التشيك")]),e._v(" "),a("option",{attrs:{value:"جمهورية الكونغو الديمقراطية"}},[e._v("\n                        جمهورية الكونغو الديمقراطية\n                    ")]),e._v(" "),a("option",{attrs:{value:"الدنمارك"}},[e._v("الدنمارك")]),e._v(" "),a("option",{attrs:{value:"جيبوتي"}},[e._v("جيبوتي")]),e._v(" "),a("option",{attrs:{value:"دومينيكا"}},[e._v("دومينيكا")]),e._v(" "),a("option",{attrs:{value:"جمهورية الدومينيكان"}},[e._v("\n                        جمهورية الدومينيكان\n                    ")]),e._v(" "),a("option",{attrs:{value:"تيمور الشرقية "}},[e._v("تيمور الشرقية")]),e._v(" "),a("option",{attrs:{value:"الإكوادور"}},[e._v("الإكوادور")]),e._v(" "),a("option",{attrs:{value:"مصر"}},[e._v("مصر")]),e._v(" "),a("option",{attrs:{value:"السلفادور"}},[e._v("السلفادور")]),e._v(" "),a("option",{attrs:{value:"غينيا الاستوائية"}},[e._v("\n                        غينيا الاستوائية\n                    ")]),e._v(" "),a("option",{attrs:{value:"إريتريا"}},[e._v("إريتريا")]),e._v(" "),a("option",{attrs:{value:"إستونيا"}},[e._v("إستونيا")]),e._v(" "),a("option",{attrs:{value:"إثيوبيا"}},[e._v("إثيوبيا")]),e._v(" "),a("option",{attrs:{value:"فيجي"}},[e._v("فيجي")]),e._v(" "),a("option",{attrs:{value:"فنلندا"}},[e._v("فنلندا")]),e._v(" "),a("option",{attrs:{value:"فرنسا"}},[e._v("فرنسا")]),e._v(" "),a("option",{attrs:{value:"الغابون"}},[e._v("الغابون")]),e._v(" "),a("option",{attrs:{value:"غامبيا"}},[e._v("غامبيا")]),e._v(" "),a("option",{attrs:{value:"جورجيا"}},[e._v("جورجيا")]),e._v(" "),a("option",{attrs:{value:"ألمانيا"}},[e._v("ألمانيا")]),e._v(" "),a("option",{attrs:{value:"غانا"}},[e._v("غانا")]),e._v(" "),a("option",{attrs:{value:"اليونان"}},[e._v("اليونان")]),e._v(" "),a("option",{attrs:{value:"جرينادا"}},[e._v("جرينادا")]),e._v(" "),a("option",{attrs:{value:"غواتيمالا"}},[e._v("غواتيمالا")]),e._v(" "),a("option",{attrs:{value:"غينيا"}},[e._v("غينيا")]),e._v(" "),a("option",{attrs:{value:"غينيا بيساو"}},[e._v("غينيا بيساو")]),e._v(" "),a("option",{attrs:{value:"غويانا"}},[e._v("غويانا")]),e._v(" "),a("option",{attrs:{value:"هايتي"}},[e._v("هايتي")]),e._v(" "),a("option",{attrs:{value:"هندوراس"}},[e._v("هندوراس")]),e._v(" "),a("option",{attrs:{value:"المجر"}},[e._v("المجر")]),e._v(" "),a("option",{attrs:{value:"آيسلندا"}},[e._v("آيسلندا")]),e._v(" "),a("option",{attrs:{value:"الهند"}},[e._v("الهند")]),e._v(" "),a("option",{attrs:{value:"إندونيسيا"}},[e._v("إندونيسيا")]),e._v(" "),a("option",{attrs:{value:"إيران"}},[e._v("إيران")]),e._v(" "),a("option",{attrs:{value:"العراق"}},[e._v("العراق")]),e._v(" "),a("option",{attrs:{value:"جمهورية أيرلندا "}},[e._v("\n                        جمهورية أيرلندا\n                    ")]),e._v(" "),a("option",{attrs:{value:"فلسطين"}},[e._v("فلسطين")]),e._v(" "),a("option",{attrs:{value:"إيطاليا"}},[e._v("إيطاليا")]),e._v(" "),a("option",{attrs:{value:"جامايكا"}},[e._v("جامايكا")]),e._v(" "),a("option",{attrs:{value:"اليابان"}},[e._v("اليابان")]),e._v(" "),a("option",{attrs:{value:"الأردن"}},[e._v("الأردن")]),e._v(" "),a("option",{attrs:{value:"كازاخستان"}},[e._v("كازاخستان")]),e._v(" "),a("option",{attrs:{value:"كينيا"}},[e._v("كينيا")]),e._v(" "),a("option",{attrs:{value:"كيريباتي"}},[e._v("كيريباتي")]),e._v(" "),a("option",{attrs:{value:"الكويت"}},[e._v("الكويت")]),e._v(" "),a("option",{attrs:{value:"قرغيزستان"}},[e._v("قرغيزستان")]),e._v(" "),a("option",{attrs:{value:"لاوس"}},[e._v("لاوس")]),e._v(" "),a("option",{attrs:{value:"لاوس"}},[e._v("لاوس")]),e._v(" "),a("option",{attrs:{value:"لاتفيا"}},[e._v("لاتفيا")]),e._v(" "),a("option",{attrs:{value:"لبنان"}},[e._v("لبنان")]),e._v(" "),a("option",{attrs:{value:"ليسوتو"}},[e._v("ليسوتو")]),e._v(" "),a("option",{attrs:{value:"ليبيريا"}},[e._v("ليبيريا")]),e._v(" "),a("option",{attrs:{value:"ليبيا"}},[e._v("ليبيا")]),e._v(" "),a("option",{attrs:{value:"ليختنشتاين"}},[e._v("ليختنشتاين")]),e._v(" "),a("option",{attrs:{value:"ليتوانيا"}},[e._v("ليتوانيا")]),e._v(" "),a("option",{attrs:{value:"لوكسمبورغ"}},[e._v("لوكسمبورغ")]),e._v(" "),a("option",{attrs:{value:"مدغشقر"}},[e._v("مدغشقر")]),e._v(" "),a("option",{attrs:{value:"مالاوي"}},[e._v("مالاوي")]),e._v(" "),a("option",{attrs:{value:"ماليزيا"}},[e._v("ماليزيا")]),e._v(" "),a("option",{attrs:{value:"جزر المالديف"}},[e._v("جزر المالديف")]),e._v(" "),a("option",{attrs:{value:"مالي"}},[e._v("مالي")]),e._v(" "),a("option",{attrs:{value:"مالطا"}},[e._v("مالطا")]),e._v(" "),a("option",{attrs:{value:"جزر مارشال"}},[e._v("جزر مارشال")]),e._v(" "),a("option",{attrs:{value:"موريتانيا"}},[e._v("موريتانيا")]),e._v(" "),a("option",{attrs:{value:"موريشيوس"}},[e._v("موريشيوس")]),e._v(" "),a("option",{attrs:{value:"المكسيك"}},[e._v("المكسيك")]),e._v(" "),a("option",{attrs:{value:"مايكرونيزيا"}},[e._v("مايكرونيزيا")]),e._v(" "),a("option",{attrs:{value:"مولدوفا"}},[e._v("مولدوفا")]),e._v(" "),a("option",{attrs:{value:"موناكو"}},[e._v("موناكو")]),e._v(" "),a("option",{attrs:{value:"منغوليا"}},[e._v("منغوليا")]),e._v(" "),a("option",{attrs:{value:"الجبل الأسود"}},[e._v("الجبل الأسود")]),e._v(" "),a("option",{attrs:{value:"المغرب"}},[e._v("المغرب")]),e._v(" "),a("option",{attrs:{value:"موزمبيق"}},[e._v("موزمبيق")]),e._v(" "),a("option",{attrs:{value:"بورما"}},[e._v("بورما")]),e._v(" "),a("option",{attrs:{value:"ناميبيا"}},[e._v("ناميبيا")]),e._v(" "),a("option",{attrs:{value:"ناورو"}},[e._v("ناورو")]),e._v(" "),a("option",{attrs:{value:"نيبال"}},[e._v("نيبال")]),e._v(" "),a("option",{attrs:{value:"هولندا"}},[e._v("هولندا")]),e._v(" "),a("option",{attrs:{value:"نيوزيلندا"}},[e._v("نيوزيلندا")]),e._v(" "),a("option",{attrs:{value:"نيكاراجوا"}},[e._v("نيكاراجوا")]),e._v(" "),a("option",{attrs:{value:"النيجر"}},[e._v("النيجر")]),e._v(" "),a("option",{attrs:{value:"نيجيريا"}},[e._v("نيجيريا")]),e._v(" "),a("option",{attrs:{value:"كوريا الشمالية "}},[e._v("كوريا الشمالية")]),e._v(" "),a("option",{attrs:{value:"النرويج"}},[e._v("النرويج")]),e._v(" "),a("option",{attrs:{value:"سلطنة عمان"}},[e._v("سلطنة عمان")]),e._v(" "),a("option",{attrs:{value:"باكستان"}},[e._v("باكستان")]),e._v(" "),a("option",{attrs:{value:"بالاو"}},[e._v("بالاو")]),e._v(" "),a("option",{attrs:{value:"بنما"}},[e._v("بنما")]),e._v(" "),a("option",{attrs:{value:"بابوا غينيا الجديدة"}},[e._v("\n                        بابوا غينيا الجديدة\n                    ")]),e._v(" "),a("option",{attrs:{value:"باراغواي"}},[e._v("باراغواي")]),e._v(" "),a("option",{attrs:{value:"بيرو"}},[e._v("بيرو")]),e._v(" "),a("option",{attrs:{value:"الفلبين"}},[e._v("الفلبين")]),e._v(" "),a("option",{attrs:{value:"بولندا"}},[e._v("بولندا")]),e._v(" "),a("option",{attrs:{value:"البرتغال"}},[e._v("البرتغال")]),e._v(" "),a("option",{attrs:{value:"قطر"}},[e._v("قطر")]),e._v(" "),a("option",{attrs:{value:"جمهورية الكونغو"}},[e._v("جمهورية الكونغو")]),e._v(" "),a("option",{attrs:{value:"جمهورية مقدونيا"}},[e._v("جمهورية مقدونيا")]),e._v(" "),a("option",{attrs:{value:"رومانيا"}},[e._v("رومانيا")]),e._v(" "),a("option",{attrs:{value:"روسيا"}},[e._v("روسيا")]),e._v(" "),a("option",{attrs:{value:"رواندا"}},[e._v("رواندا")]),e._v(" "),a("option",{attrs:{value:"سانت كيتس ونيفيس"}},[e._v("\n                        سانت كيتس ونيفيس\n                    ")]),e._v(" "),a("option",{attrs:{value:"سانت لوسيا"}},[e._v("سانت لوسيا")]),e._v(" "),a("option",{attrs:{value:"سانت فنسينت والجرينادينز"}},[e._v("\n                        سانت فنسينت والجرينادينز\n                    ")]),e._v(" "),a("option",{attrs:{value:"ساموا"}},[e._v("ساموا")]),e._v(" "),a("option",{attrs:{value:"سان مارينو"}},[e._v("سان مارينو")]),e._v(" "),a("option",{attrs:{value:"ساو تومي وبرينسيب"}},[e._v("\n                        ساو تومي وبرينسيب\n                    ")]),e._v(" "),a("option",{attrs:{value:"السعودية"}},[e._v("السعودية")]),e._v(" "),a("option",{attrs:{value:"السنغال"}},[e._v("السنغال")]),e._v(" "),a("option",{attrs:{value:"صربيا"}},[e._v("صربيا")]),e._v(" "),a("option",{attrs:{value:"سيشيل"}},[e._v("سيشيل")]),e._v(" "),a("option",{attrs:{value:"سيراليون"}},[e._v("سيراليون")]),e._v(" "),a("option",{attrs:{value:"سنغافورة"}},[e._v("سنغافورة")]),e._v(" "),a("option",{attrs:{value:"سلوفاكيا"}},[e._v("سلوفاكيا")]),e._v(" "),a("option",{attrs:{value:"سلوفينيا"}},[e._v("سلوفينيا")]),e._v(" "),a("option",{attrs:{value:"جزر سليمان"}},[e._v("جزر سليمان")]),e._v(" "),a("option",{attrs:{value:"الصومال"}},[e._v("الصومال")]),e._v(" "),a("option",{attrs:{value:"جنوب أفريقيا"}},[e._v("جنوب أفريقيا")]),e._v(" "),a("option",{attrs:{value:"كوريا الجنوبية"}},[e._v("كوريا الجنوبية")]),e._v(" "),a("option",{attrs:{value:"جنوب السودان"}},[e._v("جنوب السودان")]),e._v(" "),a("option",{attrs:{value:"إسبانيا"}},[e._v("إسبانيا")]),e._v(" "),a("option",{attrs:{value:"سريلانكا"}},[e._v("سريلانكا")]),e._v(" "),a("option",{attrs:{value:"السودان"}},[e._v("السودان")]),e._v(" "),a("option",{attrs:{value:"سورينام"}},[e._v("سورينام")]),e._v(" "),a("option",{attrs:{value:"سوازيلاند"}},[e._v("سوازيلاند")]),e._v(" "),a("option",{attrs:{value:"السويد"}},[e._v("السويد")]),e._v(" "),a("option",{attrs:{value:"سويسرا"}},[e._v("سويسرا")]),e._v(" "),a("option",{attrs:{value:"سوريا"}},[e._v("سوريا")]),e._v(" "),a("option",{attrs:{value:"طاجيكستان"}},[e._v("طاجيكستان")]),e._v(" "),a("option",{attrs:{value:"تنزانيا"}},[e._v("تنزانيا")]),e._v(" "),a("option",{attrs:{value:"تايلاند"}},[e._v("تايلاند")]),e._v(" "),a("option",{attrs:{value:"توغو"}},[e._v("توغو")]),e._v(" "),a("option",{attrs:{value:"تونجا"}},[e._v("تونجا")]),e._v(" "),a("option",{attrs:{value:"ترينيداد وتوباغو"}},[e._v("\n                        ترينيداد وتوباغو\n                    ")]),e._v(" "),a("option",{attrs:{value:"تونس"}},[e._v("تونس")]),e._v(" "),a("option",{attrs:{value:"تركيا"}},[e._v("تركيا")]),e._v(" "),a("option",{attrs:{value:"تركمانستان"}},[e._v("تركمانستان")]),e._v(" "),a("option",{attrs:{value:"توفالو"}},[e._v("توفالو")]),e._v(" "),a("option",{attrs:{value:"أوغندا"}},[e._v("أوغندا")]),e._v(" "),a("option",{attrs:{value:"أوكرانيا"}},[e._v("أوكرانيا")]),e._v(" "),a("option",{attrs:{value:"الإمارات العربية المتحدة"}},[e._v("\n                        الإمارات العربية المتحدة\n                    ")]),e._v(" "),a("option",{attrs:{value:"المملكة المتحدة"}},[e._v("المملكة المتحدة")]),e._v(" "),a("option",{attrs:{value:"الولايات المتحدة"}},[e._v("\n                        الولايات المتحدة\n                    ")]),e._v(" "),a("option",{attrs:{value:"أوروغواي"}},[e._v("أوروغواي")]),e._v(" "),a("option",{attrs:{value:"أوزبكستان"}},[e._v("أوزبكستان")]),e._v(" "),a("option",{attrs:{value:"فانواتو"}},[e._v("فانواتو")]),e._v(" "),a("option",{attrs:{value:"فنزويلا"}},[e._v("فنزويلا")]),e._v(" "),a("option",{attrs:{value:"فيتنام"}},[e._v("فيتنام")]),e._v(" "),a("option",{attrs:{value:"اليمن"}},[e._v("اليمن")]),e._v(" "),a("option",{attrs:{value:"زامبيا"}},[e._v("زامبيا")]),e._v(" "),a("option",{attrs:{value:"زيمبابوي"}},[e._v("زيمبابوي")])])],1),e._v(" "),a("b-form-group",{attrs:{label:"الصلاحية","label-for":"role"}},[a("b-form-select",{attrs:{id:"role"},model:{value:e.form.role,callback:function(t){e.$set(e.form,"role",t)},expression:"form.role"}},[a("option",{attrs:{value:"1"}},[e._v("ادمن")]),e._v(" "),a("option",{attrs:{value:"2"}},[e._v("مشرف")]),e._v(" "),a("option",{attrs:{value:"3"}},[e._v("ناشر")])])],1),e._v(" "),a("b-button",{staticClass:"w-100",attrs:{type:"submit",variant:"primary"}},[e._v("\n                اضافة\n            ")])],1)],1),e._v(" "),a("div",{staticClass:"custom-search d-flex justify-content-start"},[a("b-form-group",[a("div",{staticClass:"d-flex align-items-center"},[a("b-form-input",{staticClass:"d-inline-block",attrs:{placeholder:"بحث",type:"text"},model:{value:e.searchTerm,callback:function(t){e.searchTerm=t},expression:"searchTerm"}})],1)])],1),e._v(" "),a("vue-good-table",{attrs:{"select-options":{enabled:!0,selectionText:"صفوف محدده",clearSelectionText:"ازاله التحديد"},columns:e.columns,rows:e.rows,rtl:e.dir,"search-options":{enabled:!0,externalQuery:e.searchTerm},"pagination-options":{enabled:!0,perPage:e.pageLength}},on:{"on-selected-rows-change":e.selectionChanged},scopedSlots:e._u([{key:"table-row",fn:function(t){return["fullName"===t.column.field?a("span",{staticClass:"text-nowrap"},[a("span",{staticClass:"text-nowrap"},[e._v(e._s(t.row.fullName))])]):"role"===t.column.field?a("span",["1"===t.row.role?a("span",[e._v("ادمن")]):"2"===t.row.role?a("span",[e._v("مشرف")]):"3"===t.row.role?a("span",[e._v("ناشر")]):e._e()]):"action"===t.column.field?a("span",[a("b-dropdown",{attrs:{variant:"link","toggle-class":"text-decoration-none","no-caret":""},scopedSlots:e._u([{key:"button-content",fn:function(){return[a("feather-icon",{staticClass:"text-body",attrs:{icon:"MoreVerticalIcon",size:"16"}})]},proxy:!0}],null,!0)},[e._v(" "),a("b-dropdown-item",{attrs:{to:"/edit-admin/"+t.row.id}},[a("feather-icon",{staticClass:"mr-50",attrs:{icon:"Edit2Icon"}}),e._v(" "),a("span",[e._v("تعديل")])],1),e._v(" "),0===t.row.ban?a("b-dropdown-item",{on:{click:function(a){return e.blockAdmin(t.row.id)}}},[a("feather-icon",{staticClass:"mr-50",attrs:{icon:"StopCircleIcon"}}),e._v(" "),a("span",[e._v("حظر")])],1):a("b-dropdown-item",{on:{click:function(a){return e.unblockAdmin(t.row.id)}}},[a("feather-icon",{staticClass:"mr-50",attrs:{icon:"StopCircleIcon"}}),e._v(" "),a("span",[e._v("رفع الحظر")])],1),e._v(" "),a("b-dropdown-item",{on:{click:function(a){return e.deleteAdmin(t.row.id)}}},[a("feather-icon",{staticClass:"mr-50",attrs:{icon:"TrashIcon"}}),e._v(" "),a("span",[e._v("حذف")])],1)],1)],1):a("span",[e._v("\n                "+e._s(t.formattedRow[t.column.field])+"\n            ")])]}},{key:"pagination-bottom",fn:function(t){return[a("div",{staticClass:"d-flex justify-content-between flex-wrap"},[a("div",{staticClass:"d-flex align-items-center mb-0 mt-1"},[a("span",{staticClass:"text-nowrap"},[e._v(" اظهار 1 الي ")]),e._v(" "),a("b-form-select",{staticClass:"mx-1",attrs:{options:["15","30","50","100"]},on:{input:function(e){return t.perPageChanged({currentPerPage:e})}},model:{value:e.pageLength,callback:function(t){e.pageLength=t},expression:"pageLength"}}),e._v(" "),a("span",{staticClass:"text-nowrap"},[e._v("\n                        من "+e._s(t.total)+" صف\n                    ")])],1),e._v(" "),a("div",[a("b-pagination",{staticClass:"mt-1 mb-0",attrs:{value:1,"total-rows":t.total,"per-page":e.pageLength,"first-number":"","last-number":"",align:"right"},on:{input:function(e){return t.pageChanged({currentPage:e})}},scopedSlots:e._u([{key:"prev-text",fn:function(){return[a("feather-icon",{attrs:{icon:"ChevronLeftIcon",size:"18"}})]},proxy:!0},{key:"next-text",fn:function(){return[a("feather-icon",{attrs:{icon:"ChevronRightIcon",size:"18"}})]},proxy:!0}],null,!0)})],1)])]}}])},[a("div",{attrs:{slot:"selected-row-actions"},slot:"selected-row-actions"},[a("div",{staticClass:"d-flex align-items-center"},[a("b-button",{staticClass:"p-auto",attrs:{pill:"",variant:"danger"},on:{click:function(t){return e.deleteSelection()}}},[a("feather-icon",{staticClass:"mr-50",attrs:{icon:"TrashIcon"}}),e._v(" "),a("span",{staticClass:"align-middle"})],1)],1)]),e._v(" "),a("div",{attrs:{slot:"emptystate"},slot:"emptystate"},[e._v("لا توجد بيانات")])])],1)}),[],!1,null,null,null);t.default=b.exports},S6ln:function(e,t,a){!function(e){"use strict";function t(e,t,a){var n=e+" ";switch(a){case"ss":return n+=1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi";case"m":return t?"jedna minuta":"jedne minute";case"mm":return n+=1===e?"minuta":2===e||3===e||4===e?"minute":"minuta";case"h":return t?"jedan sat":"jednog sata";case"hh":return n+=1===e?"sat":2===e||3===e||4===e?"sata":"sati";case"dd":return n+=1===e?"dan":"dana";case"MM":return n+=1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci";case"yy":return n+=1===e?"godina":2===e||3===e||4===e?"godine":"godina"}}e.defineLocale("hr",{months:{format:"siječnja_veljače_ožujka_travnja_svibnja_lipnja_srpnja_kolovoza_rujna_listopada_studenoga_prosinca".split("_"),standalone:"siječanj_veljača_ožujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split("_")},monthsShort:"sij._velj._ožu._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"Do MMMM YYYY",LLL:"Do MMMM YYYY H:mm",LLLL:"dddd, Do MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:return"[prošlu] [nedjelju] [u] LT";case 3:return"[prošlu] [srijedu] [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:t,m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(a("wd/R"))},SFxW:function(e,t,a){!function(e){"use strict";var t={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-üncü",4:"-üncü",100:"-üncü",6:"-ncı",9:"-uncu",10:"-uncu",30:"-uncu",60:"-ıncı",90:"-ıncı"};e.defineLocale("az",{months:"yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr".split("_"),monthsShort:"yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek".split("_"),weekdays:"Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə".split("_"),weekdaysShort:"Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən".split("_"),weekdaysMin:"Bz_BE_ÇA_Çə_CA_Cü_Şə".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[sabah saat] LT",nextWeek:"[gələn həftə] dddd [saat] LT",lastDay:"[dünən] LT",lastWeek:"[keçən həftə] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s əvvəl",s:"bir neçə saniyə",ss:"%d saniyə",m:"bir dəqiqə",mm:"%d dəqiqə",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir il",yy:"%d il"},meridiemParse:/gecə|səhər|gündüz|axşam/,isPM:function(e){return/^(gündüz|axşam)$/.test(e)},meridiem:function(e,t,a){return e<4?"gecə":e<12?"səhər":e<17?"gündüz":"axşam"},dayOfMonthOrdinalParse:/\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,ordinal:function(e){if(0===e)return e+"-ıncı";var a=e%10;return e+(t[a]||t[e%100-a]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(a("wd/R"))},SatO:function(e,t,a){!function(e){"use strict";e.defineLocale("zh-hk",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,a){var n=100*e+t;return n<600?"凌晨":n<900?"早上":n<1200?"上午":1200===n?"中午":n<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})}(a("wd/R"))},UDhR:function(e,t,a){!function(e){"use strict";e.defineLocale("id",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|siang|sore|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"siang"===t?e>=11?e:e+12:"sore"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,a){return e<11?"pagi":e<15?"siang":e<19?"sore":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",ss:"%d detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:0,doy:6}})}(a("wd/R"))},USCx:function(e,t,a){!function(e){"use strict";e.defineLocale("ga",{months:["Eanáir","Feabhra","Márta","Aibreán","Bealtaine","Meitheamh","Iúil","Lúnasa","Meán Fómhair","Deireadh Fómhair","Samhain","Nollaig"],monthsShort:["Ean","Feabh","Márt","Aib","Beal","Meith","Iúil","Lún","M.F.","D.F.","Samh","Noll"],monthsParseExact:!0,weekdays:["Dé Domhnaigh","Dé Luain","Dé Máirt","Dé Céadaoin","Déardaoin","Dé hAoine","Dé Sathairn"],weekdaysShort:["Domh","Luan","Máirt","Céad","Déar","Aoine","Sath"],weekdaysMin:["Do","Lu","Má","Cé","Dé","A","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Inniu ag] LT",nextDay:"[Amárach ag] LT",nextWeek:"dddd [ag] LT",lastDay:"[Inné ag] LT",lastWeek:"dddd [seo caite] [ag] LT",sameElse:"L"},relativeTime:{future:"i %s",past:"%s ó shin",s:"cúpla soicind",ss:"%d soicind",m:"nóiméad",mm:"%d nóiméad",h:"uair an chloig",hh:"%d uair an chloig",d:"lá",dd:"%d lá",M:"mí",MM:"%d míonna",y:"bliain",yy:"%d bliain"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){return e+(1===e?"d":e%10==2?"na":"mh")},week:{dow:1,doy:4}})}(a("wd/R"))},UpQW:function(e,t,a){!function(e){"use strict";var t=["جنوری","فروری","مارچ","اپریل","مئی","جون","جولائی","اگست","ستمبر","اکتوبر","نومبر","دسمبر"],a=["اتوار","پیر","منگل","بدھ","جمعرات","جمعہ","ہفتہ"];e.defineLocale("ur",{months:t,monthsShort:t,weekdays:a,weekdaysShort:a,weekdaysMin:a,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(e){return"شام"===e},meridiem:function(e,t,a){return e<12?"صبح":"شام"},calendar:{sameDay:"[آج بوقت] LT",nextDay:"[کل بوقت] LT",nextWeek:"dddd [بوقت] LT",lastDay:"[گذشتہ روز بوقت] LT",lastWeek:"[گذشتہ] dddd [بوقت] LT",sameElse:"L"},relativeTime:{future:"%s بعد",past:"%s قبل",s:"چند سیکنڈ",ss:"%d سیکنڈ",m:"ایک منٹ",mm:"%d منٹ",h:"ایک گھنٹہ",hh:"%d گھنٹے",d:"ایک دن",dd:"%d دن",M:"ایک ماہ",MM:"%d ماہ",y:"ایک سال",yy:"%d سال"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}})}(a("wd/R"))},Ur1D:function(e,t,a){!function(e){"use strict";e.defineLocale("ss",{months:"Bhimbidvwane_Indlovana_Indlov'lenkhulu_Mabasa_Inkhwekhweti_Inhlaba_Kholwane_Ingci_Inyoni_Imphala_Lweti_Ingongoni".split("_"),monthsShort:"Bhi_Ina_Inu_Mab_Ink_Inh_Kho_Igc_Iny_Imp_Lwe_Igo".split("_"),weekdays:"Lisontfo_Umsombuluko_Lesibili_Lesitsatfu_Lesine_Lesihlanu_Umgcibelo".split("_"),weekdaysShort:"Lis_Umb_Lsb_Les_Lsi_Lsh_Umg".split("_"),weekdaysMin:"Li_Us_Lb_Lt_Ls_Lh_Ug".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Namuhla nga] LT",nextDay:"[Kusasa nga] LT",nextWeek:"dddd [nga] LT",lastDay:"[Itolo nga] LT",lastWeek:"dddd [leliphelile] [nga] LT",sameElse:"L"},relativeTime:{future:"nga %s",past:"wenteka nga %s",s:"emizuzwana lomcane",ss:"%d mzuzwana",m:"umzuzu",mm:"%d emizuzu",h:"lihora",hh:"%d emahora",d:"lilanga",dd:"%d emalanga",M:"inyanga",MM:"%d tinyanga",y:"umnyaka",yy:"%d iminyaka"},meridiemParse:/ekuseni|emini|entsambama|ebusuku/,meridiem:function(e,t,a){return e<11?"ekuseni":e<15?"emini":e<19?"entsambama":"ebusuku"},meridiemHour:function(e,t){return 12===e&&(e=0),"ekuseni"===t?e:"emini"===t?e>=11?e:e+12:"entsambama"===t||"ebusuku"===t?0===e?0:e+12:void 0},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:"%d",week:{dow:1,doy:4}})}(a("wd/R"))},V2x9:function(e,t,a){!function(e){"use strict";e.defineLocale("tet",{months:"Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu".split("_"),weekdaysShort:"Dom_Seg_Ters_Kua_Kint_Sest_Sab".split("_"),weekdaysMin:"Do_Seg_Te_Ku_Ki_Ses_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Ohin iha] LT",nextDay:"[Aban iha] LT",nextWeek:"dddd [iha] LT",lastDay:"[Horiseik iha] LT",lastWeek:"dddd [semana kotuk] [iha] LT",sameElse:"L"},relativeTime:{future:"iha %s",past:"%s liuba",s:"segundu balun",ss:"segundu %d",m:"minutu ida",mm:"minutu %d",h:"oras ida",hh:"oras %d",d:"loron ida",dd:"loron %d",M:"fulan ida",MM:"fulan %d",y:"tinan ida",yy:"tinan %d"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(a("wd/R"))},Vclq:function(e,t,a){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),a="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),n=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es-us",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,n){return e?/-MMM-/.test(n)?a[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"MM/DD/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:0,doy:6}})}(a("wd/R"))},WYrj:function(e,t,a){!function(e){"use strict";var t=["ޖެނުއަރީ","ފެބްރުއަރީ","މާރިޗު","އޭޕްރީލު","މޭ","ޖޫން","ޖުލައި","އޯގަސްޓު","ސެޕްޓެމްބަރު","އޮކްޓޯބަރު","ނޮވެމްބަރު","ޑިސެމްބަރު"],a=["އާދިއްތަ","ހޯމަ","އަންގާރަ","ބުދަ","ބުރާސްފަތި","ހުކުރު","ހޮނިހިރު"];e.defineLocale("dv",{months:t,monthsShort:t,weekdays:a,weekdaysShort:a,weekdaysMin:"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/M/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/މކ|މފ/,isPM:function(e){return"މފ"===e},meridiem:function(e,t,a){return e<12?"މކ":"މފ"},calendar:{sameDay:"[މިއަދު] LT",nextDay:"[މާދަމާ] LT",nextWeek:"dddd LT",lastDay:"[އިއްޔެ] LT",lastWeek:"[ފާއިތުވި] dddd LT",sameElse:"L"},relativeTime:{future:"ތެރޭގައި %s",past:"ކުރިން %s",s:"ސިކުންތުކޮޅެއް",ss:"d% ސިކުންތު",m:"މިނިޓެއް",mm:"މިނިޓު %d",h:"ގަޑިއިރެއް",hh:"ގަޑިއިރު %d",d:"ދުވަހެއް",dd:"ދުވަސް %d",M:"މަހެއް",MM:"މަސް %d",y:"އަހަރެއް",yy:"އަހަރު %d"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:7,doy:12}})}(a("wd/R"))},Wv91:function(e,t,a){!function(e){"use strict";var t={1:"'inji",5:"'inji",8:"'inji",70:"'inji",80:"'inji",2:"'nji",7:"'nji",20:"'nji",50:"'nji",3:"'ünji",4:"'ünji",100:"'ünji",6:"'njy",9:"'unjy",10:"'unjy",30:"'unjy",60:"'ynjy",90:"'ynjy"};e.defineLocale("tk",{months:"Ýanwar_Fewral_Mart_Aprel_Maý_Iýun_Iýul_Awgust_Sentýabr_Oktýabr_Noýabr_Dekabr".split("_"),monthsShort:"Ýan_Few_Mar_Apr_Maý_Iýn_Iýl_Awg_Sen_Okt_Noý_Dek".split("_"),weekdays:"Ýekşenbe_Duşenbe_Sişenbe_Çarşenbe_Penşenbe_Anna_Şenbe".split("_"),weekdaysShort:"Ýek_Duş_Siş_Çar_Pen_Ann_Şen".split("_"),weekdaysMin:"Ýk_Dş_Sş_Çr_Pn_An_Şn".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün sagat] LT",nextDay:"[ertir sagat] LT",nextWeek:"[indiki] dddd [sagat] LT",lastDay:"[düýn] LT",lastWeek:"[geçen] dddd [sagat] LT",sameElse:"L"},relativeTime:{future:"%s soň",past:"%s öň",s:"birnäçe sekunt",m:"bir minut",mm:"%d minut",h:"bir sagat",hh:"%d sagat",d:"bir gün",dd:"%d gün",M:"bir aý",MM:"%d aý",y:"bir ýyl",yy:"%d ýyl"},ordinal:function(e,a){switch(a){case"d":case"D":case"Do":case"DD":return e;default:if(0===e)return e+"'unjy";var n=e%10;return e+(t[n]||t[e%100-n]||t[e>=100?100:null])}},week:{dow:1,doy:7}})}(a("wd/R"))},WxRl:function(e,t,a){!function(e){"use strict";var t="vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton".split(" ");function a(e,t,a,n){var r=e;switch(a){case"s":return n||t?"néhány másodperc":"néhány másodperce";case"ss":return r+(n||t)?" másodperc":" másodperce";case"m":return"egy"+(n||t?" perc":" perce");case"mm":return r+(n||t?" perc":" perce");case"h":return"egy"+(n||t?" óra":" órája");case"hh":return r+(n||t?" óra":" órája");case"d":return"egy"+(n||t?" nap":" napja");case"dd":return r+(n||t?" nap":" napja");case"M":return"egy"+(n||t?" hónap":" hónapja");case"MM":return r+(n||t?" hónap":" hónapja");case"y":return"egy"+(n||t?" év":" éve");case"yy":return r+(n||t?" év":" éve")}return""}function n(e){return(e?"":"[múlt] ")+"["+t[this.day()]+"] LT[-kor]"}e.defineLocale("hu",{months:"január_február_március_április_május_június_július_augusztus_szeptember_október_november_december".split("_"),monthsShort:"jan._feb._márc._ápr._máj._jún._júl._aug._szept._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat".split("_"),weekdaysShort:"vas_hét_kedd_sze_csüt_pén_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"},meridiemParse:/de|du/i,isPM:function(e){return"u"===e.charAt(1).toLowerCase()},meridiem:function(e,t,a){return e<12?!0===a?"de":"DE":!0===a?"du":"DU"},calendar:{sameDay:"[ma] LT[-kor]",nextDay:"[holnap] LT[-kor]",nextWeek:function(){return n.call(this,!0)},lastDay:"[tegnap] LT[-kor]",lastWeek:function(){return n.call(this,!1)},sameElse:"L"},relativeTime:{future:"%s múlva",past:"%s",s:a,ss:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},X709:function(e,t,a){!function(e){"use strict";e.defineLocale("sv",{months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag".split("_"),weekdaysShort:"sön_mån_tis_ons_tor_fre_lör".split("_"),weekdaysMin:"sö_må_ti_on_to_fr_lö".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [kl.] HH:mm",LLLL:"dddd D MMMM YYYY [kl.] HH:mm",lll:"D MMM YYYY HH:mm",llll:"ddd D MMM YYYY HH:mm"},calendar:{sameDay:"[Idag] LT",nextDay:"[Imorgon] LT",lastDay:"[Igår] LT",nextWeek:"[På] dddd LT",lastWeek:"[I] dddd[s] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"för %s sedan",s:"några sekunder",ss:"%d sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en månad",MM:"%d månader",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}(\:e|\:a)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?":e":1===t||2===t?":a":":e")},week:{dow:1,doy:4}})}(a("wd/R"))},XDpg:function(e,t,a){!function(e){"use strict";e.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"下午"===t||"晚上"===t?e+12:e>=11?e:e+12},meridiem:function(e,t,a){var n=100*e+t;return n<600?"凌晨":n<900?"早上":n<1130?"上午":n<1230?"中午":n<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(e){return e.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(e){return this.week()!==e.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"周";default:return e}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}})}(a("wd/R"))},XLvN:function(e,t,a){!function(e){"use strict";e.defineLocale("te",{months:"జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్".split("_"),monthsShort:"జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.".split("_"),monthsParseExact:!0,weekdays:"ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం".split("_"),weekdaysShort:"ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని".split("_"),weekdaysMin:"ఆ_సో_మం_బు_గు_శు_శ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[నేడు] LT",nextDay:"[రేపు] LT",nextWeek:"dddd, LT",lastDay:"[నిన్న] LT",lastWeek:"[గత] dddd, LT",sameElse:"L"},relativeTime:{future:"%s లో",past:"%s క్రితం",s:"కొన్ని క్షణాలు",ss:"%d సెకన్లు",m:"ఒక నిమిషం",mm:"%d నిమిషాలు",h:"ఒక గంట",hh:"%d గంటలు",d:"ఒక రోజు",dd:"%d రోజులు",M:"ఒక నెల",MM:"%d నెలలు",y:"ఒక సంవత్సరం",yy:"%d సంవత్సరాలు"},dayOfMonthOrdinalParse:/\d{1,2}వ/,ordinal:"%dవ",meridiemParse:/రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,meridiemHour:function(e,t){return 12===e&&(e=0),"రాత్రి"===t?e<4?e:e+12:"ఉదయం"===t?e:"మధ్యాహ్నం"===t?e>=10?e:e+12:"సాయంత్రం"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"రాత్రి":e<10?"ఉదయం":e<17?"మధ్యాహ్నం":e<20?"సాయంత్రం":"రాత్రి"},week:{dow:0,doy:6}})}(a("wd/R"))},YRex:function(e,t,a){!function(e){"use strict";e.defineLocale("ug-cn",{months:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),monthsShort:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),weekdays:"يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە".split("_"),weekdaysShort:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),weekdaysMin:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY-يىلىM-ئاينىڭD-كۈنى",LLL:"YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm",LLLL:"dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm"},meridiemParse:/يېرىم كېچە|سەھەر|چۈشتىن بۇرۇن|چۈش|چۈشتىن كېيىن|كەچ/,meridiemHour:function(e,t){return 12===e&&(e=0),"يېرىم كېچە"===t||"سەھەر"===t||"چۈشتىن بۇرۇن"===t?e:"چۈشتىن كېيىن"===t||"كەچ"===t?e+12:e>=11?e:e+12},meridiem:function(e,t,a){var n=100*e+t;return n<600?"يېرىم كېچە":n<900?"سەھەر":n<1130?"چۈشتىن بۇرۇن":n<1230?"چۈش":n<1800?"چۈشتىن كېيىن":"كەچ"},calendar:{sameDay:"[بۈگۈن سائەت] LT",nextDay:"[ئەتە سائەت] LT",nextWeek:"[كېلەركى] dddd [سائەت] LT",lastDay:"[تۆنۈگۈن] LT",lastWeek:"[ئالدىنقى] dddd [سائەت] LT",sameElse:"L"},relativeTime:{future:"%s كېيىن",past:"%s بۇرۇن",s:"نەچچە سېكونت",ss:"%d سېكونت",m:"بىر مىنۇت",mm:"%d مىنۇت",h:"بىر سائەت",hh:"%d سائەت",d:"بىر كۈن",dd:"%d كۈن",M:"بىر ئاي",MM:"%d ئاي",y:"بىر يىل",yy:"%d يىل"},dayOfMonthOrdinalParse:/\d{1,2}(-كۈنى|-ئاي|-ھەپتە)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"-كۈنى";case"w":case"W":return e+"-ھەپتە";default:return e}},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:7}})}(a("wd/R"))},YuTi:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},Z4QM:function(e,t,a){!function(e){"use strict";var t=["جنوري","فيبروري","مارچ","اپريل","مئي","جون","جولاءِ","آگسٽ","سيپٽمبر","آڪٽوبر","نومبر","ڊسمبر"],a=["آچر","سومر","اڱارو","اربع","خميس","جمع","ڇنڇر"];e.defineLocale("sd",{months:t,monthsShort:t,weekdays:a,weekdaysShort:a,weekdaysMin:a,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(e){return"شام"===e},meridiem:function(e,t,a){return e<12?"صبح":"شام"},calendar:{sameDay:"[اڄ] LT",nextDay:"[سڀاڻي] LT",nextWeek:"dddd [اڳين هفتي تي] LT",lastDay:"[ڪالهه] LT",lastWeek:"[گزريل هفتي] dddd [تي] LT",sameElse:"L"},relativeTime:{future:"%s پوء",past:"%s اڳ",s:"چند سيڪنڊ",ss:"%d سيڪنڊ",m:"هڪ منٽ",mm:"%d منٽ",h:"هڪ ڪلاڪ",hh:"%d ڪلاڪ",d:"هڪ ڏينهن",dd:"%d ڏينهن",M:"هڪ مهينو",MM:"%d مهينا",y:"هڪ سال",yy:"%d سال"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}})}(a("wd/R"))},ZAMP:function(e,t,a){!function(e){"use strict";e.defineLocale("ms-my",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?e>=11?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,a){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}})}(a("wd/R"))},Zduo:function(e,t,a){!function(e){"use strict";e.defineLocale("eo",{months:"januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro".split("_"),monthsShort:"jan_feb_mart_apr_maj_jun_jul_aŭg_sept_okt_nov_dec".split("_"),weekdays:"dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato".split("_"),weekdaysShort:"dim_lun_mard_merk_ĵaŭ_ven_sab".split("_"),weekdaysMin:"di_lu_ma_me_ĵa_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"[la] D[-an de] MMMM, YYYY",LLL:"[la] D[-an de] MMMM, YYYY HH:mm",LLLL:"dddd[n], [la] D[-an de] MMMM, YYYY HH:mm",llll:"ddd, [la] D[-an de] MMM, YYYY HH:mm"},meridiemParse:/[ap]\.t\.m/i,isPM:function(e){return"p"===e.charAt(0).toLowerCase()},meridiem:function(e,t,a){return e>11?a?"p.t.m.":"P.T.M.":a?"a.t.m.":"A.T.M."},calendar:{sameDay:"[Hodiaŭ je] LT",nextDay:"[Morgaŭ je] LT",nextWeek:"dddd[n je] LT",lastDay:"[Hieraŭ je] LT",lastWeek:"[pasintan] dddd[n je] LT",sameElse:"L"},relativeTime:{future:"post %s",past:"antaŭ %s",s:"kelkaj sekundoj",ss:"%d sekundoj",m:"unu minuto",mm:"%d minutoj",h:"unu horo",hh:"%d horoj",d:"unu tago",dd:"%d tagoj",M:"unu monato",MM:"%d monatoj",y:"unu jaro",yy:"%d jaroj"},dayOfMonthOrdinalParse:/\d{1,2}a/,ordinal:"%da",week:{dow:1,doy:7}})}(a("wd/R"))},aIdf:function(e,t,a){!function(e){"use strict";function t(e,t,a){return e+" "+function(e,t){return 2===t?function(e){var t={m:"v",b:"v",d:"z"};return void 0===t[e.charAt(0)]?e:t[e.charAt(0)]+e.substring(1)}(e):e}({mm:"munutenn",MM:"miz",dd:"devezh"}[a],e)}var a=[/^gen/i,/^c[ʼ\']hwe/i,/^meu/i,/^ebr/i,/^mae/i,/^(mez|eve)/i,/^gou/i,/^eos/i,/^gwe/i,/^her/i,/^du/i,/^ker/i],n=/^(genver|c[ʼ\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu|gen|c[ʼ\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,r=[/^Su/i,/^Lu/i,/^Me([^r]|$)/i,/^Mer/i,/^Ya/i,/^Gw/i,/^Sa/i];e.defineLocale("br",{months:"Genver_Cʼhwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu".split("_"),monthsShort:"Gen_Cʼhwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker".split("_"),weekdays:"Sul_Lun_Meurzh_Mercʼher_Yaou_Gwener_Sadorn".split("_"),weekdaysShort:"Sul_Lun_Meu_Mer_Yao_Gwe_Sad".split("_"),weekdaysMin:"Su_Lu_Me_Mer_Ya_Gw_Sa".split("_"),weekdaysParse:r,fullWeekdaysParse:[/^sul/i,/^lun/i,/^meurzh/i,/^merc[ʼ\']her/i,/^yaou/i,/^gwener/i,/^sadorn/i],shortWeekdaysParse:[/^Sul/i,/^Lun/i,/^Meu/i,/^Mer/i,/^Yao/i,/^Gwe/i,/^Sad/i],minWeekdaysParse:r,monthsRegex:n,monthsShortRegex:n,monthsStrictRegex:/^(genver|c[ʼ\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu)/i,monthsShortStrictRegex:/^(gen|c[ʼ\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [a viz] MMMM YYYY",LLL:"D [a viz] MMMM YYYY HH:mm",LLLL:"dddd, D [a viz] MMMM YYYY HH:mm"},calendar:{sameDay:"[Hiziv da] LT",nextDay:"[Warcʼhoazh da] LT",nextWeek:"dddd [da] LT",lastDay:"[Decʼh da] LT",lastWeek:"dddd [paset da] LT",sameElse:"L"},relativeTime:{future:"a-benn %s",past:"%s ʼzo",s:"un nebeud segondennoù",ss:"%d eilenn",m:"ur vunutenn",mm:t,h:"un eur",hh:"%d eur",d:"un devezh",dd:t,M:"ur miz",MM:t,y:"ur bloaz",yy:function(e){switch(function e(t){return t>9?e(t%10):t}(e)){case 1:case 3:case 4:case 5:case 9:return e+" bloaz";default:return e+" vloaz"}}},dayOfMonthOrdinalParse:/\d{1,2}(añ|vet)/,ordinal:function(e){return e+(1===e?"añ":"vet")},week:{dow:1,doy:4},meridiemParse:/a.m.|g.m./,isPM:function(e){return"g.m."===e},meridiem:function(e,t,a){return e<12?"a.m.":"g.m."}})}(a("wd/R"))},aIsn:function(e,t,a){!function(e){"use strict";e.defineLocale("mi",{months:"Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_Pipiri_Hōngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea".split("_"),monthsShort:"Kohi_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki".split("_"),monthsRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,2}/i,weekdays:"Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei".split("_"),weekdaysShort:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),weekdaysMin:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [i] HH:mm",LLLL:"dddd, D MMMM YYYY [i] HH:mm"},calendar:{sameDay:"[i teie mahana, i] LT",nextDay:"[apopo i] LT",nextWeek:"dddd [i] LT",lastDay:"[inanahi i] LT",lastWeek:"dddd [whakamutunga i] LT",sameElse:"L"},relativeTime:{future:"i roto i %s",past:"%s i mua",s:"te hēkona ruarua",ss:"%d hēkona",m:"he meneti",mm:"%d meneti",h:"te haora",hh:"%d haora",d:"he ra",dd:"%d ra",M:"he marama",MM:"%d marama",y:"he tau",yy:"%d tau"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a("wd/R"))},aQkU:function(e,t,a){!function(e){"use strict";e.defineLocale("mk",{months:"јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември".split("_"),monthsShort:"јан_фев_мар_апр_мај_јун_јул_авг_сеп_окт_ное_дек".split("_"),weekdays:"недела_понеделник_вторник_среда_четврток_петок_сабота".split("_"),weekdaysShort:"нед_пон_вто_сре_чет_пет_саб".split("_"),weekdaysMin:"нe_пo_вт_ср_че_пе_сa".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Денес во] LT",nextDay:"[Утре во] LT",nextWeek:"[Во] dddd [во] LT",lastDay:"[Вчера во] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Изминатата] dddd [во] LT";case 1:case 2:case 4:case 5:return"[Изминатиот] dddd [во] LT"}},sameElse:"L"},relativeTime:{future:"за %s",past:"пред %s",s:"неколку секунди",ss:"%d секунди",m:"една минута",mm:"%d минути",h:"еден час",hh:"%d часа",d:"еден ден",dd:"%d дена",M:"еден месец",MM:"%d месеци",y:"една година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(e){var t=e%10,a=e%100;return 0===e?e+"-ев":0===a?e+"-ен":a>10&&a<20?e+"-ти":1===t?e+"-ви":2===t?e+"-ри":7===t||8===t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}})}(a("wd/R"))},b1Dy:function(e,t,a){!function(e){"use strict";e.defineLocale("en-nz",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(a("wd/R"))},bOMt:function(e,t,a){!function(e){"use strict";e.defineLocale("nb",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:!0,weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"sø._ma._ti._on._to._fr._lø.".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] HH:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[forrige] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"noen sekunder",ss:"%d sekunder",m:"ett minutt",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dager",w:"en uke",ww:"%d uker",M:"en måned",MM:"%d måneder",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},bXm7:function(e,t,a){!function(e){"use strict";var t={0:"-ші",1:"-ші",2:"-ші",3:"-ші",4:"-ші",5:"-ші",6:"-шы",7:"-ші",8:"-ші",9:"-шы",10:"-шы",20:"-шы",30:"-шы",40:"-шы",50:"-ші",60:"-шы",70:"-ші",80:"-ші",90:"-шы",100:"-ші"};e.defineLocale("kk",{months:"қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан".split("_"),monthsShort:"қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел".split("_"),weekdays:"жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі".split("_"),weekdaysShort:"жек_дүй_сей_сәр_бей_жұм_сен".split("_"),weekdaysMin:"жк_дй_сй_ср_бй_жм_сн".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгін сағат] LT",nextDay:"[Ертең сағат] LT",nextWeek:"dddd [сағат] LT",lastDay:"[Кеше сағат] LT",lastWeek:"[Өткен аптаның] dddd [сағат] LT",sameElse:"L"},relativeTime:{future:"%s ішінде",past:"%s бұрын",s:"бірнеше секунд",ss:"%d секунд",m:"бір минут",mm:"%d минут",h:"бір сағат",hh:"%d сағат",d:"бір күн",dd:"%d күн",M:"бір ай",MM:"%d ай",y:"бір жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(ші|шы)/,ordinal:function(e){return e+(t[e]||t[e%10]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(a("wd/R"))},bYM6:function(e,t,a){!function(e){"use strict";e.defineLocale("ar-tn",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}})}(a("wd/R"))},bpih:function(e,t,a){!function(e){"use strict";e.defineLocale("it",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:function(){return"[Oggi a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextDay:function(){return"[Domani a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextWeek:function(){return"dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastDay:function(){return"[Ieri a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastWeek:function(){switch(this.day()){case 0:return"[La scorsa] dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT";default:return"[Lo scorso] dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"}},sameElse:"L"},relativeTime:{future:"tra %s",past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",w:"una settimana",ww:"%d settimane",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a("wd/R"))},bxKX:function(e,t,a){!function(e){"use strict";e.defineLocale("it-ch",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){switch(this.day()){case 0:return"[la scorsa] dddd [alle] LT";default:return"[lo scorso] dddd [alle] LT"}},sameElse:"L"},relativeTime:{future:function(e){return(/^[0-9].+$/.test(e)?"tra":"in")+" "+e},past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a("wd/R"))},cRix:function(e,t,a){!function(e){"use strict";var t="jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.".split("_"),a="jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_");e.defineLocale("fy",{months:"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber".split("_"),monthsShort:function(e,n){return e?/-MMM-/.test(n)?a[e.month()]:t[e.month()]:t},monthsParseExact:!0,weekdays:"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon".split("_"),weekdaysShort:"si._mo._ti._wo._to._fr._so.".split("_"),weekdaysMin:"Si_Mo_Ti_Wo_To_Fr_So".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[hjoed om] LT",nextDay:"[moarn om] LT",nextWeek:"dddd [om] LT",lastDay:"[juster om] LT",lastWeek:"[ôfrûne] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oer %s",past:"%s lyn",s:"in pear sekonden",ss:"%d sekonden",m:"ien minút",mm:"%d minuten",h:"ien oere",hh:"%d oeren",d:"ien dei",dd:"%d dagen",M:"ien moanne",MM:"%d moannen",y:"ien jier",yy:"%d jierren"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(a("wd/R"))},czMo:function(e,t,a){!function(e){"use strict";e.defineLocale("en-il",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}})}(a("wd/R"))},dNwA:function(e,t,a){!function(e){"use strict";e.defineLocale("sw",{months:"Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des".split("_"),weekdays:"Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi".split("_"),weekdaysShort:"Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos".split("_"),weekdaysMin:"J2_J3_J4_J5_Al_Ij_J1".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"hh:mm A",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[leo saa] LT",nextDay:"[kesho saa] LT",nextWeek:"[wiki ijayo] dddd [saat] LT",lastDay:"[jana] LT",lastWeek:"[wiki iliyopita] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s baadaye",past:"tokea %s",s:"hivi punde",ss:"sekunde %d",m:"dakika moja",mm:"dakika %d",h:"saa limoja",hh:"masaa %d",d:"siku moja",dd:"siku %d",M:"mwezi mmoja",MM:"miezi %d",y:"mwaka mmoja",yy:"miaka %d"},week:{dow:1,doy:7}})}(a("wd/R"))},"e+ae":function(e,t,a){!function(e){"use strict";var t="január_február_marec_apríl_máj_jún_júl_august_september_október_november_december".split("_"),a="jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec".split("_");function n(e){return e>1&&e<5}function r(e,t,a,r){var s=e+" ";switch(a){case"s":return t||r?"pár sekúnd":"pár sekundami";case"ss":return t||r?s+(n(e)?"sekundy":"sekúnd"):s+"sekundami";case"m":return t?"minúta":r?"minútu":"minútou";case"mm":return t||r?s+(n(e)?"minúty":"minút"):s+"minútami";case"h":return t?"hodina":r?"hodinu":"hodinou";case"hh":return t||r?s+(n(e)?"hodiny":"hodín"):s+"hodinami";case"d":return t||r?"deň":"dňom";case"dd":return t||r?s+(n(e)?"dni":"dní"):s+"dňami";case"M":return t||r?"mesiac":"mesiacom";case"MM":return t||r?s+(n(e)?"mesiace":"mesiacov"):s+"mesiacmi";case"y":return t||r?"rok":"rokom";case"yy":return t||r?s+(n(e)?"roky":"rokov"):s+"rokmi"}}e.defineLocale("sk",{months:t,monthsShort:a,weekdays:"nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota".split("_"),weekdaysShort:"ne_po_ut_st_št_pi_so".split("_"),weekdaysMin:"ne_po_ut_st_št_pi_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm"},calendar:{sameDay:"[dnes o] LT",nextDay:"[zajtra o] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedeľu o] LT";case 1:case 2:return"[v] dddd [o] LT";case 3:return"[v stredu o] LT";case 4:return"[vo štvrtok o] LT";case 5:return"[v piatok o] LT";case 6:return"[v sobotu o] LT"}},lastDay:"[včera o] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulú nedeľu o] LT";case 1:case 2:return"[minulý] dddd [o] LT";case 3:return"[minulú stredu o] LT";case 4:case 5:return"[minulý] dddd [o] LT";case 6:return"[minulú sobotu o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pred %s",s:r,ss:r,m:r,mm:r,h:r,hh:r,d:r,dd:r,M:r,MM:r,y:r,yy:r},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},fzPg:function(e,t,a){!function(e){"use strict";e.defineLocale("yo",{months:"Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀".split("_"),monthsShort:"Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀".split("_"),weekdays:"Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta".split("_"),weekdaysShort:"Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá".split("_"),weekdaysMin:"Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Ònì ni] LT",nextDay:"[Ọ̀la ni] LT",nextWeek:"dddd [Ọsẹ̀ tón'bọ] [ni] LT",lastDay:"[Àna ni] LT",lastWeek:"dddd [Ọsẹ̀ tólọ́] [ni] LT",sameElse:"L"},relativeTime:{future:"ní %s",past:"%s kọjá",s:"ìsẹjú aayá die",ss:"aayá %d",m:"ìsẹjú kan",mm:"ìsẹjú %d",h:"wákati kan",hh:"wákati %d",d:"ọjọ́ kan",dd:"ọjọ́ %d",M:"osù kan",MM:"osù %d",y:"ọdún kan",yy:"ọdún %d"},dayOfMonthOrdinalParse:/ọjọ́\s\d{1,2}/,ordinal:"ọjọ́ %d",week:{dow:1,doy:4}})}(a("wd/R"))},gVVK:function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r=e+" ";switch(a){case"s":return t||n?"nekaj sekund":"nekaj sekundami";case"ss":return r+=1===e?t?"sekundo":"sekundi":2===e?t||n?"sekundi":"sekundah":e<5?t||n?"sekunde":"sekundah":"sekund";case"m":return t?"ena minuta":"eno minuto";case"mm":return r+=1===e?t?"minuta":"minuto":2===e?t||n?"minuti":"minutama":e<5?t||n?"minute":"minutami":t||n?"minut":"minutami";case"h":return t?"ena ura":"eno uro";case"hh":return r+=1===e?t?"ura":"uro":2===e?t||n?"uri":"urama":e<5?t||n?"ure":"urami":t||n?"ur":"urami";case"d":return t||n?"en dan":"enim dnem";case"dd":return r+=1===e?t||n?"dan":"dnem":2===e?t||n?"dni":"dnevoma":t||n?"dni":"dnevi";case"M":return t||n?"en mesec":"enim mesecem";case"MM":return r+=1===e?t||n?"mesec":"mesecem":2===e?t||n?"meseca":"mesecema":e<5?t||n?"mesece":"meseci":t||n?"mesecev":"meseci";case"y":return t||n?"eno leto":"enim letom";case"yy":return r+=1===e?t||n?"leto":"letom":2===e?t||n?"leti":"letoma":e<5?t||n?"leta":"leti":t||n?"let":"leti"}}e.defineLocale("sl",{months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),weekdaysShort:"ned._pon._tor._sre._čet._pet._sob.".split("_"),weekdaysMin:"ne_po_to_sr_če_pe_so".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danes ob] LT",nextDay:"[jutri ob] LT",nextWeek:function(){switch(this.day()){case 0:return"[v] [nedeljo] [ob] LT";case 3:return"[v] [sredo] [ob] LT";case 6:return"[v] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[v] dddd [ob] LT"}},lastDay:"[včeraj ob] LT",lastWeek:function(){switch(this.day()){case 0:return"[prejšnjo] [nedeljo] [ob] LT";case 3:return"[prejšnjo] [sredo] [ob] LT";case 6:return"[prejšnjo] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[prejšnji] dddd [ob] LT"}},sameElse:"L"},relativeTime:{future:"čez %s",past:"pred %s",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(a("wd/R"))},gekB:function(e,t,a){!function(e){"use strict";var t="nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän".split(" "),a=["nolla","yhden","kahden","kolmen","neljän","viiden","kuuden",t[7],t[8],t[9]];function n(e,n,r,s){var i="";switch(r){case"s":return s?"muutaman sekunnin":"muutama sekunti";case"ss":i=s?"sekunnin":"sekuntia";break;case"m":return s?"minuutin":"minuutti";case"mm":i=s?"minuutin":"minuuttia";break;case"h":return s?"tunnin":"tunti";case"hh":i=s?"tunnin":"tuntia";break;case"d":return s?"päivän":"päivä";case"dd":i=s?"päivän":"päivää";break;case"M":return s?"kuukauden":"kuukausi";case"MM":i=s?"kuukauden":"kuukautta";break;case"y":return s?"vuoden":"vuosi";case"yy":i=s?"vuoden":"vuotta"}return i=function(e,n){return e<10?n?a[e]:t[e]:e}(e,s)+" "+i}e.defineLocale("fi",{months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu".split("_"),weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"Do MMMM[ta] YYYY",LLL:"Do MMMM[ta] YYYY, [klo] HH.mm",LLLL:"dddd, Do MMMM[ta] YYYY, [klo] HH.mm",l:"D.M.YYYY",ll:"Do MMM YYYY",lll:"Do MMM YYYY, [klo] HH.mm",llll:"ddd, Do MMM YYYY, [klo] HH.mm"},calendar:{sameDay:"[tänään] [klo] LT",nextDay:"[huomenna] [klo] LT",nextWeek:"dddd [klo] LT",lastDay:"[eilen] [klo] LT",lastWeek:"[viime] dddd[na] [klo] LT",sameElse:"L"},relativeTime:{future:"%s päästä",past:"%s sitten",s:n,ss:n,m:n,mm:n,h:n,hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},gjCT:function(e,t,a){!function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},a={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"};e.defineLocale("ar-sa",{months:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,a){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return a[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:0,doy:6}})}(a("wd/R"))},hKrs:function(e,t,a){!function(e){"use strict";e.defineLocale("bg",{months:"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември".split("_"),monthsShort:"яну_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек".split("_"),weekdays:"неделя_понеделник_вторник_сряда_четвъртък_петък_събота".split("_"),weekdaysShort:"нед_пон_вто_сря_чет_пет_съб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Днес в] LT",nextDay:"[Утре в] LT",nextWeek:"dddd [в] LT",lastDay:"[Вчера в] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Миналата] dddd [в] LT";case 1:case 2:case 4:case 5:return"[Миналия] dddd [в] LT"}},sameElse:"L"},relativeTime:{future:"след %s",past:"преди %s",s:"няколко секунди",ss:"%d секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дена",w:"седмица",ww:"%d седмици",M:"месец",MM:"%d месеца",y:"година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(e){var t=e%10,a=e%100;return 0===e?e+"-ев":0===a?e+"-ен":a>10&&a<20?e+"-ти":1===t?e+"-ви":2===t?e+"-ри":7===t||8===t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}})}(a("wd/R"))},honF:function(e,t,a){!function(e){"use strict";var t={1:"၁",2:"၂",3:"၃",4:"၄",5:"၅",6:"၆",7:"၇",8:"၈",9:"၉",0:"၀"},a={"၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","၀":"0"};e.defineLocale("my",{months:"ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ".split("_"),monthsShort:"ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ".split("_"),weekdays:"တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ".split("_"),weekdaysShort:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),weekdaysMin:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ယနေ.] LT [မှာ]",nextDay:"[မနက်ဖြန်] LT [မှာ]",nextWeek:"dddd LT [မှာ]",lastDay:"[မနေ.က] LT [မှာ]",lastWeek:"[ပြီးခဲ့သော] dddd LT [မှာ]",sameElse:"L"},relativeTime:{future:"လာမည့် %s မှာ",past:"လွန်ခဲ့သော %s က",s:"စက္ကန်.အနည်းငယ်",ss:"%d စက္ကန့်",m:"တစ်မိနစ်",mm:"%d မိနစ်",h:"တစ်နာရီ",hh:"%d နာရီ",d:"တစ်ရက်",dd:"%d ရက်",M:"တစ်လ",MM:"%d လ",y:"တစ်နှစ်",yy:"%d နှစ်"},preparse:function(e){return e.replace(/[၁၂၃၄၅၆၇၈၉၀]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},week:{dow:1,doy:4}})}(a("wd/R"))},iEDd:function(e,t,a){!function(e){"use strict";e.defineLocale("gl",{months:"xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro".split("_"),monthsShort:"xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"domingo_luns_martes_mércores_xoves_venres_sábado".split("_"),weekdaysShort:"dom._lun._mar._mér._xov._ven._sáb.".split("_"),weekdaysMin:"do_lu_ma_mé_xo_ve_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoxe "+(1!==this.hours()?"ás":"á")+"] LT"},nextDay:function(){return"[mañá "+(1!==this.hours()?"ás":"á")+"] LT"},nextWeek:function(){return"dddd ["+(1!==this.hours()?"ás":"a")+"] LT"},lastDay:function(){return"[onte "+(1!==this.hours()?"á":"a")+"] LT"},lastWeek:function(){return"[o] dddd [pasado "+(1!==this.hours()?"ás":"a")+"] LT"},sameElse:"L"},relativeTime:{future:function(e){return 0===e.indexOf("un")?"n"+e:"en "+e},past:"hai %s",s:"uns segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"unha hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(a("wd/R"))},iYuL:function(e,t,a){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),a="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),n=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,n){return e?/-MMM-/.test(n)?a[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4},invalidDate:"Fecha inválida"})}(a("wd/R"))},jUeY:function(e,t,a){!function(e){"use strict";e.defineLocale("el",{monthsNominativeEl:"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος".split("_"),monthsGenitiveEl:"Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου".split("_"),months:function(e,t){return e?"string"==typeof t&&/D/.test(t.substring(0,t.indexOf("MMMM")))?this._monthsGenitiveEl[e.month()]:this._monthsNominativeEl[e.month()]:this._monthsNominativeEl},monthsShort:"Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ".split("_"),weekdays:"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο".split("_"),weekdaysShort:"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ".split("_"),weekdaysMin:"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα".split("_"),meridiem:function(e,t,a){return e>11?a?"μμ":"ΜΜ":a?"πμ":"ΠΜ"},isPM:function(e){return"μ"===(e+"").toLowerCase()[0]},meridiemParse:/[ΠΜ]\.?Μ?\.?/i,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendarEl:{sameDay:"[Σήμερα {}] LT",nextDay:"[Αύριο {}] LT",nextWeek:"dddd [{}] LT",lastDay:"[Χθες {}] LT",lastWeek:function(){switch(this.day()){case 6:return"[το προηγούμενο] dddd [{}] LT";default:return"[την προηγούμενη] dddd [{}] LT"}},sameElse:"L"},calendar:function(e,t){var a,n=this._calendarEl[e],r=t&&t.hours();return a=n,("undefined"!=typeof Function&&a instanceof Function||"[object Function]"===Object.prototype.toString.call(a))&&(n=n.apply(t)),n.replace("{}",r%12==1?"στη":"στις")},relativeTime:{future:"σε %s",past:"%s πριν",s:"λίγα δευτερόλεπτα",ss:"%d δευτερόλεπτα",m:"ένα λεπτό",mm:"%d λεπτά",h:"μία ώρα",hh:"%d ώρες",d:"μία μέρα",dd:"%d μέρες",M:"ένας μήνας",MM:"%d μήνες",y:"ένας χρόνος",yy:"%d χρόνια"},dayOfMonthOrdinalParse:/\d{1,2}η/,ordinal:"%dη",week:{dow:1,doy:4}})}(a("wd/R"))},jVdC:function(e,t,a){!function(e){"use strict";var t="styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień".split("_"),a="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia".split("_"),n=[/^sty/i,/^lut/i,/^mar/i,/^kwi/i,/^maj/i,/^cze/i,/^lip/i,/^sie/i,/^wrz/i,/^paź/i,/^lis/i,/^gru/i];function r(e){return e%10<5&&e%10>1&&~~(e/10)%10!=1}function s(e,t,a){var n=e+" ";switch(a){case"ss":return n+(r(e)?"sekundy":"sekund");case"m":return t?"minuta":"minutę";case"mm":return n+(r(e)?"minuty":"minut");case"h":return t?"godzina":"godzinę";case"hh":return n+(r(e)?"godziny":"godzin");case"ww":return n+(r(e)?"tygodnie":"tygodni");case"MM":return n+(r(e)?"miesiące":"miesięcy");case"yy":return n+(r(e)?"lata":"lat")}}e.defineLocale("pl",{months:function(e,n){return e?/D MMMM/.test(n)?a[e.month()]:t[e.month()]:t},monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru".split("_"),monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota".split("_"),weekdaysShort:"ndz_pon_wt_śr_czw_pt_sob".split("_"),weekdaysMin:"Nd_Pn_Wt_Śr_Cz_Pt_So".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Dziś o] LT",nextDay:"[Jutro o] LT",nextWeek:function(){switch(this.day()){case 0:return"[W niedzielę o] LT";case 2:return"[We wtorek o] LT";case 3:return"[W środę o] LT";case 6:return"[W sobotę o] LT";default:return"[W] dddd [o] LT"}},lastDay:"[Wczoraj o] LT",lastWeek:function(){switch(this.day()){case 0:return"[W zeszłą niedzielę o] LT";case 3:return"[W zeszłą środę o] LT";case 6:return"[W zeszłą sobotę o] LT";default:return"[W zeszły] dddd [o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",ss:s,m:s,mm:s,h:s,hh:s,d:"1 dzień",dd:"%d dni",w:"tydzień",ww:s,M:"miesiąc",MM:s,y:"rok",yy:s},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},jfSC:function(e,t,a){!function(e){"use strict";var t={1:"۱",2:"۲",3:"۳",4:"۴",5:"۵",6:"۶",7:"۷",8:"۸",9:"۹",0:"۰"},a={"۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","۰":"0"};e.defineLocale("fa",{months:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),monthsShort:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),weekdays:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysShort:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysMin:"ی_د_س_چ_پ_ج_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/قبل از ظهر|بعد از ظهر/,isPM:function(e){return/بعد از ظهر/.test(e)},meridiem:function(e,t,a){return e<12?"قبل از ظهر":"بعد از ظهر"},calendar:{sameDay:"[امروز ساعت] LT",nextDay:"[فردا ساعت] LT",nextWeek:"dddd [ساعت] LT",lastDay:"[دیروز ساعت] LT",lastWeek:"dddd [پیش] [ساعت] LT",sameElse:"L"},relativeTime:{future:"در %s",past:"%s پیش",s:"چند ثانیه",ss:"%d ثانیه",m:"یک دقیقه",mm:"%d دقیقه",h:"یک ساعت",hh:"%d ساعت",d:"یک روز",dd:"%d روز",M:"یک ماه",MM:"%d ماه",y:"یک سال",yy:"%d سال"},preparse:function(e){return e.replace(/[۰-۹]/g,(function(e){return a[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},dayOfMonthOrdinalParse:/\d{1,2}م/,ordinal:"%dم",week:{dow:6,doy:12}})}(a("wd/R"))},jnO4:function(e,t,a){!function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},a={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},n=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},r={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},s=function(e){return function(t,a,s,i){var o=n(t),d=r[e][n(t)];return 2===o&&(d=d[a?0:1]),d.replace(/%d/i,t)}},i=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];e.defineLocale("ar",{months:i,monthsShort:i,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,a){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:s("s"),ss:s("s"),m:s("m"),mm:s("m"),h:s("h"),hh:s("h"),d:s("d"),dd:s("d"),M:s("M"),MM:s("M"),y:s("y"),yy:s("y")},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return a[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}})}(a("wd/R"))},kEOa:function(e,t,a){!function(e){"use strict";var t={1:"১",2:"২",3:"৩",4:"৪",5:"৫",6:"৬",7:"৭",8:"৮",9:"৯",0:"০"},a={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"};e.defineLocale("bn",{months:"জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(e){return e.replace(/[১২৩৪৫৬৭৮৯০]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/রাত|সকাল|দুপুর|বিকাল|রাত/,meridiemHour:function(e,t){return 12===e&&(e=0),"রাত"===t&&e>=4||"দুপুর"===t&&e<5||"বিকাল"===t?e+12:e},meridiem:function(e,t,a){return e<4?"রাত":e<10?"সকাল":e<17?"দুপুর":e<20?"বিকাল":"রাত"},week:{dow:0,doy:6}})}(a("wd/R"))},kOpN:function(e,t,a){!function(e){"use strict";e.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,a){var n=100*e+t;return n<600?"凌晨":n<900?"早上":n<1130?"上午":n<1230?"中午":n<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})}(a("wd/R"))},l5ep:function(e,t,a){!function(e){"use strict";e.defineLocale("cy",{months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Heddiw am] LT",nextDay:"[Yfory am] LT",nextWeek:"dddd [am] LT",lastDay:"[Ddoe am] LT",lastWeek:"dddd [diwethaf am] LT",sameElse:"L"},relativeTime:{future:"mewn %s",past:"%s yn ôl",s:"ychydig eiliadau",ss:"%d eiliad",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"},dayOfMonthOrdinalParse:/\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,ordinal:function(e){var t="";return e>20?t=40===e||50===e||60===e||80===e||100===e?"fed":"ain":e>0&&(t=["","af","il","ydd","ydd","ed","ed","ed","fed","fed","fed","eg","fed","eg","eg","fed","eg","eg","fed","eg","fed"][e]),e+t},week:{dow:1,doy:4}})}(a("wd/R"))},lXzo:function(e,t,a){!function(e){"use strict";function t(e,t,a){var n,r;return"m"===a?t?"минута":"минуту":e+" "+(n=+e,r={ss:t?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:t?"минута_минуты_минут":"минуту_минуты_минут",hh:"час_часа_часов",dd:"день_дня_дней",ww:"неделя_недели_недель",MM:"месяц_месяца_месяцев",yy:"год_года_лет"}[a].split("_"),n%10==1&&n%100!=11?r[0]:n%10>=2&&n%10<=4&&(n%100<10||n%100>=20)?r[1]:r[2])}var a=[/^янв/i,/^фев/i,/^мар/i,/^апр/i,/^ма[йя]/i,/^июн/i,/^июл/i,/^авг/i,/^сен/i,/^окт/i,/^ноя/i,/^дек/i];e.defineLocale("ru",{months:{format:"января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря".split("_"),standalone:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_")},monthsShort:{format:"янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.".split("_"),standalone:"янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.".split("_")},weekdays:{standalone:"воскресенье_понедельник_вторник_среда_четверг_пятница_суббота".split("_"),format:"воскресенье_понедельник_вторник_среду_четверг_пятницу_субботу".split("_"),isFormat:/\[ ?[Вв] ?(?:прошлую|следующую|эту)? ?] ?dddd/},weekdaysShort:"вс_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"вс_пн_вт_ср_чт_пт_сб".split("_"),monthsParse:a,longMonthsParse:a,shortMonthsParse:a,monthsRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsShortRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsStrictRegex:/^(январ[яь]|феврал[яь]|марта?|апрел[яь]|ма[яй]|июн[яь]|июл[яь]|августа?|сентябр[яь]|октябр[яь]|ноябр[яь]|декабр[яь])/i,monthsShortStrictRegex:/^(янв\.|февр?\.|мар[т.]|апр\.|ма[яй]|июн[ья.]|июл[ья.]|авг\.|сент?\.|окт\.|нояб?\.|дек\.)/i,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., H:mm",LLLL:"dddd, D MMMM YYYY г., H:mm"},calendar:{sameDay:"[Сегодня, в] LT",nextDay:"[Завтра, в] LT",lastDay:"[Вчера, в] LT",nextWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В следующее] dddd, [в] LT";case 1:case 2:case 4:return"[В следующий] dddd, [в] LT";case 3:case 5:case 6:return"[В следующую] dddd, [в] LT"}},lastWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В прошлое] dddd, [в] LT";case 1:case 2:case 4:return"[В прошлый] dddd, [в] LT";case 3:case 5:case 6:return"[В прошлую] dddd, [в] LT"}},sameElse:"L"},relativeTime:{future:"через %s",past:"%s назад",s:"несколько секунд",ss:t,m:t,mm:t,h:"час",hh:t,d:"день",dd:t,w:"неделя",ww:t,M:"месяц",MM:t,y:"год",yy:t},meridiemParse:/ночи|утра|дня|вечера/i,isPM:function(e){return/^(дня|вечера)$/.test(e)},meridiem:function(e,t,a){return e<4?"ночи":e<12?"утра":e<17?"дня":"вечера"},dayOfMonthOrdinalParse:/\d{1,2}-(й|го|я)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":return e+"-й";case"D":return e+"-го";case"w":case"W":return e+"-я";default:return e}},week:{dow:1,doy:4}})}(a("wd/R"))},lYtQ:function(e,t,a){!function(e){"use strict";function t(e,t,a,n){switch(a){case"s":return t?"хэдхэн секунд":"хэдхэн секундын";case"ss":return e+(t?" секунд":" секундын");case"m":case"mm":return e+(t?" минут":" минутын");case"h":case"hh":return e+(t?" цаг":" цагийн");case"d":case"dd":return e+(t?" өдөр":" өдрийн");case"M":case"MM":return e+(t?" сар":" сарын");case"y":case"yy":return e+(t?" жил":" жилийн");default:return e}}e.defineLocale("mn",{months:"Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар".split("_"),monthsShort:"1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар".split("_"),monthsParseExact:!0,weekdays:"Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба".split("_"),weekdaysShort:"Ням_Дав_Мяг_Лха_Пүр_Баа_Бям".split("_"),weekdaysMin:"Ня_Да_Мя_Лх_Пү_Ба_Бя".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY оны MMMMын D",LLL:"YYYY оны MMMMын D HH:mm",LLLL:"dddd, YYYY оны MMMMын D HH:mm"},meridiemParse:/ҮӨ|ҮХ/i,isPM:function(e){return"ҮХ"===e},meridiem:function(e,t,a){return e<12?"ҮӨ":"ҮХ"},calendar:{sameDay:"[Өнөөдөр] LT",nextDay:"[Маргааш] LT",nextWeek:"[Ирэх] dddd LT",lastDay:"[Өчигдөр] LT",lastWeek:"[Өнгөрсөн] dddd LT",sameElse:"L"},relativeTime:{future:"%s дараа",past:"%s өмнө",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2} өдөр/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+" өдөр";default:return e}}})}(a("wd/R"))},lgnt:function(e,t,a){!function(e){"use strict";var t={0:"-чү",1:"-чи",2:"-чи",3:"-чү",4:"-чү",5:"-чи",6:"-чы",7:"-чи",8:"-чи",9:"-чу",10:"-чу",20:"-чы",30:"-чу",40:"-чы",50:"-чү",60:"-чы",70:"-чи",80:"-чи",90:"-чу",100:"-чү"};e.defineLocale("ky",{months:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_"),monthsShort:"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек".split("_"),weekdays:"Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби".split("_"),weekdaysShort:"Жек_Дүй_Шей_Шар_Бей_Жум_Ише".split("_"),weekdaysMin:"Жк_Дй_Шй_Шр_Бй_Жм_Иш".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгүн саат] LT",nextDay:"[Эртең саат] LT",nextWeek:"dddd [саат] LT",lastDay:"[Кечээ саат] LT",lastWeek:"[Өткөн аптанын] dddd [күнү] [саат] LT",sameElse:"L"},relativeTime:{future:"%s ичинде",past:"%s мурун",s:"бирнече секунд",ss:"%d секунд",m:"бир мүнөт",mm:"%d мүнөт",h:"бир саат",hh:"%d саат",d:"бир күн",dd:"%d күн",M:"бир ай",MM:"%d ай",y:"бир жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(чи|чы|чү|чу)/,ordinal:function(e){return e+(t[e]||t[e%10]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(a("wd/R"))},loYQ:function(e,t,a){!function(e){"use strict";var t={1:"১",2:"২",3:"৩",4:"৪",5:"৫",6:"৬",7:"৭",8:"৮",9:"৯",0:"০"},a={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"};e.defineLocale("bn-bd",{months:"জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(e){return e.replace(/[১২৩৪৫৬৭৮৯০]/g,(function(e){return a[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/রাত|ভোর|সকাল|দুপুর|বিকাল|সন্ধ্যা|রাত/,meridiemHour:function(e,t){return 12===e&&(e=0),"রাত"===t?e<4?e:e+12:"ভোর"===t||"সকাল"===t?e:"দুপুর"===t?e>=3?e:e+12:"বিকাল"===t||"সন্ধ্যা"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"রাত":e<6?"ভোর":e<12?"সকাল":e<15?"দুপুর":e<18?"বিকাল":e<20?"সন্ধ্যা":"রাত"},week:{dow:0,doy:6}})}(a("wd/R"))},lyxo:function(e,t,a){!function(e){"use strict";function t(e,t,a){var n=" ";return(e%100>=20||e>=100&&e%100==0)&&(n=" de "),e+n+{ss:"secunde",mm:"minute",hh:"ore",dd:"zile",ww:"săptămâni",MM:"luni",yy:"ani"}[a]}e.defineLocale("ro",{months:"ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie".split("_"),monthsShort:"ian._feb._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"duminică_luni_marți_miercuri_joi_vineri_sâmbătă".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_Sâm".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_Sâ".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[azi la] LT",nextDay:"[mâine la] LT",nextWeek:"dddd [la] LT",lastDay:"[ieri la] LT",lastWeek:"[fosta] dddd [la] LT",sameElse:"L"},relativeTime:{future:"peste %s",past:"%s în urmă",s:"câteva secunde",ss:t,m:"un minut",mm:t,h:"o oră",hh:t,d:"o zi",dd:t,w:"o săptămână",ww:t,M:"o lună",MM:t,y:"un an",yy:t},week:{dow:1,doy:7}})}(a("wd/R"))},nyYc:function(e,t,a){!function(e){"use strict";var t=/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,a=[/^janv/i,/^févr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^août/i,/^sept/i,/^oct/i,/^nov/i,/^déc/i];e.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsRegex:t,monthsShortRegex:t,monthsStrictRegex:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,monthsShortStrictRegex:/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})}(a("wd/R"))},o1bE:function(e,t,a){!function(e){"use strict";var t=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},a={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},n=function(e){return function(n,r,s,i){var o=t(n),d=a[e][t(n)];return 2===o&&(d=d[r?0:1]),d.replace(/%d/i,n)}},r=["جانفي","فيفري","مارس","أفريل","ماي","جوان","جويلية","أوت","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];e.defineLocale("ar-dz",{months:r,monthsShort:r,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,a){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:n("s"),ss:n("s"),m:n("m"),mm:n("m"),h:n("h"),hh:n("h"),d:n("d"),dd:n("d"),M:n("M"),MM:n("M"),y:n("y"),yy:n("y")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:0,doy:4}})}(a("wd/R"))},"p/rL":function(e,t,a){!function(e){"use strict";e.defineLocale("bm",{months:"Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo".split("_"),monthsShort:"Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des".split("_"),weekdays:"Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri".split("_"),weekdaysShort:"Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib".split("_"),weekdaysMin:"Ka_Nt_Ta_Ar_Al_Ju_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"MMMM [tile] D [san] YYYY",LLL:"MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm",LLLL:"dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm"},calendar:{sameDay:"[Bi lɛrɛ] LT",nextDay:"[Sini lɛrɛ] LT",nextWeek:"dddd [don lɛrɛ] LT",lastDay:"[Kunu lɛrɛ] LT",lastWeek:"dddd [tɛmɛnen lɛrɛ] LT",sameElse:"L"},relativeTime:{future:"%s kɔnɔ",past:"a bɛ %s bɔ",s:"sanga dama dama",ss:"sekondi %d",m:"miniti kelen",mm:"miniti %d",h:"lɛrɛ kelen",hh:"lɛrɛ %d",d:"tile kelen",dd:"tile %d",M:"kalo kelen",MM:"kalo %d",y:"san kelen",yy:"san %d"},week:{dow:1,doy:4}})}(a("wd/R"))},qvJo:function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={s:["थोडया सॅकंडांनी","थोडे सॅकंड"],ss:[e+" सॅकंडांनी",e+" सॅकंड"],m:["एका मिणटान","एक मिनूट"],mm:[e+" मिणटांनी",e+" मिणटां"],h:["एका वरान","एक वर"],hh:[e+" वरांनी",e+" वरां"],d:["एका दिसान","एक दीस"],dd:[e+" दिसांनी",e+" दीस"],M:["एका म्हयन्यान","एक म्हयनो"],MM:[e+" म्हयन्यानी",e+" म्हयने"],y:["एका वर्सान","एक वर्स"],yy:[e+" वर्सांनी",e+" वर्सां"]};return n?r[a][0]:r[a][1]}e.defineLocale("gom-deva",{months:{standalone:"जानेवारी_फेब्रुवारी_मार्च_एप्रील_मे_जून_जुलय_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),format:"जानेवारीच्या_फेब्रुवारीच्या_मार्चाच्या_एप्रीलाच्या_मेयाच्या_जूनाच्या_जुलयाच्या_ऑगस्टाच्या_सप्टेंबराच्या_ऑक्टोबराच्या_नोव्हेंबराच्या_डिसेंबराच्या".split("_"),isFormat:/MMMM(\s)+D[oD]?/},monthsShort:"जाने._फेब्रु._मार्च_एप्री._मे_जून_जुल._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:!0,weekdays:"आयतार_सोमार_मंगळार_बुधवार_बिरेस्तार_सुक्रार_शेनवार".split("_"),weekdaysShort:"आयत._सोम._मंगळ._बुध._ब्रेस्त._सुक्र._शेन.".split("_"),weekdaysMin:"आ_सो_मं_बु_ब्रे_सु_शे".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [वाजतां]",LTS:"A h:mm:ss [वाजतां]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [वाजतां]",LLLL:"dddd, MMMM Do, YYYY, A h:mm [वाजतां]",llll:"ddd, D MMM YYYY, A h:mm [वाजतां]"},calendar:{sameDay:"[आयज] LT",nextDay:"[फाल्यां] LT",nextWeek:"[फुडलो] dddd[,] LT",lastDay:"[काल] LT",lastWeek:"[फाटलो] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s आदीं",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}(वेर)/,ordinal:function(e,t){switch(t){case"D":return e+"वेर";default:case"M":case"Q":case"DDD":case"d":case"w":case"W":return e}},week:{dow:0,doy:3},meridiemParse:/राती|सकाळीं|दनपारां|सांजे/,meridiemHour:function(e,t){return 12===e&&(e=0),"राती"===t?e<4?e:e+12:"सकाळीं"===t?e:"दनपारां"===t?e>12?e:e+12:"सांजे"===t?e+12:void 0},meridiem:function(e,t,a){return e<4?"राती":e<12?"सकाळीं":e<16?"दनपारां":e<20?"सांजे":"राती"}})}(a("wd/R"))},raLr:function(e,t,a){!function(e){"use strict";function t(e,t,a){var n,r;return"m"===a?t?"хвилина":"хвилину":"h"===a?t?"година":"годину":e+" "+(n=+e,r={ss:t?"секунда_секунди_секунд":"секунду_секунди_секунд",mm:t?"хвилина_хвилини_хвилин":"хвилину_хвилини_хвилин",hh:t?"година_години_годин":"годину_години_годин",dd:"день_дні_днів",MM:"місяць_місяці_місяців",yy:"рік_роки_років"}[a].split("_"),n%10==1&&n%100!=11?r[0]:n%10>=2&&n%10<=4&&(n%100<10||n%100>=20)?r[1]:r[2])}function a(e){return function(){return e+"о"+(11===this.hours()?"б":"")+"] LT"}}e.defineLocale("uk",{months:{format:"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня".split("_"),standalone:"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень".split("_")},monthsShort:"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд".split("_"),weekdays:function(e,t){var a={nominative:"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота".split("_"),accusative:"неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу".split("_"),genitive:"неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи".split("_")};return!0===e?a.nominative.slice(1,7).concat(a.nominative.slice(0,1)):e?a[/(\[[ВвУу]\]) ?dddd/.test(t)?"accusative":/\[?(?:минулої|наступної)? ?\] ?dddd/.test(t)?"genitive":"nominative"][e.day()]:a.nominative},weekdaysShort:"нд_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY р.",LLL:"D MMMM YYYY р., HH:mm",LLLL:"dddd, D MMMM YYYY р., HH:mm"},calendar:{sameDay:a("[Сьогодні "),nextDay:a("[Завтра "),lastDay:a("[Вчора "),nextWeek:a("[У] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return a("[Минулої] dddd [").call(this);case 1:case 2:case 4:return a("[Минулого] dddd [").call(this)}},sameElse:"L"},relativeTime:{future:"за %s",past:"%s тому",s:"декілька секунд",ss:t,m:t,mm:t,h:"годину",hh:t,d:"день",dd:t,M:"місяць",MM:t,y:"рік",yy:t},meridiemParse:/ночі|ранку|дня|вечора/,isPM:function(e){return/^(дня|вечора)$/.test(e)},meridiem:function(e,t,a){return e<4?"ночі":e<12?"ранку":e<17?"дня":"вечора"},dayOfMonthOrdinalParse:/\d{1,2}-(й|го)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e+"-й";case"D":return e+"-го";default:return e}},week:{dow:1,doy:7}})}(a("wd/R"))},"s+uk":function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[a][0]:r[a][1]}e.defineLocale("de-at",{months:"Jänner_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jän._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},sp3z:function(e,t,a){!function(e){"use strict";e.defineLocale("lo",{months:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),monthsShort:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),weekdays:"ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysShort:"ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysMin:"ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"ວັນdddd D MMMM YYYY HH:mm"},meridiemParse:/ຕອນເຊົ້າ|ຕອນແລງ/,isPM:function(e){return"ຕອນແລງ"===e},meridiem:function(e,t,a){return e<12?"ຕອນເຊົ້າ":"ຕອນແລງ"},calendar:{sameDay:"[ມື້ນີ້ເວລາ] LT",nextDay:"[ມື້ອື່ນເວລາ] LT",nextWeek:"[ວັນ]dddd[ໜ້າເວລາ] LT",lastDay:"[ມື້ວານນີ້ເວລາ] LT",lastWeek:"[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT",sameElse:"L"},relativeTime:{future:"ອີກ %s",past:"%sຜ່ານມາ",s:"ບໍ່ເທົ່າໃດວິນາທີ",ss:"%d ວິນາທີ",m:"1 ນາທີ",mm:"%d ນາທີ",h:"1 ຊົ່ວໂມງ",hh:"%d ຊົ່ວໂມງ",d:"1 ມື້",dd:"%d ມື້",M:"1 ເດືອນ",MM:"%d ເດືອນ",y:"1 ປີ",yy:"%d ປີ"},dayOfMonthOrdinalParse:/(ທີ່)\d{1,2}/,ordinal:function(e){return"ທີ່"+e}})}(a("wd/R"))},"t+mt":function(e,t,a){!function(e){"use strict";e.defineLocale("en-sg",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(a("wd/R"))},tGlX:function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[a][0]:r[a][1]}e.defineLocale("de",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},tT3J:function(e,t,a){!function(e){"use strict";e.defineLocale("tzm-latn",{months:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),monthsShort:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),weekdays:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysShort:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysMin:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[asdkh g] LT",nextDay:"[aska g] LT",nextWeek:"dddd [g] LT",lastDay:"[assant g] LT",lastWeek:"dddd [g] LT",sameElse:"L"},relativeTime:{future:"dadkh s yan %s",past:"yan %s",s:"imik",ss:"%d imik",m:"minuḍ",mm:"%d minuḍ",h:"saɛa",hh:"%d tassaɛin",d:"ass",dd:"%d ossan",M:"ayowr",MM:"%d iyyirn",y:"asgas",yy:"%d isgasn"},week:{dow:6,doy:12}})}(a("wd/R"))},tUCv:function(e,t,a){!function(e){"use strict";e.defineLocale("jv",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des".split("_"),weekdays:"Minggu_Senen_Seloso_Rebu_Kemis_Jemuwah_Septu".split("_"),weekdaysShort:"Min_Sen_Sel_Reb_Kem_Jem_Sep".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sp".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/enjing|siyang|sonten|ndalu/,meridiemHour:function(e,t){return 12===e&&(e=0),"enjing"===t?e:"siyang"===t?e>=11?e:e+12:"sonten"===t||"ndalu"===t?e+12:void 0},meridiem:function(e,t,a){return e<11?"enjing":e<15?"siyang":e<19?"sonten":"ndalu"},calendar:{sameDay:"[Dinten puniko pukul] LT",nextDay:"[Mbenjang pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kala wingi pukul] LT",lastWeek:"dddd [kepengker pukul] LT",sameElse:"L"},relativeTime:{future:"wonten ing %s",past:"%s ingkang kepengker",s:"sawetawis detik",ss:"%d detik",m:"setunggal menit",mm:"%d menit",h:"setunggal jam",hh:"%d jam",d:"sedinten",dd:"%d dinten",M:"sewulan",MM:"%d wulan",y:"setaun",yy:"%d taun"},week:{dow:1,doy:7}})}(a("wd/R"))},tbfe:function(e,t,a){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),a="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),n=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es-mx",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,n){return e?/-MMM-/.test(n)?a[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:0,doy:4},invalidDate:"Fecha inválida"})}(a("wd/R"))},u3GI:function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[a][0]:r[a][1]}e.defineLocale("de-ch",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},uEye:function(e,t,a){!function(e){"use strict";e.defineLocale("nn",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:!0,weekdays:"sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag".split("_"),weekdaysShort:"su._må._ty._on._to._fr._lau.".split("_"),weekdaysMin:"su_må_ty_on_to_fr_la".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[I dag klokka] LT",nextDay:"[I morgon klokka] LT",nextWeek:"dddd [klokka] LT",lastDay:"[I går klokka] LT",lastWeek:"[Føregåande] dddd [klokka] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s sidan",s:"nokre sekund",ss:"%d sekund",m:"eit minutt",mm:"%d minutt",h:"ein time",hh:"%d timar",d:"ein dag",dd:"%d dagar",w:"ei veke",ww:"%d veker",M:"ein månad",MM:"%d månader",y:"eit år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},uXwI:function(e,t,a){!function(e){"use strict";var t={ss:"sekundes_sekundēm_sekunde_sekundes".split("_"),m:"minūtes_minūtēm_minūte_minūtes".split("_"),mm:"minūtes_minūtēm_minūte_minūtes".split("_"),h:"stundas_stundām_stunda_stundas".split("_"),hh:"stundas_stundām_stunda_stundas".split("_"),d:"dienas_dienām_diena_dienas".split("_"),dd:"dienas_dienām_diena_dienas".split("_"),M:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),MM:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),y:"gada_gadiem_gads_gadi".split("_"),yy:"gada_gadiem_gads_gadi".split("_")};function a(e,t,a){return a?t%10==1&&t%100!=11?e[2]:e[3]:t%10==1&&t%100!=11?e[0]:e[1]}function n(e,n,r){return e+" "+a(t[r],e,n)}function r(e,n,r){return a(t[r],e,n)}e.defineLocale("lv",{months:"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris".split("_"),monthsShort:"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec".split("_"),weekdays:"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena".split("_"),weekdaysShort:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysMin:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY.",LL:"YYYY. [gada] D. MMMM",LLL:"YYYY. [gada] D. MMMM, HH:mm",LLLL:"YYYY. [gada] D. MMMM, dddd, HH:mm"},calendar:{sameDay:"[Šodien pulksten] LT",nextDay:"[Rīt pulksten] LT",nextWeek:"dddd [pulksten] LT",lastDay:"[Vakar pulksten] LT",lastWeek:"[Pagājušā] dddd [pulksten] LT",sameElse:"L"},relativeTime:{future:"pēc %s",past:"pirms %s",s:function(e,t){return t?"dažas sekundes":"dažām sekundēm"},ss:n,m:r,mm:n,h:r,hh:n,d:r,dd:n,M:r,MM:n,y:r,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},uqS5:function(e,t,a){"use strict";a.d(t,"a",(function(){return I}));var n,r=a("XuX8"),s=a.n(r),i=a("xjcK"),o=a("6GPe"),d=a("AFYn"),u=a("m/oX"),l=a("pyNs"),_=a("m3aq"),c=a("kGy3"),m=a("a3f1"),h=a("WPLV"),f=a("2C+6"),p=a("z3V6"),M=a("STsD"),y=a("kO/s"),g=a("YC3Q"),L=a("jBgq"),v=a("c4aD"),Y=a("8p45"),w=a("zio1");function b(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,n)}return a}function k(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?b(Object(a),!0).forEach((function(t){D(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):b(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function D(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}var T=Object(m.d)(i.q,"request-state"),S=Object(m.d)(i.q,"toggle"),j=Object(m.e)(i.q,"state"),x=Object(m.e)(i.q,"sync-state"),H=Object(h.a)("visible",{type:l.g,defaultValue:!1,event:d.d}),P=H.mixin,O=H.props,C=H.prop,E=H.event,R=Object(p.d)(Object(f.m)(k(k(k({},y.b),O),{},{ariaLabel:Object(p.c)(l.t),ariaLabelledby:Object(p.c)(l.t),backdrop:Object(p.c)(l.g,!1),backdropVariant:Object(p.c)(l.t,"dark"),bgVariant:Object(p.c)(l.t,"light"),bodyClass:Object(p.c)(l.e),closeLabel:Object(p.c)(l.t),footerClass:Object(p.c)(l.e),headerClass:Object(p.c)(l.e),lazy:Object(p.c)(l.g,!1),noCloseOnBackdrop:Object(p.c)(l.g,!1),noCloseOnEsc:Object(p.c)(l.g,!1),noCloseOnRouteChange:Object(p.c)(l.g,!1),noEnforceFocus:Object(p.c)(l.g,!1),noHeader:Object(p.c)(l.g,!1),noHeaderClose:Object(p.c)(l.g,!1),noSlide:Object(p.c)(l.g,!1),right:Object(p.c)(l.g,!1),shadow:Object(p.c)(l.j,!1),sidebarClass:Object(p.c)(l.e),tag:Object(p.c)(l.t,"div"),textVariant:Object(p.c)(l.t,"dark"),title:Object(p.c)(l.t),width:Object(p.c)(l.t),zIndex:Object(p.c)(l.o)})),i.hb),F=function(e,t){if(t.noHeader)return e();var a=t.normalizeSlot(_.r,t.slotScope);if(!a){var n=function(e,t){var a=t.normalizeSlot(_.R,t.slotScope)||t.title;return a?e("strong",{attrs:{id:t.safeId("__title__")}},[a]):e("span")}(e,t),r=function(e,t){if(t.noHeaderClose)return e();var a=t.closeLabel,n=t.textVariant,r=t.hide;return e(Y.a,{props:{ariaLabel:a,textVariant:n},on:{click:r},ref:"close-button"},[t.normalizeSlot(_.s)||e(v.e)])}(e,t);a=t.right?[r,n]:[n,r]}return e("header",{staticClass:"".concat("b-sidebar","-header"),class:t.headerClass,key:"header"},a)},W=function(e,t){return e("div",{staticClass:"".concat("b-sidebar","-body"),class:t.bodyClass,key:"body"},[t.normalizeSlot(_.h,t.slotScope)])},A=function(e,t){var a=t.normalizeSlot(_.q,t.slotScope);return a?e("footer",{staticClass:"".concat("b-sidebar","-footer"),class:t.footerClass,key:"footer"},[a]):e()},z=function(e,t){var a=F(e,t);return t.lazy&&!t.isOpen?a:[a,W(e,t),A(e,t)]},N=function(e,t){if(!t.backdrop)return e();var a=t.backdropVariant;return e("div",{directives:[{name:"show",value:t.localShow}],staticClass:"b-sidebar-backdrop",class:D({},"bg-".concat(a),a),on:{click:t.onBackdropClick}})},I=s.a.extend({name:i.hb,mixins:[M.a,y.a,P,g.a,L.a],inheritAttrs:!1,props:R,data:function(){var e=!!this[C];return{localShow:e,isOpen:e}},computed:{transitionProps:function(){return this.noSlide?{css:!0}:{css:!0,enterClass:"",enterActiveClass:"slide",enterToClass:"show",leaveClass:"show",leaveActiveClass:"slide",leaveToClass:""}},slotScope:function(){return{hide:this.hide,right:this.right,visible:this.localShow}},hasTitle:function(){var e=this.$scopedSlots,t=this.$slots;return!(this.noHeader||this.hasNormalizedSlot(_.r)||!this.normalizeSlot(_.R,this.slotScope,e,t)&&!this.title)},titleId:function(){return this.hasTitle?this.safeId("__title__"):null},computedAttrs:function(){return k(k({},this.bvAttrs),{},{id:this.safeId(),tabindex:"-1",role:"dialog","aria-modal":this.backdrop?"true":"false","aria-hidden":this.localShow?null:"true","aria-label":this.ariaLabel||null,"aria-labelledby":this.ariaLabelledby||this.titleId||null})}},watch:(n={},D(n,C,(function(e,t){e!==t&&(this.localShow=e)})),D(n,"localShow",(function(e,t){e!==t&&(this.emitState(e),this.$emit(E,e))})),D(n,"$route",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.noCloseOnRouteChange||e.fullPath===t.fullPath||this.hide()})),n),created:function(){this.$_returnFocusEl=null},mounted:function(){var e=this;this.listenOnRoot(S,this.handleToggle),this.listenOnRoot(T,this.handleSync),this.$nextTick((function(){e.emitState(e.localShow)}))},activated:function(){this.emitSync()},beforeDestroy:function(){this.localShow=!1,this.$_returnFocusEl=null},methods:{hide:function(){this.localShow=!1},emitState:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(j,this.safeId(),e)},emitSync:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(x,this.safeId(),e)},handleToggle:function(e){e&&e===this.safeId()&&(this.localShow=!this.localShow)},handleSync:function(e){var t=this;e&&e===this.safeId()&&this.$nextTick((function(){t.emitSync(t.localShow)}))},onKeydown:function(e){var t=e.keyCode;!this.noCloseOnEsc&&t===u.d&&this.localShow&&this.hide()},onBackdropClick:function(){this.localShow&&!this.noCloseOnBackdrop&&this.hide()},onTopTrapFocus:function(){var e=Object(c.n)(this.$refs.content);this.enforceFocus(e.reverse()[0])},onBottomTrapFocus:function(){var e=Object(c.n)(this.$refs.content);this.enforceFocus(e[0])},onBeforeEnter:function(){this.$_returnFocusEl=Object(c.g)(o.f?[document.body]:[]),this.isOpen=!0},onAfterEnter:function(e){Object(c.f)(e,Object(c.g)())||this.enforceFocus(e),this.$emit(d.O)},onAfterLeave:function(){this.enforceFocus(this.$_returnFocusEl),this.$_returnFocusEl=null,this.isOpen=!1,this.$emit(d.s)},enforceFocus:function(e){this.noEnforceFocus||Object(c.d)(e)}},render:function(e){var t,a=this.bgVariant,n=this.width,r=this.textVariant,s=this.localShow,i=""===this.shadow||this.shadow,o=e(this.tag,{staticClass:"b-sidebar",class:[(t={shadow:!0===i},D(t,"shadow-".concat(i),i&&!0!==i),D(t,"".concat("b-sidebar","-right"),this.right),D(t,"bg-".concat(a),a),D(t,"text-".concat(r),r),t),this.sidebarClass],style:{width:n},attrs:this.computedAttrs,directives:[{name:"show",value:s}],ref:"content"},[z(e,this)]);o=e("transition",{props:this.transitionProps,on:{beforeEnter:this.onBeforeEnter,afterEnter:this.onAfterEnter,afterLeave:this.onAfterLeave}},[o]);var d=e(w.a,{props:{noFade:this.noSlide}},[N(e,this)]),u=e(),l=e();return this.backdrop&&s&&(u=e("div",{attrs:{tabindex:"0"},on:{focus:this.onTopTrapFocus}}),l=e("div",{attrs:{tabindex:"0"},on:{focus:this.onBottomTrapFocus}})),e("div",{staticClass:"b-sidebar-outer",style:{zIndex:this.zIndex},attrs:{tabindex:"-1"},on:{keydown:this.onKeydown}},[u,o,l,d])}})},wQk9:function(e,t,a){!function(e){"use strict";e.defineLocale("tzm",{months:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),monthsShort:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),weekdays:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysShort:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysMin:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ⴰⵙⴷⵅ ⴴ] LT",nextDay:"[ⴰⵙⴽⴰ ⴴ] LT",nextWeek:"dddd [ⴴ] LT",lastDay:"[ⴰⵚⴰⵏⵜ ⴴ] LT",lastWeek:"dddd [ⴴ] LT",sameElse:"L"},relativeTime:{future:"ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s",past:"ⵢⴰⵏ %s",s:"ⵉⵎⵉⴽ",ss:"%d ⵉⵎⵉⴽ",m:"ⵎⵉⵏⵓⴺ",mm:"%d ⵎⵉⵏⵓⴺ",h:"ⵙⴰⵄⴰ",hh:"%d ⵜⴰⵙⵙⴰⵄⵉⵏ",d:"ⴰⵙⵙ",dd:"%d oⵙⵙⴰⵏ",M:"ⴰⵢoⵓⵔ",MM:"%d ⵉⵢⵢⵉⵔⵏ",y:"ⴰⵙⴳⴰⵙ",yy:"%d ⵉⵙⴳⴰⵙⵏ"},week:{dow:6,doy:12}})}(a("wd/R"))},"wd/R":function(e,t,a){(function(e){e.exports=function(){"use strict";var t,n;function r(){return t.apply(null,arguments)}function s(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function i(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function o(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function d(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var t;for(t in e)if(o(e,t))return!1;return!0}function u(e){return void 0===e}function l(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function _(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function c(e,t){var a,n=[],r=e.length;for(a=0;a<r;++a)n.push(t(e[a],a));return n}function m(e,t){for(var a in t)o(t,a)&&(e[a]=t[a]);return o(t,"toString")&&(e.toString=t.toString),o(t,"valueOf")&&(e.valueOf=t.valueOf),e}function h(e,t,a,n){return kt(e,t,a,n,!0).utc()}function f(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function p(e){if(null==e._isValid){var t=f(e),a=n.call(t.parsedDateParts,(function(e){return null!=e})),r=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&a);if(e._strict&&(r=r&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return r;e._isValid=r}return e._isValid}function M(e){var t=h(NaN);return null!=e?m(f(t),e):f(t).userInvalidated=!0,t}n=Array.prototype.some?Array.prototype.some:function(e){var t,a=Object(this),n=a.length>>>0;for(t=0;t<n;t++)if(t in a&&e.call(this,a[t],t,a))return!0;return!1};var y=r.momentProperties=[],g=!1;function L(e,t){var a,n,r,s=y.length;if(u(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),u(t._i)||(e._i=t._i),u(t._f)||(e._f=t._f),u(t._l)||(e._l=t._l),u(t._strict)||(e._strict=t._strict),u(t._tzm)||(e._tzm=t._tzm),u(t._isUTC)||(e._isUTC=t._isUTC),u(t._offset)||(e._offset=t._offset),u(t._pf)||(e._pf=f(t)),u(t._locale)||(e._locale=t._locale),s>0)for(a=0;a<s;a++)u(r=t[n=y[a]])||(e[n]=r);return e}function v(e){L(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===g&&(g=!0,r.updateOffset(this),g=!1)}function Y(e){return e instanceof v||null!=e&&null!=e._isAMomentObject}function w(e){!1===r.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function b(e,t){var a=!0;return m((function(){if(null!=r.deprecationHandler&&r.deprecationHandler(null,e),a){var n,s,i,d=[],u=arguments.length;for(s=0;s<u;s++){if(n="","object"==typeof arguments[s]){for(i in n+="\n["+s+"] ",arguments[0])o(arguments[0],i)&&(n+=i+": "+arguments[0][i]+", ");n=n.slice(0,-2)}else n=arguments[s];d.push(n)}w(e+"\nArguments: "+Array.prototype.slice.call(d).join("")+"\n"+(new Error).stack),a=!1}return t.apply(this,arguments)}),t)}var k,D={};function T(e,t){null!=r.deprecationHandler&&r.deprecationHandler(e,t),D[e]||(w(t),D[e]=!0)}function S(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function j(e,t){var a,n=m({},e);for(a in t)o(t,a)&&(i(e[a])&&i(t[a])?(n[a]={},m(n[a],e[a]),m(n[a],t[a])):null!=t[a]?n[a]=t[a]:delete n[a]);for(a in e)o(e,a)&&!o(t,a)&&i(e[a])&&(n[a]=m({},n[a]));return n}function x(e){null!=e&&this.set(e)}function H(e,t,a){var n=""+Math.abs(e),r=t-n.length;return(e>=0?a?"+":"":"-")+Math.pow(10,Math.max(0,r)).toString().substr(1)+n}r.suppressDeprecationWarnings=!1,r.deprecationHandler=null,k=Object.keys?Object.keys:function(e){var t,a=[];for(t in e)o(e,t)&&a.push(t);return a};var P=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,O=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,C={},E={};function R(e,t,a,n){var r=n;"string"==typeof n&&(r=function(){return this[n]()}),e&&(E[e]=r),t&&(E[t[0]]=function(){return H(r.apply(this,arguments),t[1],t[2])}),a&&(E[a]=function(){return this.localeData().ordinal(r.apply(this,arguments),e)})}function F(e,t){return e.isValid()?(t=W(t,e.localeData()),C[t]=C[t]||function(e){var t,a,n,r=e.match(P);for(t=0,a=r.length;t<a;t++)E[r[t]]?r[t]=E[r[t]]:r[t]=(n=r[t]).match(/\[[\s\S]/)?n.replace(/^\[|\]$/g,""):n.replace(/\\/g,"");return function(t){var n,s="";for(n=0;n<a;n++)s+=S(r[n])?r[n].call(t,e):r[n];return s}}(t),C[t](e)):e.localeData().invalidDate()}function W(e,t){var a=5;function n(e){return t.longDateFormat(e)||e}for(O.lastIndex=0;a>=0&&O.test(e);)e=e.replace(O,n),O.lastIndex=0,a-=1;return e}var A={};function z(e,t){var a=e.toLowerCase();A[a]=A[a+"s"]=A[t]=e}function N(e){return"string"==typeof e?A[e]||A[e.toLowerCase()]:void 0}function I(e){var t,a,n={};for(a in e)o(e,a)&&(t=N(a))&&(n[t]=e[a]);return n}var U={};function J(e,t){U[e]=t}function B(e){return e%4==0&&e%100!=0||e%400==0}function G(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function $(e){var t=+e,a=0;return 0!==t&&isFinite(t)&&(a=G(t)),a}function V(e,t){return function(a){return null!=a?(Q(this,e,a),r.updateOffset(this,t),this):q(this,e)}}function q(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function Q(e,t,a){e.isValid()&&!isNaN(a)&&("FullYear"===t&&B(e.year())&&1===e.month()&&29===e.date()?(a=$(a),e._d["set"+(e._isUTC?"UTC":"")+t](a,e.month(),Ye(a,e.month()))):e._d["set"+(e._isUTC?"UTC":"")+t](a))}var K,X=/\d/,Z=/\d\d/,ee=/\d{3}/,te=/\d{4}/,ae=/[+-]?\d{6}/,ne=/\d\d?/,re=/\d\d\d\d?/,se=/\d\d\d\d\d\d?/,ie=/\d{1,3}/,oe=/\d{1,4}/,de=/[+-]?\d{1,6}/,ue=/\d+/,le=/[+-]?\d+/,_e=/Z|[+-]\d\d:?\d\d/gi,ce=/Z|[+-]\d\d(?::?\d\d)?/gi,me=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;function he(e,t,a){K[e]=S(t)?t:function(e,n){return e&&a?a:t}}function fe(e,t){return o(K,e)?K[e](t._strict,t._locale):new RegExp(pe(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,a,n,r){return t||a||n||r}))))}function pe(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}K={};var Me,ye={};function ge(e,t){var a,n,r=t;for("string"==typeof e&&(e=[e]),l(t)&&(r=function(e,a){a[t]=$(e)}),n=e.length,a=0;a<n;a++)ye[e[a]]=r}function Le(e,t){ge(e,(function(e,a,n,r){n._w=n._w||{},t(e,n._w,n,r)}))}function ve(e,t,a){null!=t&&o(ye,e)&&ye[e](t,a._a,a,e)}function Ye(e,t){if(isNaN(e)||isNaN(t))return NaN;var a,n=(t%(a=12)+a)%a;return e+=(t-n)/12,1===n?B(e)?29:28:31-n%7%2}Me=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},R("M",["MM",2],"Mo",(function(){return this.month()+1})),R("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),R("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),z("month","M"),J("month",8),he("M",ne),he("MM",ne,Z),he("MMM",(function(e,t){return t.monthsShortRegex(e)})),he("MMMM",(function(e,t){return t.monthsRegex(e)})),ge(["M","MM"],(function(e,t){t[1]=$(e)-1})),ge(["MMM","MMMM"],(function(e,t,a,n){var r=a._locale.monthsParse(e,n,a._strict);null!=r?t[1]=r:f(a).invalidMonth=e}));var we="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),be="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),ke=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,De=me,Te=me;function Se(e,t,a){var n,r,s,i=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],n=0;n<12;++n)s=h([2e3,n]),this._shortMonthsParse[n]=this.monthsShort(s,"").toLocaleLowerCase(),this._longMonthsParse[n]=this.months(s,"").toLocaleLowerCase();return a?"MMM"===t?-1!==(r=Me.call(this._shortMonthsParse,i))?r:null:-1!==(r=Me.call(this._longMonthsParse,i))?r:null:"MMM"===t?-1!==(r=Me.call(this._shortMonthsParse,i))||-1!==(r=Me.call(this._longMonthsParse,i))?r:null:-1!==(r=Me.call(this._longMonthsParse,i))||-1!==(r=Me.call(this._shortMonthsParse,i))?r:null}function je(e,t){var a;if(!e.isValid())return e;if("string"==typeof t)if(/^\d+$/.test(t))t=$(t);else if(!l(t=e.localeData().monthsParse(t)))return e;return a=Math.min(e.date(),Ye(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,a),e}function xe(e){return null!=e?(je(this,e),r.updateOffset(this,!0),this):q(this,"Month")}function He(){function e(e,t){return t.length-e.length}var t,a,n=[],r=[],s=[];for(t=0;t<12;t++)a=h([2e3,t]),n.push(this.monthsShort(a,"")),r.push(this.months(a,"")),s.push(this.months(a,"")),s.push(this.monthsShort(a,""));for(n.sort(e),r.sort(e),s.sort(e),t=0;t<12;t++)n[t]=pe(n[t]),r[t]=pe(r[t]);for(t=0;t<24;t++)s[t]=pe(s[t]);this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+n.join("|")+")","i")}function Pe(e){return B(e)?366:365}R("Y",0,0,(function(){var e=this.year();return e<=9999?H(e,4):"+"+e})),R(0,["YY",2],0,(function(){return this.year()%100})),R(0,["YYYY",4],0,"year"),R(0,["YYYYY",5],0,"year"),R(0,["YYYYYY",6,!0],0,"year"),z("year","y"),J("year",1),he("Y",le),he("YY",ne,Z),he("YYYY",oe,te),he("YYYYY",de,ae),he("YYYYYY",de,ae),ge(["YYYYY","YYYYYY"],0),ge("YYYY",(function(e,t){t[0]=2===e.length?r.parseTwoDigitYear(e):$(e)})),ge("YY",(function(e,t){t[0]=r.parseTwoDigitYear(e)})),ge("Y",(function(e,t){t[0]=parseInt(e,10)})),r.parseTwoDigitYear=function(e){return $(e)+($(e)>68?1900:2e3)};var Oe=V("FullYear",!0);function Ce(e,t,a,n,r,s,i){var o;return e<100&&e>=0?(o=new Date(e+400,t,a,n,r,s,i),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,a,n,r,s,i),o}function Ee(e){var t,a;return e<100&&e>=0?((a=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,a)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Re(e,t,a){var n=7+t-a;return-(7+Ee(e,0,n).getUTCDay()-t)%7+n-1}function Fe(e,t,a,n,r){var s,i,o=1+7*(t-1)+(7+a-n)%7+Re(e,n,r);return o<=0?i=Pe(s=e-1)+o:o>Pe(e)?(s=e+1,i=o-Pe(e)):(s=e,i=o),{year:s,dayOfYear:i}}function We(e,t,a){var n,r,s=Re(e.year(),t,a),i=Math.floor((e.dayOfYear()-s-1)/7)+1;return i<1?n=i+Ae(r=e.year()-1,t,a):i>Ae(e.year(),t,a)?(n=i-Ae(e.year(),t,a),r=e.year()+1):(r=e.year(),n=i),{week:n,year:r}}function Ae(e,t,a){var n=Re(e,t,a),r=Re(e+1,t,a);return(Pe(e)-n+r)/7}function ze(e,t){return e.slice(t,7).concat(e.slice(0,t))}R("w",["ww",2],"wo","week"),R("W",["WW",2],"Wo","isoWeek"),z("week","w"),z("isoWeek","W"),J("week",5),J("isoWeek",5),he("w",ne),he("ww",ne,Z),he("W",ne),he("WW",ne,Z),Le(["w","ww","W","WW"],(function(e,t,a,n){t[n.substr(0,1)]=$(e)})),R("d",0,"do","day"),R("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),R("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),R("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),R("e",0,0,"weekday"),R("E",0,0,"isoWeekday"),z("day","d"),z("weekday","e"),z("isoWeekday","E"),J("day",11),J("weekday",11),J("isoWeekday",11),he("d",ne),he("e",ne),he("E",ne),he("dd",(function(e,t){return t.weekdaysMinRegex(e)})),he("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),he("dddd",(function(e,t){return t.weekdaysRegex(e)})),Le(["dd","ddd","dddd"],(function(e,t,a,n){var r=a._locale.weekdaysParse(e,n,a._strict);null!=r?t.d=r:f(a).invalidWeekday=e})),Le(["d","e","E"],(function(e,t,a,n){t[n]=$(e)}));var Ne="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ie="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ue="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Je=me,Be=me,Ge=me;function $e(e,t,a){var n,r,s,i=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],n=0;n<7;++n)s=h([2e3,1]).day(n),this._minWeekdaysParse[n]=this.weekdaysMin(s,"").toLocaleLowerCase(),this._shortWeekdaysParse[n]=this.weekdaysShort(s,"").toLocaleLowerCase(),this._weekdaysParse[n]=this.weekdays(s,"").toLocaleLowerCase();return a?"dddd"===t?-1!==(r=Me.call(this._weekdaysParse,i))?r:null:"ddd"===t?-1!==(r=Me.call(this._shortWeekdaysParse,i))?r:null:-1!==(r=Me.call(this._minWeekdaysParse,i))?r:null:"dddd"===t?-1!==(r=Me.call(this._weekdaysParse,i))||-1!==(r=Me.call(this._shortWeekdaysParse,i))||-1!==(r=Me.call(this._minWeekdaysParse,i))?r:null:"ddd"===t?-1!==(r=Me.call(this._shortWeekdaysParse,i))||-1!==(r=Me.call(this._weekdaysParse,i))||-1!==(r=Me.call(this._minWeekdaysParse,i))?r:null:-1!==(r=Me.call(this._minWeekdaysParse,i))||-1!==(r=Me.call(this._weekdaysParse,i))||-1!==(r=Me.call(this._shortWeekdaysParse,i))?r:null}function Ve(){function e(e,t){return t.length-e.length}var t,a,n,r,s,i=[],o=[],d=[],u=[];for(t=0;t<7;t++)a=h([2e3,1]).day(t),n=pe(this.weekdaysMin(a,"")),r=pe(this.weekdaysShort(a,"")),s=pe(this.weekdays(a,"")),i.push(n),o.push(r),d.push(s),u.push(n),u.push(r),u.push(s);i.sort(e),o.sort(e),d.sort(e),u.sort(e),this._weekdaysRegex=new RegExp("^("+u.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+d.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function qe(){return this.hours()%12||12}function Qe(e,t){R(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function Ke(e,t){return t._meridiemParse}R("H",["HH",2],0,"hour"),R("h",["hh",2],0,qe),R("k",["kk",2],0,(function(){return this.hours()||24})),R("hmm",0,0,(function(){return""+qe.apply(this)+H(this.minutes(),2)})),R("hmmss",0,0,(function(){return""+qe.apply(this)+H(this.minutes(),2)+H(this.seconds(),2)})),R("Hmm",0,0,(function(){return""+this.hours()+H(this.minutes(),2)})),R("Hmmss",0,0,(function(){return""+this.hours()+H(this.minutes(),2)+H(this.seconds(),2)})),Qe("a",!0),Qe("A",!1),z("hour","h"),J("hour",13),he("a",Ke),he("A",Ke),he("H",ne),he("h",ne),he("k",ne),he("HH",ne,Z),he("hh",ne,Z),he("kk",ne,Z),he("hmm",re),he("hmmss",se),he("Hmm",re),he("Hmmss",se),ge(["H","HH"],3),ge(["k","kk"],(function(e,t,a){var n=$(e);t[3]=24===n?0:n})),ge(["a","A"],(function(e,t,a){a._isPm=a._locale.isPM(e),a._meridiem=e})),ge(["h","hh"],(function(e,t,a){t[3]=$(e),f(a).bigHour=!0})),ge("hmm",(function(e,t,a){var n=e.length-2;t[3]=$(e.substr(0,n)),t[4]=$(e.substr(n)),f(a).bigHour=!0})),ge("hmmss",(function(e,t,a){var n=e.length-4,r=e.length-2;t[3]=$(e.substr(0,n)),t[4]=$(e.substr(n,2)),t[5]=$(e.substr(r)),f(a).bigHour=!0})),ge("Hmm",(function(e,t,a){var n=e.length-2;t[3]=$(e.substr(0,n)),t[4]=$(e.substr(n))})),ge("Hmmss",(function(e,t,a){var n=e.length-4,r=e.length-2;t[3]=$(e.substr(0,n)),t[4]=$(e.substr(n,2)),t[5]=$(e.substr(r))}));var Xe,Ze=V("Hours",!0),et={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:we,monthsShort:be,week:{dow:0,doy:6},weekdays:Ne,weekdaysMin:Ue,weekdaysShort:Ie,meridiemParse:/[ap]\.?m?\.?/i},tt={},at={};function nt(e,t){var a,n=Math.min(e.length,t.length);for(a=0;a<n;a+=1)if(e[a]!==t[a])return a;return n}function rt(e){return e?e.toLowerCase().replace("_","-"):e}function st(t){var n=null;if(void 0===tt[t]&&void 0!==e&&e&&e.exports&&function(e){return null!=e.match("^[^/\\\\]*$")}(t))try{n=Xe._abbr,a("RnhZ")("./"+t),it(n)}catch(e){tt[t]=null}return tt[t]}function it(e,t){var a;return e&&((a=u(t)?dt(e):ot(e,t))?Xe=a:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),Xe._abbr}function ot(e,t){if(null!==t){var a,n=et;if(t.abbr=e,null!=tt[e])T("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),n=tt[e]._config;else if(null!=t.parentLocale)if(null!=tt[t.parentLocale])n=tt[t.parentLocale]._config;else{if(null==(a=st(t.parentLocale)))return at[t.parentLocale]||(at[t.parentLocale]=[]),at[t.parentLocale].push({name:e,config:t}),null;n=a._config}return tt[e]=new x(j(n,t)),at[e]&&at[e].forEach((function(e){ot(e.name,e.config)})),it(e),tt[e]}return delete tt[e],null}function dt(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return Xe;if(!s(e)){if(t=st(e))return t;e=[e]}return function(e){for(var t,a,n,r,s=0;s<e.length;){for(t=(r=rt(e[s]).split("-")).length,a=(a=rt(e[s+1]))?a.split("-"):null;t>0;){if(n=st(r.slice(0,t).join("-")))return n;if(a&&a.length>=t&&nt(r,a)>=t-1)break;t--}s++}return Xe}(e)}function ut(e){var t,a=e._a;return a&&-2===f(e).overflow&&(t=a[1]<0||a[1]>11?1:a[2]<1||a[2]>Ye(a[0],a[1])?2:a[3]<0||a[3]>24||24===a[3]&&(0!==a[4]||0!==a[5]||0!==a[6])?3:a[4]<0||a[4]>59?4:a[5]<0||a[5]>59?5:a[6]<0||a[6]>999?6:-1,f(e)._overflowDayOfYear&&(t<0||t>2)&&(t=2),f(e)._overflowWeeks&&-1===t&&(t=7),f(e)._overflowWeekday&&-1===t&&(t=8),f(e).overflow=t),e}var lt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,_t=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ct=/Z|[+-]\d\d(?::?\d\d)?/,mt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ht=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ft=/^\/?Date\((-?\d+)/i,pt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Mt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function yt(e){var t,a,n,r,s,i,o=e._i,d=lt.exec(o)||_t.exec(o),u=mt.length,l=ht.length;if(d){for(f(e).iso=!0,t=0,a=u;t<a;t++)if(mt[t][1].exec(d[1])){r=mt[t][0],n=!1!==mt[t][2];break}if(null==r)return void(e._isValid=!1);if(d[3]){for(t=0,a=l;t<a;t++)if(ht[t][1].exec(d[3])){s=(d[2]||" ")+ht[t][0];break}if(null==s)return void(e._isValid=!1)}if(!n&&null!=s)return void(e._isValid=!1);if(d[4]){if(!ct.exec(d[4]))return void(e._isValid=!1);i="Z"}e._f=r+(s||"")+(i||""),wt(e)}else e._isValid=!1}function gt(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function Lt(e){var t,a,n,r,s,i,o,d,u=pt.exec(e._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(u){if(a=u[4],n=u[3],r=u[2],s=u[5],i=u[6],o=u[7],d=[gt(a),be.indexOf(n),parseInt(r,10),parseInt(s,10),parseInt(i,10)],o&&d.push(parseInt(o,10)),t=d,!function(e,t,a){return!e||Ie.indexOf(e)===new Date(t[0],t[1],t[2]).getDay()||(f(a).weekdayMismatch=!0,a._isValid=!1,!1)}(u[1],t,e))return;e._a=t,e._tzm=function(e,t,a){if(e)return Mt[e];if(t)return 0;var n=parseInt(a,10),r=n%100;return(n-r)/100*60+r}(u[8],u[9],u[10]),e._d=Ee.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),f(e).rfc2822=!0}else e._isValid=!1}function vt(e,t,a){return null!=e?e:null!=t?t:a}function Yt(e){var t,a,n,s,i,o=[];if(!e._d){for(n=function(e){var t=new Date(r.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}(e),e._w&&null==e._a[2]&&null==e._a[1]&&function(e){var t,a,n,r,s,i,o,d,u;null!=(t=e._w).GG||null!=t.W||null!=t.E?(s=1,i=4,a=vt(t.GG,e._a[0],We(Dt(),1,4).year),n=vt(t.W,1),((r=vt(t.E,1))<1||r>7)&&(d=!0)):(s=e._locale._week.dow,i=e._locale._week.doy,u=We(Dt(),s,i),a=vt(t.gg,e._a[0],u.year),n=vt(t.w,u.week),null!=t.d?((r=t.d)<0||r>6)&&(d=!0):null!=t.e?(r=t.e+s,(t.e<0||t.e>6)&&(d=!0)):r=s),n<1||n>Ae(a,s,i)?f(e)._overflowWeeks=!0:null!=d?f(e)._overflowWeekday=!0:(o=Fe(a,n,r,s,i),e._a[0]=o.year,e._dayOfYear=o.dayOfYear)}(e),null!=e._dayOfYear&&(i=vt(e._a[0],n[0]),(e._dayOfYear>Pe(i)||0===e._dayOfYear)&&(f(e)._overflowDayOfYear=!0),a=Ee(i,0,e._dayOfYear),e._a[1]=a.getUTCMonth(),e._a[2]=a.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=o[t]=n[t];for(;t<7;t++)e._a[t]=o[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[3]&&0===e._a[4]&&0===e._a[5]&&0===e._a[6]&&(e._nextDay=!0,e._a[3]=0),e._d=(e._useUTC?Ee:Ce).apply(null,o),s=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[3]=24),e._w&&void 0!==e._w.d&&e._w.d!==s&&(f(e).weekdayMismatch=!0)}}function wt(e){if(e._f!==r.ISO_8601)if(e._f!==r.RFC_2822){e._a=[],f(e).empty=!0;var t,a,n,s,i,o,d,u=""+e._i,l=u.length,_=0;for(d=(n=W(e._f,e._locale).match(P)||[]).length,t=0;t<d;t++)s=n[t],(a=(u.match(fe(s,e))||[])[0])&&((i=u.substr(0,u.indexOf(a))).length>0&&f(e).unusedInput.push(i),u=u.slice(u.indexOf(a)+a.length),_+=a.length),E[s]?(a?f(e).empty=!1:f(e).unusedTokens.push(s),ve(s,a,e)):e._strict&&!a&&f(e).unusedTokens.push(s);f(e).charsLeftOver=l-_,u.length>0&&f(e).unusedInput.push(u),e._a[3]<=12&&!0===f(e).bigHour&&e._a[3]>0&&(f(e).bigHour=void 0),f(e).parsedDateParts=e._a.slice(0),f(e).meridiem=e._meridiem,e._a[3]=function(e,t,a){var n;return null==a?t:null!=e.meridiemHour?e.meridiemHour(t,a):null!=e.isPM?((n=e.isPM(a))&&t<12&&(t+=12),n||12!==t||(t=0),t):t}(e._locale,e._a[3],e._meridiem),null!==(o=f(e).era)&&(e._a[0]=e._locale.erasConvertYear(o,e._a[0])),Yt(e),ut(e)}else Lt(e);else yt(e)}function bt(e){var t=e._i,a=e._f;return e._locale=e._locale||dt(e._l),null===t||void 0===a&&""===t?M({nullInput:!0}):("string"==typeof t&&(e._i=t=e._locale.preparse(t)),Y(t)?new v(ut(t)):(_(t)?e._d=t:s(a)?function(e){var t,a,n,r,s,i,o=!1,d=e._f.length;if(0===d)return f(e).invalidFormat=!0,void(e._d=new Date(NaN));for(r=0;r<d;r++)s=0,i=!1,t=L({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[r],wt(t),p(t)&&(i=!0),s+=f(t).charsLeftOver,s+=10*f(t).unusedTokens.length,f(t).score=s,o?s<n&&(n=s,a=t):(null==n||s<n||i)&&(n=s,a=t,i&&(o=!0));m(e,a||t)}(e):a?wt(e):function(e){var t=e._i;u(t)?e._d=new Date(r.now()):_(t)?e._d=new Date(t.valueOf()):"string"==typeof t?function(e){var t=ft.exec(e._i);null===t?(yt(e),!1===e._isValid&&(delete e._isValid,Lt(e),!1===e._isValid&&(delete e._isValid,e._strict?e._isValid=!1:r.createFromInputFallback(e)))):e._d=new Date(+t[1])}(e):s(t)?(e._a=c(t.slice(0),(function(e){return parseInt(e,10)})),Yt(e)):i(t)?function(e){if(!e._d){var t=I(e._i),a=void 0===t.day?t.date:t.day;e._a=c([t.year,t.month,a,t.hour,t.minute,t.second,t.millisecond],(function(e){return e&&parseInt(e,10)})),Yt(e)}}(e):l(t)?e._d=new Date(t):r.createFromInputFallback(e)}(e),p(e)||(e._d=null),e))}function kt(e,t,a,n,r){var o,u={};return!0!==t&&!1!==t||(n=t,t=void 0),!0!==a&&!1!==a||(n=a,a=void 0),(i(e)&&d(e)||s(e)&&0===e.length)&&(e=void 0),u._isAMomentObject=!0,u._useUTC=u._isUTC=r,u._l=a,u._i=e,u._f=t,u._strict=n,(o=new v(ut(bt(u))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function Dt(e,t,a,n){return kt(e,t,a,n,!1)}r.createFromInputFallback=b("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),r.ISO_8601=function(){},r.RFC_2822=function(){};var Tt=b("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Dt.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:M()})),St=b("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Dt.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:M()}));function jt(e,t){var a,n;if(1===t.length&&s(t[0])&&(t=t[0]),!t.length)return Dt();for(a=t[0],n=1;n<t.length;++n)t[n].isValid()&&!t[n][e](a)||(a=t[n]);return a}var xt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ht(e){var t=I(e),a=t.year||0,n=t.quarter||0,r=t.month||0,s=t.week||t.isoWeek||0,i=t.day||0,d=t.hour||0,u=t.minute||0,l=t.second||0,_=t.millisecond||0;this._isValid=function(e){var t,a,n=!1,r=xt.length;for(t in e)if(o(e,t)&&(-1===Me.call(xt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(a=0;a<r;++a)if(e[xt[a]]){if(n)return!1;parseFloat(e[xt[a]])!==$(e[xt[a]])&&(n=!0)}return!0}(t),this._milliseconds=+_+1e3*l+6e4*u+1e3*d*60*60,this._days=+i+7*s,this._months=+r+3*n+12*a,this._data={},this._locale=dt(),this._bubble()}function Pt(e){return e instanceof Ht}function Ot(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Ct(e,t){R(e,0,0,(function(){var e=this.utcOffset(),a="+";return e<0&&(e=-e,a="-"),a+H(~~(e/60),2)+t+H(~~e%60,2)}))}Ct("Z",":"),Ct("ZZ",""),he("Z",ce),he("ZZ",ce),ge(["Z","ZZ"],(function(e,t,a){a._useUTC=!0,a._tzm=Rt(ce,e)}));var Et=/([\+\-]|\d\d)/gi;function Rt(e,t){var a,n,r=(t||"").match(e);return null===r?null:0===(n=60*(a=((r[r.length-1]||[])+"").match(Et)||["-",0,0])[1]+$(a[2]))?0:"+"===a[0]?n:-n}function Ft(e,t){var a,n;return t._isUTC?(a=t.clone(),n=(Y(e)||_(e)?e.valueOf():Dt(e).valueOf())-a.valueOf(),a._d.setTime(a._d.valueOf()+n),r.updateOffset(a,!1),a):Dt(e).local()}function Wt(e){return-Math.round(e._d.getTimezoneOffset())}function At(){return!!this.isValid()&&this._isUTC&&0===this._offset}r.updateOffset=function(){};var zt=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Nt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function It(e,t){var a,n,r,s,i,d,u=e,_=null;return Pt(e)?u={ms:e._milliseconds,d:e._days,M:e._months}:l(e)||!isNaN(+e)?(u={},t?u[t]=+e:u.milliseconds=+e):(_=zt.exec(e))?(a="-"===_[1]?-1:1,u={y:0,d:$(_[2])*a,h:$(_[3])*a,m:$(_[4])*a,s:$(_[5])*a,ms:$(Ot(1e3*_[6]))*a}):(_=Nt.exec(e))?(a="-"===_[1]?-1:1,u={y:Ut(_[2],a),M:Ut(_[3],a),w:Ut(_[4],a),d:Ut(_[5],a),h:Ut(_[6],a),m:Ut(_[7],a),s:Ut(_[8],a)}):null==u?u={}:"object"==typeof u&&("from"in u||"to"in u)&&(s=Dt(u.from),i=Dt(u.to),r=s.isValid()&&i.isValid()?(i=Ft(i,s),s.isBefore(i)?d=Jt(s,i):((d=Jt(i,s)).milliseconds=-d.milliseconds,d.months=-d.months),d):{milliseconds:0,months:0},(u={}).ms=r.milliseconds,u.M=r.months),n=new Ht(u),Pt(e)&&o(e,"_locale")&&(n._locale=e._locale),Pt(e)&&o(e,"_isValid")&&(n._isValid=e._isValid),n}function Ut(e,t){var a=e&&parseFloat(e.replace(",","."));return(isNaN(a)?0:a)*t}function Jt(e,t){var a={};return a.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(a.months,"M").isAfter(t)&&--a.months,a.milliseconds=+t-+e.clone().add(a.months,"M"),a}function Bt(e,t){return function(a,n){var r;return null===n||isNaN(+n)||(T(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),r=a,a=n,n=r),Gt(this,It(a,n),e),this}}function Gt(e,t,a,n){var s=t._milliseconds,i=Ot(t._days),o=Ot(t._months);e.isValid()&&(n=null==n||n,o&&je(e,q(e,"Month")+o*a),i&&Q(e,"Date",q(e,"Date")+i*a),s&&e._d.setTime(e._d.valueOf()+s*a),n&&r.updateOffset(e,i||o))}It.fn=Ht.prototype,It.invalid=function(){return It(NaN)};var $t=Bt(1,"add"),Vt=Bt(-1,"subtract");function qt(e){return"string"==typeof e||e instanceof String}function Qt(e){return Y(e)||_(e)||qt(e)||l(e)||function(e){var t=s(e),a=!1;return t&&(a=0===e.filter((function(t){return!l(t)&&qt(e)})).length),t&&a}(e)||function(e){var t,a,n=i(e)&&!d(e),r=!1,s=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],u=s.length;for(t=0;t<u;t+=1)a=s[t],r=r||o(e,a);return n&&r}(e)||null==e}function Kt(e){var t,a=i(e)&&!d(e),n=!1,r=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(t=0;t<r.length;t+=1)n=n||o(e,r[t]);return a&&n}function Xt(e,t){if(e.date()<t.date())return-Xt(t,e);var a=12*(t.year()-e.year())+(t.month()-e.month()),n=e.clone().add(a,"months");return-(a+(t-n<0?(t-n)/(n-e.clone().add(a-1,"months")):(t-n)/(e.clone().add(a+1,"months")-n)))||0}function Zt(e){var t;return void 0===e?this._locale._abbr:(null!=(t=dt(e))&&(this._locale=t),this)}r.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",r.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var ea=b("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));function ta(){return this._locale}function aa(e,t){return(e%t+t)%t}function na(e,t,a){return e<100&&e>=0?new Date(e+400,t,a)-126227808e5:new Date(e,t,a).valueOf()}function ra(e,t,a){return e<100&&e>=0?Date.UTC(e+400,t,a)-126227808e5:Date.UTC(e,t,a)}function sa(e,t){return t.erasAbbrRegex(e)}function ia(){var e,t,a=[],n=[],r=[],s=[],i=this.eras();for(e=0,t=i.length;e<t;++e)n.push(pe(i[e].name)),a.push(pe(i[e].abbr)),r.push(pe(i[e].narrow)),s.push(pe(i[e].name)),s.push(pe(i[e].abbr)),s.push(pe(i[e].narrow));this._erasRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+n.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+r.join("|")+")","i")}function oa(e,t){R(0,[e,e.length],0,t)}function da(e,t,a,n,r){var s;return null==e?We(this,n,r).year:(t>(s=Ae(e,n,r))&&(t=s),ua.call(this,e,t,a,n,r))}function ua(e,t,a,n,r){var s=Fe(e,t,a,n,r),i=Ee(s.year,0,s.dayOfYear);return this.year(i.getUTCFullYear()),this.month(i.getUTCMonth()),this.date(i.getUTCDate()),this}R("N",0,0,"eraAbbr"),R("NN",0,0,"eraAbbr"),R("NNN",0,0,"eraAbbr"),R("NNNN",0,0,"eraName"),R("NNNNN",0,0,"eraNarrow"),R("y",["y",1],"yo","eraYear"),R("y",["yy",2],0,"eraYear"),R("y",["yyy",3],0,"eraYear"),R("y",["yyyy",4],0,"eraYear"),he("N",sa),he("NN",sa),he("NNN",sa),he("NNNN",(function(e,t){return t.erasNameRegex(e)})),he("NNNNN",(function(e,t){return t.erasNarrowRegex(e)})),ge(["N","NN","NNN","NNNN","NNNNN"],(function(e,t,a,n){var r=a._locale.erasParse(e,n,a._strict);r?f(a).era=r:f(a).invalidEra=e})),he("y",ue),he("yy",ue),he("yyy",ue),he("yyyy",ue),he("yo",(function(e,t){return t._eraYearOrdinalRegex||ue})),ge(["y","yy","yyy","yyyy"],0),ge(["yo"],(function(e,t,a,n){var r;a._locale._eraYearOrdinalRegex&&(r=e.match(a._locale._eraYearOrdinalRegex)),a._locale.eraYearOrdinalParse?t[0]=a._locale.eraYearOrdinalParse(e,r):t[0]=parseInt(e,10)})),R(0,["gg",2],0,(function(){return this.weekYear()%100})),R(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),oa("gggg","weekYear"),oa("ggggg","weekYear"),oa("GGGG","isoWeekYear"),oa("GGGGG","isoWeekYear"),z("weekYear","gg"),z("isoWeekYear","GG"),J("weekYear",1),J("isoWeekYear",1),he("G",le),he("g",le),he("GG",ne,Z),he("gg",ne,Z),he("GGGG",oe,te),he("gggg",oe,te),he("GGGGG",de,ae),he("ggggg",de,ae),Le(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,a,n){t[n.substr(0,2)]=$(e)})),Le(["gg","GG"],(function(e,t,a,n){t[n]=r.parseTwoDigitYear(e)})),R("Q",0,"Qo","quarter"),z("quarter","Q"),J("quarter",7),he("Q",X),ge("Q",(function(e,t){t[1]=3*($(e)-1)})),R("D",["DD",2],"Do","date"),z("date","D"),J("date",9),he("D",ne),he("DD",ne,Z),he("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),ge(["D","DD"],2),ge("Do",(function(e,t){t[2]=$(e.match(ne)[0])}));var la=V("Date",!0);R("DDD",["DDDD",3],"DDDo","dayOfYear"),z("dayOfYear","DDD"),J("dayOfYear",4),he("DDD",ie),he("DDDD",ee),ge(["DDD","DDDD"],(function(e,t,a){a._dayOfYear=$(e)})),R("m",["mm",2],0,"minute"),z("minute","m"),J("minute",14),he("m",ne),he("mm",ne,Z),ge(["m","mm"],4);var _a=V("Minutes",!1);R("s",["ss",2],0,"second"),z("second","s"),J("second",15),he("s",ne),he("ss",ne,Z),ge(["s","ss"],5);var ca,ma,ha=V("Seconds",!1);for(R("S",0,0,(function(){return~~(this.millisecond()/100)})),R(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),R(0,["SSS",3],0,"millisecond"),R(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),R(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),R(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),R(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),R(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),R(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),z("millisecond","ms"),J("millisecond",16),he("S",ie,X),he("SS",ie,Z),he("SSS",ie,ee),ca="SSSS";ca.length<=9;ca+="S")he(ca,ue);function fa(e,t){t[6]=$(1e3*("0."+e))}for(ca="S";ca.length<=9;ca+="S")ge(ca,fa);ma=V("Milliseconds",!1),R("z",0,0,"zoneAbbr"),R("zz",0,0,"zoneName");var pa=v.prototype;function Ma(e){return e}pa.add=$t,pa.calendar=function(e,t){1===arguments.length&&(arguments[0]?Qt(arguments[0])?(e=arguments[0],t=void 0):Kt(arguments[0])&&(t=arguments[0],e=void 0):(e=void 0,t=void 0));var a=e||Dt(),n=Ft(a,this).startOf("day"),s=r.calendarFormat(this,n)||"sameElse",i=t&&(S(t[s])?t[s].call(this,a):t[s]);return this.format(i||this.localeData().calendar(s,this,Dt(a)))},pa.clone=function(){return new v(this)},pa.diff=function(e,t,a){var n,r,s;if(!this.isValid())return NaN;if(!(n=Ft(e,this)).isValid())return NaN;switch(r=6e4*(n.utcOffset()-this.utcOffset()),t=N(t)){case"year":s=Xt(this,n)/12;break;case"month":s=Xt(this,n);break;case"quarter":s=Xt(this,n)/3;break;case"second":s=(this-n)/1e3;break;case"minute":s=(this-n)/6e4;break;case"hour":s=(this-n)/36e5;break;case"day":s=(this-n-r)/864e5;break;case"week":s=(this-n-r)/6048e5;break;default:s=this-n}return a?s:G(s)},pa.endOf=function(e){var t,a;if(void 0===(e=N(e))||"millisecond"===e||!this.isValid())return this;switch(a=this._isUTC?ra:na,e){case"year":t=a(this.year()+1,0,1)-1;break;case"quarter":t=a(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=a(this.year(),this.month()+1,1)-1;break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=a(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-aa(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-aa(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-aa(t,1e3)-1}return this._d.setTime(t),r.updateOffset(this,!0),this},pa.format=function(e){e||(e=this.isUtc()?r.defaultFormatUtc:r.defaultFormat);var t=F(this,e);return this.localeData().postformat(t)},pa.from=function(e,t){return this.isValid()&&(Y(e)&&e.isValid()||Dt(e).isValid())?It({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},pa.fromNow=function(e){return this.from(Dt(),e)},pa.to=function(e,t){return this.isValid()&&(Y(e)&&e.isValid()||Dt(e).isValid())?It({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},pa.toNow=function(e){return this.to(Dt(),e)},pa.get=function(e){return S(this[e=N(e)])?this[e]():this},pa.invalidAt=function(){return f(this).overflow},pa.isAfter=function(e,t){var a=Y(e)?e:Dt(e);return!(!this.isValid()||!a.isValid())&&("millisecond"===(t=N(t)||"millisecond")?this.valueOf()>a.valueOf():a.valueOf()<this.clone().startOf(t).valueOf())},pa.isBefore=function(e,t){var a=Y(e)?e:Dt(e);return!(!this.isValid()||!a.isValid())&&("millisecond"===(t=N(t)||"millisecond")?this.valueOf()<a.valueOf():this.clone().endOf(t).valueOf()<a.valueOf())},pa.isBetween=function(e,t,a,n){var r=Y(e)?e:Dt(e),s=Y(t)?t:Dt(t);return!!(this.isValid()&&r.isValid()&&s.isValid())&&(("("===(n=n||"()")[0]?this.isAfter(r,a):!this.isBefore(r,a))&&(")"===n[1]?this.isBefore(s,a):!this.isAfter(s,a)))},pa.isSame=function(e,t){var a,n=Y(e)?e:Dt(e);return!(!this.isValid()||!n.isValid())&&("millisecond"===(t=N(t)||"millisecond")?this.valueOf()===n.valueOf():(a=n.valueOf(),this.clone().startOf(t).valueOf()<=a&&a<=this.clone().endOf(t).valueOf()))},pa.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},pa.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},pa.isValid=function(){return p(this)},pa.lang=ea,pa.locale=Zt,pa.localeData=ta,pa.max=St,pa.min=Tt,pa.parsingFlags=function(){return m({},f(this))},pa.set=function(e,t){if("object"==typeof e){var a,n=function(e){var t,a=[];for(t in e)o(e,t)&&a.push({unit:t,priority:U[t]});return a.sort((function(e,t){return e.priority-t.priority})),a}(e=I(e)),r=n.length;for(a=0;a<r;a++)this[n[a].unit](e[n[a].unit])}else if(S(this[e=N(e)]))return this[e](t);return this},pa.startOf=function(e){var t,a;if(void 0===(e=N(e))||"millisecond"===e||!this.isValid())return this;switch(a=this._isUTC?ra:na,e){case"year":t=a(this.year(),0,1);break;case"quarter":t=a(this.year(),this.month()-this.month()%3,1);break;case"month":t=a(this.year(),this.month(),1);break;case"week":t=a(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=a(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=aa(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=aa(t,6e4);break;case"second":t=this._d.valueOf(),t-=aa(t,1e3)}return this._d.setTime(t),r.updateOffset(this,!0),this},pa.subtract=Vt,pa.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},pa.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},pa.toDate=function(){return new Date(this.valueOf())},pa.toISOString=function(e){if(!this.isValid())return null;var t=!0!==e,a=t?this.clone().utc():this;return a.year()<0||a.year()>9999?F(a,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):S(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",F(a,"Z")):F(a,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},pa.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e,t,a,n="moment",r="";return this.isLocal()||(n=0===this.utcOffset()?"moment.utc":"moment.parseZone",r="Z"),e="["+n+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",a=r+'[")]',this.format(e+t+"-MM-DD[T]HH:mm:ss.SSS"+a)},"undefined"!=typeof Symbol&&null!=Symbol.for&&(pa[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),pa.toJSON=function(){return this.isValid()?this.toISOString():null},pa.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},pa.unix=function(){return Math.floor(this.valueOf()/1e3)},pa.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},pa.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},pa.eraName=function(){var e,t,a,n=this.localeData().eras();for(e=0,t=n.length;e<t;++e){if(a=this.clone().startOf("day").valueOf(),n[e].since<=a&&a<=n[e].until)return n[e].name;if(n[e].until<=a&&a<=n[e].since)return n[e].name}return""},pa.eraNarrow=function(){var e,t,a,n=this.localeData().eras();for(e=0,t=n.length;e<t;++e){if(a=this.clone().startOf("day").valueOf(),n[e].since<=a&&a<=n[e].until)return n[e].narrow;if(n[e].until<=a&&a<=n[e].since)return n[e].narrow}return""},pa.eraAbbr=function(){var e,t,a,n=this.localeData().eras();for(e=0,t=n.length;e<t;++e){if(a=this.clone().startOf("day").valueOf(),n[e].since<=a&&a<=n[e].until)return n[e].abbr;if(n[e].until<=a&&a<=n[e].since)return n[e].abbr}return""},pa.eraYear=function(){var e,t,a,n,s=this.localeData().eras();for(e=0,t=s.length;e<t;++e)if(a=s[e].since<=s[e].until?1:-1,n=this.clone().startOf("day").valueOf(),s[e].since<=n&&n<=s[e].until||s[e].until<=n&&n<=s[e].since)return(this.year()-r(s[e].since).year())*a+s[e].offset;return this.year()},pa.year=Oe,pa.isLeapYear=function(){return B(this.year())},pa.weekYear=function(e){return da.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},pa.isoWeekYear=function(e){return da.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},pa.quarter=pa.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},pa.month=xe,pa.daysInMonth=function(){return Ye(this.year(),this.month())},pa.week=pa.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},pa.isoWeek=pa.isoWeeks=function(e){var t=We(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},pa.weeksInYear=function(){var e=this.localeData()._week;return Ae(this.year(),e.dow,e.doy)},pa.weeksInWeekYear=function(){var e=this.localeData()._week;return Ae(this.weekYear(),e.dow,e.doy)},pa.isoWeeksInYear=function(){return Ae(this.year(),1,4)},pa.isoWeeksInISOWeekYear=function(){return Ae(this.isoWeekYear(),1,4)},pa.date=la,pa.day=pa.days=function(e){if(!this.isValid())return null!=e?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=function(e,t){return"string"!=typeof e?e:isNaN(e)?"number"==typeof(e=t.weekdaysParse(e))?e:null:parseInt(e,10)}(e,this.localeData()),this.add(e-t,"d")):t},pa.weekday=function(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},pa.isoWeekday=function(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=function(e,t){return"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7},pa.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},pa.hour=pa.hours=Ze,pa.minute=pa.minutes=_a,pa.second=pa.seconds=ha,pa.millisecond=pa.milliseconds=ma,pa.utcOffset=function(e,t,a){var n,s=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(null===(e=Rt(ce,e)))return this}else Math.abs(e)<16&&!a&&(e*=60);return!this._isUTC&&t&&(n=Wt(this)),this._offset=e,this._isUTC=!0,null!=n&&this.add(n,"m"),s!==e&&(!t||this._changeInProgress?Gt(this,It(e-s,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,r.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?s:Wt(this)},pa.utc=function(e){return this.utcOffset(0,e)},pa.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Wt(this),"m")),this},pa.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var e=Rt(_e,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this},pa.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Dt(e).utcOffset():0,(this.utcOffset()-e)%60==0)},pa.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},pa.isLocal=function(){return!!this.isValid()&&!this._isUTC},pa.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},pa.isUtc=At,pa.isUTC=At,pa.zoneAbbr=function(){return this._isUTC?"UTC":""},pa.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},pa.dates=b("dates accessor is deprecated. Use date instead.",la),pa.months=b("months accessor is deprecated. Use month instead",xe),pa.years=b("years accessor is deprecated. Use year instead",Oe),pa.zone=b("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(e,t){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()})),pa.isDSTShifted=b("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){if(!u(this._isDSTShifted))return this._isDSTShifted;var e,t={};return L(t,this),(t=bt(t))._a?(e=t._isUTC?h(t._a):Dt(t._a),this._isDSTShifted=this.isValid()&&function(e,t,a){var n,r=Math.min(e.length,t.length),s=Math.abs(e.length-t.length),i=0;for(n=0;n<r;n++)(a&&e[n]!==t[n]||!a&&$(e[n])!==$(t[n]))&&i++;return i+s}(t._a,e.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}));var ya=x.prototype;function ga(e,t,a,n){var r=dt(),s=h().set(n,t);return r[a](s,e)}function La(e,t,a){if(l(e)&&(t=e,e=void 0),e=e||"",null!=t)return ga(e,t,a,"month");var n,r=[];for(n=0;n<12;n++)r[n]=ga(e,n,a,"month");return r}function va(e,t,a,n){"boolean"==typeof e?(l(t)&&(a=t,t=void 0),t=t||""):(a=t=e,e=!1,l(t)&&(a=t,t=void 0),t=t||"");var r,s=dt(),i=e?s._week.dow:0,o=[];if(null!=a)return ga(t,(a+i)%7,n,"day");for(r=0;r<7;r++)o[r]=ga(t,(r+i)%7,n,"day");return o}ya.calendar=function(e,t,a){var n=this._calendar[e]||this._calendar.sameElse;return S(n)?n.call(t,a):n},ya.longDateFormat=function(e){var t=this._longDateFormat[e],a=this._longDateFormat[e.toUpperCase()];return t||!a?t:(this._longDateFormat[e]=a.match(P).map((function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e})).join(""),this._longDateFormat[e])},ya.invalidDate=function(){return this._invalidDate},ya.ordinal=function(e){return this._ordinal.replace("%d",e)},ya.preparse=Ma,ya.postformat=Ma,ya.relativeTime=function(e,t,a,n){var r=this._relativeTime[a];return S(r)?r(e,t,a,n):r.replace(/%d/i,e)},ya.pastFuture=function(e,t){var a=this._relativeTime[e>0?"future":"past"];return S(a)?a(t):a.replace(/%s/i,t)},ya.set=function(e){var t,a;for(a in e)o(e,a)&&(S(t=e[a])?this[a]=t:this["_"+a]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},ya.eras=function(e,t){var a,n,s,i=this._eras||dt("en")._eras;for(a=0,n=i.length;a<n;++a){switch(typeof i[a].since){case"string":s=r(i[a].since).startOf("day"),i[a].since=s.valueOf()}switch(typeof i[a].until){case"undefined":i[a].until=1/0;break;case"string":s=r(i[a].until).startOf("day").valueOf(),i[a].until=s.valueOf()}}return i},ya.erasParse=function(e,t,a){var n,r,s,i,o,d=this.eras();for(e=e.toUpperCase(),n=0,r=d.length;n<r;++n)if(s=d[n].name.toUpperCase(),i=d[n].abbr.toUpperCase(),o=d[n].narrow.toUpperCase(),a)switch(t){case"N":case"NN":case"NNN":if(i===e)return d[n];break;case"NNNN":if(s===e)return d[n];break;case"NNNNN":if(o===e)return d[n]}else if([s,i,o].indexOf(e)>=0)return d[n]},ya.erasConvertYear=function(e,t){var a=e.since<=e.until?1:-1;return void 0===t?r(e.since).year():r(e.since).year()+(t-e.offset)*a},ya.erasAbbrRegex=function(e){return o(this,"_erasAbbrRegex")||ia.call(this),e?this._erasAbbrRegex:this._erasRegex},ya.erasNameRegex=function(e){return o(this,"_erasNameRegex")||ia.call(this),e?this._erasNameRegex:this._erasRegex},ya.erasNarrowRegex=function(e){return o(this,"_erasNarrowRegex")||ia.call(this),e?this._erasNarrowRegex:this._erasRegex},ya.months=function(e,t){return e?s(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||ke).test(t)?"format":"standalone"][e.month()]:s(this._months)?this._months:this._months.standalone},ya.monthsShort=function(e,t){return e?s(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[ke.test(t)?"format":"standalone"][e.month()]:s(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},ya.monthsParse=function(e,t,a){var n,r,s;if(this._monthsParseExact)return Se.call(this,e,t,a);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),n=0;n<12;n++){if(r=h([2e3,n]),a&&!this._longMonthsParse[n]&&(this._longMonthsParse[n]=new RegExp("^"+this.months(r,"").replace(".","")+"$","i"),this._shortMonthsParse[n]=new RegExp("^"+this.monthsShort(r,"").replace(".","")+"$","i")),a||this._monthsParse[n]||(s="^"+this.months(r,"")+"|^"+this.monthsShort(r,""),this._monthsParse[n]=new RegExp(s.replace(".",""),"i")),a&&"MMMM"===t&&this._longMonthsParse[n].test(e))return n;if(a&&"MMM"===t&&this._shortMonthsParse[n].test(e))return n;if(!a&&this._monthsParse[n].test(e))return n}},ya.monthsRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||He.call(this),e?this._monthsStrictRegex:this._monthsRegex):(o(this,"_monthsRegex")||(this._monthsRegex=Te),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},ya.monthsShortRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||He.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(o(this,"_monthsShortRegex")||(this._monthsShortRegex=De),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},ya.week=function(e){return We(e,this._week.dow,this._week.doy).week},ya.firstDayOfYear=function(){return this._week.doy},ya.firstDayOfWeek=function(){return this._week.dow},ya.weekdays=function(e,t){var a=s(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"];return!0===e?ze(a,this._week.dow):e?a[e.day()]:a},ya.weekdaysMin=function(e){return!0===e?ze(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},ya.weekdaysShort=function(e){return!0===e?ze(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},ya.weekdaysParse=function(e,t,a){var n,r,s;if(this._weekdaysParseExact)return $e.call(this,e,t,a);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),n=0;n<7;n++){if(r=h([2e3,1]).day(n),a&&!this._fullWeekdaysParse[n]&&(this._fullWeekdaysParse[n]=new RegExp("^"+this.weekdays(r,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[n]=new RegExp("^"+this.weekdaysShort(r,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[n]=new RegExp("^"+this.weekdaysMin(r,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[n]||(s="^"+this.weekdays(r,"")+"|^"+this.weekdaysShort(r,"")+"|^"+this.weekdaysMin(r,""),this._weekdaysParse[n]=new RegExp(s.replace(".",""),"i")),a&&"dddd"===t&&this._fullWeekdaysParse[n].test(e))return n;if(a&&"ddd"===t&&this._shortWeekdaysParse[n].test(e))return n;if(a&&"dd"===t&&this._minWeekdaysParse[n].test(e))return n;if(!a&&this._weekdaysParse[n].test(e))return n}},ya.weekdaysRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Ve.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(o(this,"_weekdaysRegex")||(this._weekdaysRegex=Je),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},ya.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Ve.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(o(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Be),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},ya.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Ve.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(o(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Ge),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},ya.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},ya.meridiem=function(e,t,a){return e>11?a?"pm":"PM":a?"am":"AM"},it("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===$(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}}),r.lang=b("moment.lang is deprecated. Use moment.locale instead.",it),r.langData=b("moment.langData is deprecated. Use moment.localeData instead.",dt);var Ya=Math.abs;function wa(e,t,a,n){var r=It(t,a);return e._milliseconds+=n*r._milliseconds,e._days+=n*r._days,e._months+=n*r._months,e._bubble()}function ba(e){return e<0?Math.floor(e):Math.ceil(e)}function ka(e){return 4800*e/146097}function Da(e){return 146097*e/4800}function Ta(e){return function(){return this.as(e)}}var Sa=Ta("ms"),ja=Ta("s"),xa=Ta("m"),Ha=Ta("h"),Pa=Ta("d"),Oa=Ta("w"),Ca=Ta("M"),Ea=Ta("Q"),Ra=Ta("y");function Fa(e){return function(){return this.isValid()?this._data[e]:NaN}}var Wa=Fa("milliseconds"),Aa=Fa("seconds"),za=Fa("minutes"),Na=Fa("hours"),Ia=Fa("days"),Ua=Fa("months"),Ja=Fa("years"),Ba=Math.round,Ga={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function $a(e,t,a,n,r){return r.relativeTime(t||1,!!a,e,n)}var Va=Math.abs;function qa(e){return(e>0)-(e<0)||+e}function Qa(){if(!this.isValid())return this.localeData().invalidDate();var e,t,a,n,r,s,i,o,d=Va(this._milliseconds)/1e3,u=Va(this._days),l=Va(this._months),_=this.asSeconds();return _?(e=G(d/60),t=G(e/60),d%=60,e%=60,a=G(l/12),l%=12,n=d?d.toFixed(3).replace(/\.?0+$/,""):"",r=_<0?"-":"",s=qa(this._months)!==qa(_)?"-":"",i=qa(this._days)!==qa(_)?"-":"",o=qa(this._milliseconds)!==qa(_)?"-":"",r+"P"+(a?s+a+"Y":"")+(l?s+l+"M":"")+(u?i+u+"D":"")+(t||e||d?"T":"")+(t?o+t+"H":"")+(e?o+e+"M":"")+(d?o+n+"S":"")):"P0D"}var Ka=Ht.prototype;return Ka.isValid=function(){return this._isValid},Ka.abs=function(){var e=this._data;return this._milliseconds=Ya(this._milliseconds),this._days=Ya(this._days),this._months=Ya(this._months),e.milliseconds=Ya(e.milliseconds),e.seconds=Ya(e.seconds),e.minutes=Ya(e.minutes),e.hours=Ya(e.hours),e.months=Ya(e.months),e.years=Ya(e.years),this},Ka.add=function(e,t){return wa(this,e,t,1)},Ka.subtract=function(e,t){return wa(this,e,t,-1)},Ka.as=function(e){if(!this.isValid())return NaN;var t,a,n=this._milliseconds;if("month"===(e=N(e))||"quarter"===e||"year"===e)switch(t=this._days+n/864e5,a=this._months+ka(t),e){case"month":return a;case"quarter":return a/3;case"year":return a/12}else switch(t=this._days+Math.round(Da(this._months)),e){case"week":return t/7+n/6048e5;case"day":return t+n/864e5;case"hour":return 24*t+n/36e5;case"minute":return 1440*t+n/6e4;case"second":return 86400*t+n/1e3;case"millisecond":return Math.floor(864e5*t)+n;default:throw new Error("Unknown unit "+e)}},Ka.asMilliseconds=Sa,Ka.asSeconds=ja,Ka.asMinutes=xa,Ka.asHours=Ha,Ka.asDays=Pa,Ka.asWeeks=Oa,Ka.asMonths=Ca,Ka.asQuarters=Ea,Ka.asYears=Ra,Ka.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*$(this._months/12):NaN},Ka._bubble=function(){var e,t,a,n,r,s=this._milliseconds,i=this._days,o=this._months,d=this._data;return s>=0&&i>=0&&o>=0||s<=0&&i<=0&&o<=0||(s+=864e5*ba(Da(o)+i),i=0,o=0),d.milliseconds=s%1e3,e=G(s/1e3),d.seconds=e%60,t=G(e/60),d.minutes=t%60,a=G(t/60),d.hours=a%24,i+=G(a/24),r=G(ka(i)),o+=r,i-=ba(Da(r)),n=G(o/12),o%=12,d.days=i,d.months=o,d.years=n,this},Ka.clone=function(){return It(this)},Ka.get=function(e){return e=N(e),this.isValid()?this[e+"s"]():NaN},Ka.milliseconds=Wa,Ka.seconds=Aa,Ka.minutes=za,Ka.hours=Na,Ka.days=Ia,Ka.weeks=function(){return G(this.days()/7)},Ka.months=Ua,Ka.years=Ja,Ka.humanize=function(e,t){if(!this.isValid())return this.localeData().invalidDate();var a,n,r=!1,s=Ga;return"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(r=e),"object"==typeof t&&(s=Object.assign({},Ga,t),null!=t.s&&null==t.ss&&(s.ss=t.s-1)),a=this.localeData(),n=function(e,t,a,n){var r=It(e).abs(),s=Ba(r.as("s")),i=Ba(r.as("m")),o=Ba(r.as("h")),d=Ba(r.as("d")),u=Ba(r.as("M")),l=Ba(r.as("w")),_=Ba(r.as("y")),c=s<=a.ss&&["s",s]||s<a.s&&["ss",s]||i<=1&&["m"]||i<a.m&&["mm",i]||o<=1&&["h"]||o<a.h&&["hh",o]||d<=1&&["d"]||d<a.d&&["dd",d];return null!=a.w&&(c=c||l<=1&&["w"]||l<a.w&&["ww",l]),(c=c||u<=1&&["M"]||u<a.M&&["MM",u]||_<=1&&["y"]||["yy",_])[2]=t,c[3]=+e>0,c[4]=n,$a.apply(null,c)}(this,!r,s,a),r&&(n=a.pastFuture(+this,n)),a.postformat(n)},Ka.toISOString=Qa,Ka.toString=Qa,Ka.toJSON=Qa,Ka.locale=Zt,Ka.localeData=ta,Ka.toIsoString=b("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Qa),Ka.lang=ea,R("X",0,0,"unix"),R("x",0,0,"valueOf"),he("x",le),he("X",/[+-]?\d+(\.\d{1,3})?/),ge("X",(function(e,t,a){a._d=new Date(1e3*parseFloat(e))})),ge("x",(function(e,t,a){a._d=new Date($(e))})),r.version="2.29.3",t=Dt,r.fn=pa,r.min=function(){var e=[].slice.call(arguments,0);return jt("isBefore",e)},r.max=function(){var e=[].slice.call(arguments,0);return jt("isAfter",e)},r.now=function(){return Date.now?Date.now():+new Date},r.utc=h,r.unix=function(e){return Dt(1e3*e)},r.months=function(e,t){return La(e,t,"months")},r.isDate=_,r.locale=it,r.invalid=M,r.duration=It,r.isMoment=Y,r.weekdays=function(e,t,a){return va(e,t,a,"weekdays")},r.parseZone=function(){return Dt.apply(null,arguments).parseZone()},r.localeData=dt,r.isDuration=Pt,r.monthsShort=function(e,t){return La(e,t,"monthsShort")},r.weekdaysMin=function(e,t,a){return va(e,t,a,"weekdaysMin")},r.defineLocale=ot,r.updateLocale=function(e,t){if(null!=t){var a,n,r=et;null!=tt[e]&&null!=tt[e].parentLocale?tt[e].set(j(tt[e]._config,t)):(null!=(n=st(e))&&(r=n._config),t=j(r,t),null==n&&(t.abbr=e),(a=new x(t)).parentLocale=tt[e],tt[e]=a),it(e)}else null!=tt[e]&&(null!=tt[e].parentLocale?(tt[e]=tt[e].parentLocale,e===it()&&it(e)):null!=tt[e]&&delete tt[e]);return tt[e]},r.locales=function(){return k(tt)},r.weekdaysShort=function(e,t,a){return va(e,t,a,"weekdaysShort")},r.normalizeUnits=N,r.relativeTimeRounding=function(e){return void 0===e?Ba:"function"==typeof e&&(Ba=e,!0)},r.relativeTimeThreshold=function(e,t){return void 0!==Ga[e]&&(void 0===t?Ga[e]:(Ga[e]=t,"s"===e&&(Ga.ss=t-1),!0))},r.calendarFormat=function(e,t){var a=e.diff(t,"days",!0);return a<-6?"sameElse":a<-1?"lastWeek":a<0?"lastDay":a<1?"sameDay":a<2?"nextDay":a<7?"nextWeek":"sameElse"},r.prototype=pa,r.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},r}()}).call(this,a("YuTi")(e))},x6pH:function(e,t,a){!function(e){"use strict";e.defineLocale("he",{months:"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"),monthsShort:"ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳".split("_"),weekdays:"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"),weekdaysShort:"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"),weekdaysMin:"א_ב_ג_ד_ה_ו_ש".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [ב]MMMM YYYY",LLL:"D [ב]MMMM YYYY HH:mm",LLLL:"dddd, D [ב]MMMM YYYY HH:mm",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[היום ב־]LT",nextDay:"[מחר ב־]LT",nextWeek:"dddd [בשעה] LT",lastDay:"[אתמול ב־]LT",lastWeek:"[ביום] dddd [האחרון בשעה] LT",sameElse:"L"},relativeTime:{future:"בעוד %s",past:"לפני %s",s:"מספר שניות",ss:"%d שניות",m:"דקה",mm:"%d דקות",h:"שעה",hh:function(e){return 2===e?"שעתיים":e+" שעות"},d:"יום",dd:function(e){return 2===e?"יומיים":e+" ימים"},M:"חודש",MM:function(e){return 2===e?"חודשיים":e+" חודשים"},y:"שנה",yy:function(e){return 2===e?"שנתיים":e%10==0&&10!==e?e+" שנה":e+" שנים"}},meridiemParse:/אחה"צ|לפנה"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,isPM:function(e){return/^(אחה"צ|אחרי הצהריים|בערב)$/.test(e)},meridiem:function(e,t,a){return e<5?"לפנות בוקר":e<10?"בבוקר":e<12?a?'לפנה"צ':"לפני הצהריים":e<18?a?'אחה"צ':"אחרי הצהריים":"בערב"}})}(a("wd/R"))},yPMs:function(e,t,a){!function(e){"use strict";e.defineLocale("sq",{months:"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor".split("_"),monthsShort:"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj".split("_"),weekdays:"E Diel_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë".split("_"),weekdaysShort:"Die_Hën_Mar_Mër_Enj_Pre_Sht".split("_"),weekdaysMin:"D_H_Ma_Më_E_P_Sh".split("_"),weekdaysParseExact:!0,meridiemParse:/PD|MD/,isPM:function(e){return"M"===e.charAt(0)},meridiem:function(e,t,a){return e<12?"PD":"MD"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Sot në] LT",nextDay:"[Nesër në] LT",nextWeek:"dddd [në] LT",lastDay:"[Dje në] LT",lastWeek:"dddd [e kaluar në] LT",sameElse:"L"},relativeTime:{future:"në %s",past:"%s më parë",s:"disa sekonda",ss:"%d sekonda",m:"një minutë",mm:"%d minuta",h:"një orë",hh:"%d orë",d:"një ditë",dd:"%d ditë",M:"një muaj",MM:"%d muaj",y:"një vit",yy:"%d vite"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},z1FC:function(e,t,a){!function(e){"use strict";function t(e,t,a,n){var r={s:["viensas secunds","'iensas secunds"],ss:[e+" secunds",e+" secunds"],m:["'n míut","'iens míut"],mm:[e+" míuts",e+" míuts"],h:["'n þora","'iensa þora"],hh:[e+" þoras",e+" þoras"],d:["'n ziua","'iensa ziua"],dd:[e+" ziuas",e+" ziuas"],M:["'n mes","'iens mes"],MM:[e+" mesen",e+" mesen"],y:["'n ar","'iens ar"],yy:[e+" ars",e+" ars"]};return n||t?r[a][0]:r[a][1]}e.defineLocale("tzl",{months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdays:"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi".split("_"),weekdaysShort:"Súl_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"},meridiemParse:/d\'o|d\'a/i,isPM:function(e){return"d'o"===e.toLowerCase()},meridiem:function(e,t,a){return e>11?a?"d'o":"D'O":a?"d'a":"D'A"},calendar:{sameDay:"[oxhi à] LT",nextDay:"[demà à] LT",nextWeek:"dddd [à] LT",lastDay:"[ieiri à] LT",lastWeek:"[sür el] dddd [lasteu à] LT",sameElse:"L"},relativeTime:{future:"osprei %s",past:"ja%s",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},z3Vd:function(e,t,a){!function(e){"use strict";var t="pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut".split("_");function a(e,a,n,r){var s=function(e){var a=Math.floor(e%1e3/100),n=Math.floor(e%100/10),r=e%10,s="";return a>0&&(s+=t[a]+"vatlh"),n>0&&(s+=(""!==s?" ":"")+t[n]+"maH"),r>0&&(s+=(""!==s?" ":"")+t[r]),""===s?"pagh":s}(e);switch(n){case"ss":return s+" lup";case"mm":return s+" tup";case"hh":return s+" rep";case"dd":return s+" jaj";case"MM":return s+" jar";case"yy":return s+" DIS"}}e.defineLocale("tlh",{months:"tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’".split("_"),monthsShort:"jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’".split("_"),monthsParseExact:!0,weekdays:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysShort:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysMin:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[DaHjaj] LT",nextDay:"[wa’leS] LT",nextWeek:"LLL",lastDay:"[wa’Hu’] LT",lastWeek:"LLL",sameElse:"L"},relativeTime:{future:function(e){var t=e;return t=-1!==e.indexOf("jaj")?t.slice(0,-3)+"leS":-1!==e.indexOf("jar")?t.slice(0,-3)+"waQ":-1!==e.indexOf("DIS")?t.slice(0,-3)+"nem":t+" pIq"},past:function(e){var t=e;return t=-1!==e.indexOf("jaj")?t.slice(0,-3)+"Hu’":-1!==e.indexOf("jar")?t.slice(0,-3)+"wen":-1!==e.indexOf("DIS")?t.slice(0,-3)+"ben":t+" ret"},s:"puS lup",ss:a,m:"wa’ tup",mm:a,h:"wa’ rep",hh:a,d:"wa’ jaj",dd:a,M:"wa’ jar",MM:a,y:"wa’ DIS",yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a("wd/R"))},zx6S:function(e,t,a){!function(e){"use strict";var t={words:{ss:["sekunda","sekunde","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],d:["jedan dan","jednog dana"],dd:["dan","dana","dana"],M:["jedan mesec","jednog meseca"],MM:["mesec","meseca","meseci"],y:["jednu godinu","jedne godine"],yy:["godinu","godine","godina"]},correctGrammaticalCase:function(e,t){return e%10>=1&&e%10<=4&&(e%100<10||e%100>=20)?e%10==1?t[0]:t[1]:t[2]},translate:function(e,a,n,r){var s,i=t.words[n];return 1===n.length?"y"===n&&a?"jedna godina":r||a?i[0]:i[1]:(s=t.correctGrammaticalCase(e,i),"yy"===n&&a&&"godinu"===s?e+" godina":e+" "+s)}};e.defineLocale("sr",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljak_utorak_sreda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sre._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D. M. YYYY.",LL:"D. MMMM YYYY.",LLL:"D. MMMM YYYY. H:mm",LLLL:"dddd, D. MMMM YYYY. H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedelju] [u] LT";case 3:return"[u] [sredu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedelje] [u] LT","[prošlog] [ponedeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"pre %s",s:"nekoliko sekundi",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:t.translate,dd:t.translate,M:t.translate,MM:t.translate,y:t.translate,yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(a("wd/R"))}}]);