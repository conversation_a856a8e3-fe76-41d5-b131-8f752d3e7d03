{"info": {"_postman_id": "26b1056e-b583-4f1c-a402-e5e0ab4e0fa0", "name": "Matar A<PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "11198324"}, "item": [{"name": "Authentication", "item": [{"name": "تسجيل حساب", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"mohamed <PERSON>\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"123\",\r\n    \"country\": \"مصر\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/auth/signup", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "auth", "signup"]}}, "response": []}, {"name": "تسجيل بواسطة جوجل", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON>\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"google_token\": \"1fadf12d45a6d@adf\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/auth/social/google", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "auth", "social", "google"]}, "description": "URL => /api/auth/social/{service}  \nParam => Service  \nParams => google / facebook  \nExample => [http://127.0.0.1:8000/api/auth/social/google](http://127.0.0.1:8000/api/auth/social/googleReturn)\n\n**Return** => User Data\n\n**Body (JSON)** =>\n\nname\n\nemail\n\n{param}_token"}, "response": []}, {"name": "تسجيل بواسطة فيسبوك", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"<PERSON>\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"facebook_token\": \"1fadf12d45a6d@adf\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/auth/social/facebook", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "auth", "social", "facebook"]}, "description": "URL => /api/auth/social/{service}  \nParam => Service  \nParams => google / facebook  \nExample => [http://127.0.0.1:8000/api/auth/social/google](http://127.0.0.1:8000/api/auth/social/googleReturn)\n\n**Return** => User Data\n\n**Body (JSON)** =>\n\nname\n\nemail\n\n{param}_token"}, "response": []}, {"name": "تسجيل الدخول", "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/auth/login", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "طلب حذف الحساب (NEW)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "fe3dd567d865226515a06a101fdc1c81", "type": "text"}], "url": {"raw": "https://admin.rain-app.com/api/request-delete-account", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "request-delete-account"]}}, "response": []}]}, {"name": "التوقعات ومتابعه الحالات", "item": [{"name": "منشور بواسطة الدولة", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/outlooks/1", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "outlooks", "1"]}, "description": "Params : [https://admin.rain-app.com/api/outlooks/{country_id}](https://admin.rain-app.com/api/outlooks/{country_id})\n\nFile Path: https://admin.rain-app.com/storage/outlooks/"}, "response": []}, {"name": "جميع المنشورات", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/outlooks", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "outlooks"]}, "description": "StartFragment\n\nFile Path: [https://admin.rain-app.com/storage/outlooks/](https://desktop.postman.com/?desktopVersion=9.19.0&userId=********&teamId=0)\n\nEndFragment"}, "response": [{"name": "جميع المنشورات", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://127.0.0.1:8000/api/outlooks", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "outlooks"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "html", "header": [{"key": "Host", "value": "127.0.0.1:8000"}, {"key": "Date", "value": "Mon, 17 Apr 2023 18:55:00 GMT"}, {"key": "Connection", "value": "close"}, {"key": "X-Powered-By", "value": "PHP/8.2.1"}, {"key": "Content-type", "value": "text/html; charset=UTF-8"}], "cookie": [], "body": "<script> Sfdump = window.Sfdump || (function (doc) { var refStyle = doc.createElement('style'), rxEsc = /([.*+?^${}()|\\[\\]\\/\\\\])/g, idRx = /\\bsf-dump-\\d+-ref[012]\\w+\\b/, keyHint = 0 <= navigator.platform.toUpperCase().indexOf('MAC') ? 'Cmd' : 'Ctrl', addEventListener = function (e, n, cb) { e.addEventListener(n, cb, false); }; refStyle.innerHTML = 'pre.sf-dump .sf-dump-compact, .sf-dump-str-collapse .sf-dump-str-collapse, .sf-dump-str-expand .sf-dump-str-expand { display: none; }'; doc.head.appendChild(refStyle); refStyle = doc.createElement('style'); doc.head.appendChild(refStyle); if (!doc.addEventListener) { addEventListener = function (element, eventName, callback) { element.attachEvent('on' + eventName, function (e) { e.preventDefault = function () {e.returnValue = false;}; e.target = e.srcElement; callback(e); }); }; } function toggle(a, recursive) { var s = a.nextSibling || {}, oldClass = s.className, arrow, newClass; if (/\\bsf-dump-compact\\b/.test(oldClass)) { arrow = '&#9660;'; newClass = 'sf-dump-expanded'; } else if (/\\bsf-dump-expanded\\b/.test(oldClass)) { arrow = '&#9654;'; newClass = 'sf-dump-compact'; } else { return false; } if (doc.createEvent && s.dispatchEvent) { var event = doc.createEvent('Event'); event.initEvent('sf-dump-expanded' === newClass ? 'sfbeforedumpexpand' : 'sfbeforedumpcollapse', true, false); s.dispatchEvent(event); } a.lastChild.innerHTML = arrow; s.className = s.className.replace(/\\bsf-dump-(compact|expanded)\\b/, newClass); if (recursive) { try { a = s.querySelectorAll('.'+oldClass); for (s = 0; s < a.length; ++s) { if (-1 == a[s].className.indexOf(newClass)) { a[s].className = newClass; a[s].previousSibling.lastChild.innerHTML = arrow; } } } catch (e) { } } return true; }; function collapse(a, recursive) { var s = a.nextSibling || {}, oldClass = s.className; if (/\\bsf-dump-expanded\\b/.test(oldClass)) { toggle(a, recursive); return true; } return false; }; function expand(a, recursive) { var s = a.nextSibling || {}, oldClass = s.className; if (/\\bsf-dump-compact\\b/.test(oldClass)) { toggle(a, recursive); return true; } return false; }; function collapseAll(root) { var a = root.querySelector('a.sf-dump-toggle'); if (a) { collapse(a, true); expand(a); return true; } return false; } function reveal(node) { var previous, parents = []; while ((node = node.parentNode || {}) && (previous = node.previousSibling) && 'A' === previous.tagName) { parents.push(previous); } if (0 !== parents.length) { parents.forEach(function (parent) { expand(parent); }); return true; } return false; } function highlight(root, activeNode, nodes) { resetHighlightedNodes(root); Array.from(nodes||[]).forEach(function (node) { if (!/\\bsf-dump-highlight\\b/.test(node.className)) { node.className = node.className + ' sf-dump-highlight'; } }); if (!/\\bsf-dump-highlight-active\\b/.test(activeNode.className)) { activeNode.className = activeNode.className + ' sf-dump-highlight-active'; } } function resetHighlightedNodes(root) { Array.from(root.querySelectorAll('.sf-dump-str, .sf-dump-key, .sf-dump-public, .sf-dump-protected, .sf-dump-private')).forEach(function (strNode) { strNode.className = strNode.className.replace(/\\bsf-dump-highlight\\b/, ''); strNode.className = strNode.className.replace(/\\bsf-dump-highlight-active\\b/, ''); }); } return function (root, x) { root = doc.getElementById(root); var indentRx = new RegExp('^('+(root.getAttribute('data-indent-pad') || ' ').replace(rxEsc, '\\\\$1')+')+', 'm'), options = {\"maxDepth\":1,\"maxStringLength\":160,\"fileLinkFormat\":false}, elt = root.getElementsByTagName('A'), len = elt.length, i = 0, s, h, t = []; while (i < len) t.push(elt[i++]); for (i in x) { options[i] = x[i]; } function a(e, f) { addEventListener(root, e, function (e, n) { if ('A' == e.target.tagName) { f(e.target, e); } else if ('A' == e.target.parentNode.tagName) { f(e.target.parentNode, e); } else { n = /\\bsf-dump-ellipsis\\b/.test(e.target.className) ? e.target.parentNode : e.target; if ((n = n.nextElementSibling) && 'A' == n.tagName) { if (!/\\bsf-dump-toggle\\b/.test(n.className)) { n = n.nextElementSibling || n; } f(n, e, true); } } }); }; function isCtrlKey(e) { return e.ctrlKey || e.metaKey; } function xpathString(str) { var parts = str.match(/[^'\"]+|['\"]/g).map(function (part) { if (\"'\" == part) { return '\"\\'\"'; } if ('\"' == part) { return \"'\\\"'\"; } return \"'\" + part + \"'\"; }); return \"concat(\" + parts.join(\",\") + \", '')\"; } function xpathHasClass(className) { return \"contains(concat(' ', normalize-space(@class), ' '), ' \" + className +\" ')\"; } addEventListener(root, 'mouseover', function (e) { if ('' != refStyle.innerHTML) { refStyle.innerHTML = ''; } }); a('mouseover', function (a, e, c) { if (c) { e.target.style.cursor = \"pointer\"; } else if (a = idRx.exec(a.className)) { try { refStyle.innerHTML = 'pre.sf-dump .'+a[0]+'{background-color: #B729D9; color: #FFF !important; border-radius: 2px}'; } catch (e) { } } }); a('click', function (a, e, c) { if (/\\bsf-dump-toggle\\b/.test(a.className)) { e.preventDefault(); if (!toggle(a, isCtrlKey(e))) { var r = doc.getElementById(a.getAttribute('href').slice(1)), s = r.previousSibling, f = r.parentNode, t = a.parentNode; t.replaceChild(r, a); f.replaceChild(a, s); t.insertBefore(s, r); f = f.firstChild.nodeValue.match(indentRx); t = t.firstChild.nodeValue.match(indentRx); if (f && t && f[0] !== t[0]) { r.innerHTML = r.innerHTML.replace(new RegExp('^'+f[0].replace(rxEsc, '\\\\$1'), 'mg'), t[0]); } if (/\\bsf-dump-compact\\b/.test(r.className)) { toggle(s, isCtrlKey(e)); } } if (c) { } else if (doc.getSelection) { try { doc.getSelection().removeAllRanges(); } catch (e) { doc.getSelection().empty(); } } else { doc.selection.empty(); } } else if (/\\bsf-dump-str-toggle\\b/.test(a.className)) { e.preventDefault(); e = a.parentNode.parentNode; e.className = e.className.replace(/\\bsf-dump-str-(expand|collapse)\\b/, a.parentNode.className); } }); elt = root.getElementsByTagName('SAMP'); len = elt.length; i = 0; while (i < len) t.push(elt[i++]); len = t.length; for (i = 0; i < len; ++i) { elt = t[i]; if ('SAMP' == elt.tagName) { a = elt.previousSibling || {}; if ('A' != a.tagName) { a = doc.createElement('A'); a.className = 'sf-dump-ref'; elt.parentNode.insertBefore(a, elt); } else { a.innerHTML += ' '; } a.title = (a.title ? a.title+'\\n[' : '[')+keyHint+'+click] Expand all children'; a.innerHTML += elt.className == 'sf-dump-compact' ? '\n    <span>&#9654;</span>' : '\n    <span>&#9660;</span>'; a.className += ' sf-dump-toggle'; x = 1; if ('sf-dump' != elt.parentNode.className) { x += elt.parentNode.getAttribute('data-depth')/1; } } else if (/\\bsf-dump-ref\\b/.test(elt.className) && (a = elt.getAttribute('href'))) { a = a.slice(1); elt.className += ' '+a; if (/[\\[{]$/.test(elt.previousSibling.nodeValue)) { a = a != elt.nextSibling.id && doc.getElementById(a); try { s = a.nextSibling; elt.appendChild(a); s.parentNode.insertBefore(a, s); if (/^[@#]/.test(elt.innerHTML)) { elt.innerHTML += '\n    <span>&#9654;</span>'; } else { elt.innerHTML = '\n    <span>&#9654;</span>'; elt.className = 'sf-dump-ref'; } elt.className += ' sf-dump-toggle'; } catch (e) { if ('&' == elt.innerHTML.charAt(0)) { elt.innerHTML = '&#8230;'; elt.className = 'sf-dump-ref'; } } } } } if (doc.evaluate && Array.from && root.children.length > 1) { root.setAttribute('tabindex', 0); SearchState = function () { this.nodes = []; this.idx = 0; }; SearchState.prototype = { next: function () { if (this.isEmpty()) { return this.current(); } this.idx = this.idx< (this.nodes.length - 1) ? this.idx + 1 : 0; return this.current(); }, previous: function () { if (this.isEmpty()) { return this.current(); } this.idx = this.idx > 0 ? this.idx - 1 : (this.nodes.length - 1); return this.current(); }, isEmpty: function () { return 0 === this.count(); }, current: function () { if (this.isEmpty()) { return null; } return this.nodes[this.idx]; }, reset: function () { this.nodes = []; this.idx = 0; }, count: function () { return this.nodes.length; }, }; function showCurrent(state) { var currentNode = state.current(), currentRect, searchRect; if (currentNode) { reveal(currentNode); highlight(root, currentNode, state.nodes); if ('scrollIntoView' in currentNode) { currentNode.scrollIntoView(true); currentRect = currentNode.getBoundingClientRect(); searchRect = search.getBoundingClientRect(); if (currentRect.top < (searchRect.top + searchRect.height)) { window.scrollBy(0, -(searchRect.top + searchRect.height + 5)); } } } counter.textContent = (state.isEmpty() ? 0 : state.idx + 1) + ' of ' + state.count(); } var search = doc.createElement('div'); search.className = 'sf-dump-search-wrapper sf-dump-search-hidden'; search.innerHTML = ' \n    <input type=\"text\" class=\"sf-dump-search-input\">\n    <span class=\"sf-dump-search-count\">0 of 0<\\/span>\n        <button type=\"button\" class=\"sf-dump-search-input-previous\" tabindex=\"-1\">\n            <svg viewBox=\"0 0 1792 1792\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M1683 1331l-166 165q-19 19-45 19t-45-19L896 965l-531 531q-19 19-45 19t-45-19l-166-165q-19-19-19-45.5t19-45.5l742-741q19-19 45-19t45 19l742 741q19 19 19 45.5t-19 45.5z\"\\/><\\/svg><\\/button>\n                <button type=\"button\" class=\"sf-dump-search-input-next\" tabindex=\"-1\">\n                    <svg viewBox=\"0 0 1792 1792\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path d=\"M1683 808l-742 741q-19 19-45 19t-45-19L109 808q-19-19-19-45.5t19-45.5l166-165q19-19 45-19t45 19l531 531 531-531q19-19 45-19t45 19l166 165q19 19 19 45.5t-19 45.5z\"\\/><\\/svg><\\/button> '; root.insertBefore(search, root.firstChild); var state = new SearchState(); var searchInput = search.querySelector('.sf-dump-search-input'); var counter = search.querySelector('.sf-dump-search-count'); var searchInputTimer = 0; var previousSearchQuery = ''; addEventListener(searchInput, 'keyup', function (e) { var searchQuery = e.target.value; /* Don't perform anything if the pressed key didn't change the query */ if (searchQuery === previousSearchQuery) { return; } previousSearchQuery = searchQuery; clearTimeout(searchInputTimer); searchInputTimer = setTimeout(function () { state.reset(); collapseAll(root); resetHighlightedNodes(root); if ('' === searchQuery) { counter.textContent = '0 of 0'; return; } var classMatches = [ \"sf-dump-str\", \"sf-dump-key\", \"sf-dump-public\", \"sf-dump-protected\", \"sf-dump-private\", ].map(xpathHasClass).join(' or '); var xpathResult = doc.evaluate('.//span[' + classMatches + '][contains(translate(child::text(), ' + xpathString(searchQuery.toUpperCase()) + ', ' + xpathString(searchQuery.toLowerCase()) + '), ' + xpathString(searchQuery.toLowerCase()) + ')]', root, null, XPathResult.ORDERED_NODE_ITERATOR_TYPE, null); while (node = xpathResult.iterateNext()) state.nodes.push(node); showCurrent(state); }, 400); }); Array.from(search.querySelectorAll('.sf-dump-search-input-next, .sf-dump-search-input-previous')).forEach(function (btn) { addEventListener(btn, 'click', function (e) { e.preventDefault(); -1 !== e.target.className.indexOf('next') ? state.next() : state.previous(); searchInput.focus(); collapseAll(root); showCurrent(state); }) }); addEventListener(root, 'keydown', function (e) { var isSearchActive = !/\\bsf-dump-search-hidden\\b/.test(search.className); if ((114 === e.keyCode && !isSearchActive) || (isCtrlKey(e) && 70 === e.keyCode)) { /* F3 or CMD/CTRL + F */ if (70 === e.keyCode && document.activeElement === searchInput) { /* * If CMD/CTRL + F is hit while having focus on search input, * the user probably meant to trigger browser search instead. * Let the browser execute its behavior: */ return; } e.preventDefault(); search.className = search.className.replace(/\\bsf-dump-search-hidden\\b/, ''); searchInput.focus(); } else if (isSearchActive) { if (27 === e.keyCode) { /* ESC key */ search.className += ' sf-dump-search-hidden'; e.preventDefault(); resetHighlightedNodes(root); searchInput.value = ''; } else if ( (isCtrlKey(e) && 71 === e.keyCode) /* CMD/CTRL + G */ || 13 === e.keyCode /* Enter */ || 114 === e.keyCode /* F3 */ ) { e.preventDefault(); e.shiftKey ? state.previous() : state.next(); collapseAll(root); showCurrent(state); } } }); } if (0 >= options.maxStringLength) { return; } try { elt = root.querySelectorAll('.sf-dump-str'); len = elt.length; i = 0; t = []; while (i < len) t.push(elt[i++]); len = t.length; for (i = 0; i < len; ++i) { elt = t[i]; s = elt.innerText || elt.textContent; x = s.length - options.maxStringLength; if (0 < x) { h = elt.innerHTML; elt[elt.innerText ? 'innerText' : 'textContent'] = s.substring(0, options.maxStringLength); elt.className += ' sf-dump-str-collapse'; elt.innerHTML = '\n                        <span class=sf-dump-str-collapse>'+h+'\n                            <a class=\"sf-dump-ref sf-dump-str-toggle\" title=\"Collapse\"> &#9664;</a>\n                        </span>'+ '\n                        <span class=sf-dump-str-expand>'+elt.innerHTML+'\n                            <a class=\"sf-dump-ref sf-dump-str-toggle\" title=\"'+x+' remaining characters\"> &#9654;</a>\n                        </span>'; } } } catch (e) { } }; })(document);\n                    </script>\n                    <style> pre.sf-dump { display: block; white-space: pre; padding: 5px; overflow: initial !important; } pre.sf-dump:after { content: \"\"; visibility: hidden; display: block; height: 0; clear: both; } pre.sf-dump span { display: inline; } pre.sf-dump a { text-decoration: none; cursor: pointer; border: 0; outline: none; color: inherit; } pre.sf-dump img { max-width: 50em; max-height: 50em; margin: .5em 0 0 0; padding: 0; background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAAAAAA6mKC9AAAAHUlEQVQY02O8zAABilCaiQEN0EeA8QuUcX9g3QEAAjcC5piyhyEAAAAASUVORK5CYII=) #D3D3D3; } pre.sf-dump .sf-dump-ellipsis { display: inline-block; overflow: visible; text-overflow: ellipsis; max-width: 5em; white-space: nowrap; overflow: hidden; vertical-align: top; } pre.sf-dump .sf-dump-ellipsis+.sf-dump-ellipsis { max-width: none; } pre.sf-dump code { display:inline; padding:0; background:none; } .sf-dump-public.sf-dump-highlight, .sf-dump-protected.sf-dump-highlight, .sf-dump-private.sf-dump-highlight, .sf-dump-str.sf-dump-highlight, .sf-dump-key.sf-dump-highlight { background: rgba(111, 172, 204, 0.3); border: 1px solid #7DA0B1; border-radius: 3px; } .sf-dump-public.sf-dump-highlight-active, .sf-dump-protected.sf-dump-highlight-active, .sf-dump-private.sf-dump-highlight-active, .sf-dump-str.sf-dump-highlight-active, .sf-dump-key.sf-dump-highlight-active { background: rgba(253, 175, 0, 0.4); border: 1px solid #ffa500; border-radius: 3px; } pre.sf-dump .sf-dump-search-hidden { display: none !important; } pre.sf-dump .sf-dump-search-wrapper { font-size: 0; white-space: nowrap; margin-bottom: 5px; display: flex; position: -webkit-sticky; position: sticky; top: 5px; } pre.sf-dump .sf-dump-search-wrapper > * { vertical-align: top; box-sizing: border-box; height: 21px; font-weight: normal; border-radius: 0; background: #FFF; color: #757575; border: 1px solid #BBB; } pre.sf-dump .sf-dump-search-wrapper > input.sf-dump-search-input { padding: 3px; height: 21px; font-size: 12px; border-right: none; border-top-left-radius: 3px; border-bottom-left-radius: 3px; color: #000; min-width: 15px; width: 100%; } pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-next, pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-previous { background: #F2F2F2; outline: none; border-left: none; font-size: 0; line-height: 0; } pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-next { border-top-right-radius: 3px; border-bottom-right-radius: 3px; } pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-next > svg, pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-input-previous > svg { pointer-events: none; width: 12px; height: 12px; } pre.sf-dump .sf-dump-search-wrapper > .sf-dump-search-count { display: inline-block; padding: 0 5px; margin: 0; border-left: none; line-height: 21px; font-size: 12px; }pre.sf-dump, pre.sf-dump .sf-dump-default{background-color:#18171B; color:#FF8400; line-height:1.2em; font:12px Menlo, Monaco, Consolas, monospace; word-wrap: break-word; white-space: pre-wrap; position:relative; z-index:99999; word-break: break-all}pre.sf-dump .sf-dump-num{font-weight:bold; color:#1299DA}pre.sf-dump .sf-dump-const{font-weight:bold}pre.sf-dump .sf-dump-str{font-weight:bold; color:#56DB3A}pre.sf-dump .sf-dump-note{color:#1299DA}pre.sf-dump .sf-dump-ref{color:#A0A0A0}pre.sf-dump .sf-dump-public{color:#FFFFFF}pre.sf-dump .sf-dump-protected{color:#FFFFFF}pre.sf-dump .sf-dump-private{color:#FFFFFF}pre.sf-dump .sf-dump-meta{color:#B729D9}pre.sf-dump .sf-dump-key{color:#56DB3A}pre.sf-dump .sf-dump-index{color:#1299DA}pre.sf-dump .sf-dump-ellipsis{color:#FF8400}pre.sf-dump .sf-dump-ns{user-select:none;}pre.sf-dump .sf-dump-ellipsis-note{color:#1299DA}</style>\n                    <pre class=sf-dump id=sf-dump-257985005 data-indent-pad=\"  \">\n                        <span class=sf-dump-note>array:13</span> [\n                        <samp data-depth=1 class=sf-dump-expanded>\n                            <span style=\"color: #A0A0A0;\"> // app\\Http\\Controllers\\OutlooksController.php:63</span>\n  \"\n                            <span class=sf-dump-key>current_page</span>\" =>\n                            <span class=sf-dump-num>1</span>\n  \"\n                            <span class=sf-dump-key>first_page_url</span>\" => \"\n                            <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=1</span>\"\n  \"\n                            <span class=sf-dump-key>from</span>\" =>\n                            <span class=sf-dump-num>1</span>\n  \"\n                            <span class=sf-dump-key>last_page</span>\" =>\n                            <span class=sf-dump-num>21</span>\n  \"\n                            <span class=sf-dump-key>last_page_url</span>\" => \"\n                            <span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/api/outlooks?page=21</span>\"\n  \"\n                            <span class=sf-dump-key>links</span>\" =>\n                            <span class=sf-dump-note>array:15</span> [\n                            <samp data-depth=2 class=sf-dump-compact>\n                                <span class=sf-dump-index>0</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" =>\n                                    <span class=sf-dump-const>null</span>\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str title=\"16 characters\">&amp;laquo; Previous</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>1</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=1</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>1</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>true</span>\n                                </samp>]\n                                <span class=sf-dump-index>2</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=2</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>2</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>3</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=3</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>3</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>4</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=4</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>4</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>5</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=5</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>5</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>6</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=6</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>6</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>7</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=7</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>7</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>8</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=8</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>8</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>9</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=9</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str>9</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>10</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/api/outlooks?page=10</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str title=\"2 characters\">10</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>11</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" =>\n                                    <span class=sf-dump-const>null</span>\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str title=\"3 characters\">...</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>12</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/api/outlooks?page=20</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str title=\"2 characters\">20</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>13</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"42 characters\">http://127.0.0.1:8000/api/outlooks?page=21</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str title=\"2 characters\">21</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                                <span class=sf-dump-index>14</span> =>\n                                <span class=sf-dump-note>array:3</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>url</span>\" => \"\n                                    <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=2</span>\"\n      \"\n                                    <span class=sf-dump-key>label</span>\" => \"\n                                    <span class=sf-dump-str title=\"12 characters\">Next &amp;raquo;</span>\"\n      \"\n                                    <span class=sf-dump-key>active</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n                                </samp>]\n                            </samp>]\n  \"\n                            <span class=sf-dump-key>next_page_url</span>\" => \"\n                            <span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/api/outlooks?page=2</span>\"\n  \"\n                            <span class=sf-dump-key>path</span>\" => \"\n                            <span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/api/outlooks</span>\"\n  \"\n                            <span class=sf-dump-key>per_page</span>\" =>\n                            <span class=sf-dump-num>1</span>\n  \"\n                            <span class=sf-dump-key>prev_page_url</span>\" =>\n                            <span class=sf-dump-const>null</span>\n  \"\n                            <span class=sf-dump-key>to</span>\" =>\n                            <span class=sf-dump-num>1</span>\n  \"\n                            <span class=sf-dump-key>total</span>\" =>\n                            <span class=sf-dump-num>21</span>\n  \"\n                            <span class=sf-dump-key>data</span>\" =>\n                            <span class=sf-dump-note>array:1</span> [\n                            <samp data-depth=2 class=sf-dump-compact>\n                                <span class=sf-dump-index>0</span> =>\n                                <span class=sf-dump-note>array:12</span> [\n                                <samp data-depth=3 class=sf-dump-compact>\n      \"\n                                    <span class=sf-dump-key>id</span>\" =>\n                                    <span class=sf-dump-num>212</span>\n      \"\n                                    <span class=sf-dump-key>title</span>\" => \"\n                                    <span class=sf-dump-str title=\"6 characters\">testtt</span>\"\n      \"\n                                    <span class=sf-dump-key>date</span>\" => \"\n                                    <span class=sf-dump-str title=\"19 characters\">2023-02-16 12:00:00</span>\"\n      \"\n                                    <span class=sf-dump-key>country</span>\" => \"\n                                    <span class=sf-dump-str title=\"3 characters\">&#1602;&#1591;&#1585;</span>\"\n      \"\n                                    <span class=sf-dump-key>details</span>\" => \"\n                                    <span class=sf-dump-str title=\"4 characters\">test</span>\"\n      \"\n                                    <span class=sf-dump-key>schedule</span>\" => \"\n                                    <span class=sf-dump-str title=\"18 characters\">2023-02-24 12:00:0</span>\"\n      \"\n                                    <span class=sf-dump-key>hide</span>\" => \"\"\n      \"\n                                    <span class=sf-dump-key>likes</span>\" =>\n                                    <span class=sf-dump-num>0</span>\n      \"\n                                    <span class=sf-dump-key>shares</span>\" =>\n                                    <span class=sf-dump-num>0</span>\n      \"\n                                    <span class=sf-dump-key>liked</span>\" =>\n                                    <span class=sf-dump-const>false</span>\n      \"\n                                    <span class=sf-dump-key>files</span>\" =>\n                                    <span class=sf-dump-note>array:1</span> [\n                                    <samp data-depth=4 class=sf-dump-compact>\n                                        <span class=sf-dump-index>0</span> =>\n                                        <span class=sf-dump-note>array:3</span> [\n                                        <samp data-depth=5 class=sf-dump-compact>\n          \"\n                                            <span class=sf-dump-key>id</span>\" =>\n                                            <span class=sf-dump-num>315</span>\n          \"\n                                            <span class=sf-dump-key>outlook_id</span>\" => \"\n                                            <span class=sf-dump-str title=\"3 characters\">212</span>\"\n          \"\n                                            <span class=sf-dump-key>file</span>\" => \"\n                                            <span class=sf-dump-str title=\"17 characters\">63edfdfff3304.png</span>\"\n                                        </samp>]\n                                    </samp>]\n      \"\n                                    <span class=sf-dump-key>comments</span>\" => []\n                                </samp>]\n                            </samp>]\n                        </samp>]\n                    </pre>\n                    <script>Sfdump(\"sf-dump-257985005\")</script>"}]}, {"name": "صفحة ارسال صور ومقاطع الطقس", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "0f0fe27edf282f695be398a505bb0ef3", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "user_id", "value": "3", "type": "text"}, {"key": "photographer", "value": "عبدالحمن ابوزيد", "type": "text"}, {"key": "location", "value": "مصر", "type": "text"}, {"key": "date", "value": "2022-05-31 02:00:00", "type": "text"}, {"key": "media", "type": "file", "src": "/C:/Users/<USER>/Desktop/41SpQI9hz4S._AC_UL600_SR600,600_.jpg"}]}, "url": {"raw": "https://admin.rain-app.com/api/send-pending-shot", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "send-pending-shot"]}}, "response": []}, {"name": "ارسال تعليق علي منشور", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "e38abb5c407cbeebde38106421ae42cb", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"outlook_id\": 99,\r\n    \"comment\": \"Test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/send-comment", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "send-comment"]}}, "response": []}, {"name": "اعجاب بمنشور", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "e38abb5c407cbeebde38106421ae42cb", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"outlook_id\": 110\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/submit-like", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "submit-like"]}}, "response": []}, {"name": "اضافة رد", "request": {"method": "POST", "header": [{"key": "e38abb5c407cbeebde38106421ae42cb", "value": "", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"comment_id\": 1,\r\n    \"outlook_id\": 1,\r\n    \"reply\": \"اشكرك\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/send-reply", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "send-reply"]}}, "response": []}, {"name": "مشاركة منشور", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "e38abb5c407cbeebde38106421ae42cb", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"outlook_id\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/outlook/share", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "outlook", "share"]}}, "response": []}]}, {"name": "صور ومقاطع الطقس", "item": [{"name": "جميع صور ومقاطع الطقس", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/weatherShots", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "weatherShots"]}, "description": "Media path : https://admin.rain-app.com/storage/weather-shots/"}, "response": []}, {"name": "مشاركة", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "e38abb5c407cbeebde38106421ae42cb", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"shot_id\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/weather-shot/share", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "weather-shot", "share"]}}, "response": []}]}, {"name": "التوقعات ومتابعة الحالات V2 (NEW)", "item": [{"name": "التوقعات ومتابعة الحالات", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "", "value": "", "type": "text"}]}, "url": {"raw": "https://admin.rain-app.com/api/v2/outlooks", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "v2", "outlooks"]}, "description": "Media path : https://admin.rain-app.com/storage/weather-shots/"}, "response": []}, {"name": "التوقعات ومتابعة الحالات - حس<PERSON> الدولة", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "898389a333285ca82c2ac82c1bcb8d87", "description": "مطلوب فقط عندما يكون نظام الاشتراكات مفعل من لوحة التحكم ( يمكن لاي شخص الاشتراك بعد مشاهده اعلان من خلال طلب endpoint subscribe via ad )\n", "type": "text"}], "url": {"raw": "https://admin.rain-app.com/api/v2/outlooks/51", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "v2", "outlooks", "51"]}, "description": "Media path : https://admin.rain-app.com/storage/weather-shots/"}, "response": []}, {"name": "التوقعات ومتابعة الحالات - العامة", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/v2/outlooks/public", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "v2", "outlooks", "public"]}, "description": "Media path : https://admin.rain-app.com/storage/weather-shots/"}, "response": []}, {"name": "الاشتراك بقسم توقعات ومتابعة الحالات لدوله (بعد مشاهده اعلان)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "898389a333285ca82c2ac82c1bcb8d87", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "https://admin.rain-app.com/api/subscribe-via-ad?country=51", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "subscribe-via-ad"], "query": [{"key": "country", "value": "51"}]}}, "response": []}]}, {"name": "صور الاقمار الصناعية", "item": [{"name": "رابط استعراض الخريطة", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/sattelite-link", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "sattelite-link"]}}, "response": []}]}, {"name": "تذاكر الدعم", "item": [{"name": "ارسال تذكرة دعم", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"content\": \"هناك مشكلة في النظام\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/send-ticket", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "send-ticket"]}}, "response": []}]}, {"name": "التنبيهات", "item": [{"name": "الاشعارات", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/all-notifications", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "all-notifications"]}}, "response": []}]}, {"name": "الاعلانات", "item": [{"name": "الاعلانات", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/ads", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "ads"]}, "description": "Media path : https://admin.rain-app.com/storage/ads/"}, "response": []}, {"name": "زيادة مشاهدات", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "e175affac0b0910991e82fdcee85f093", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"ad_id\": 3\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/increase-views", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "increase-views"]}}, "response": []}, {"name": "زيادة كليك الاعلانات", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "e175affac0b0910991e82fdcee85f093", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"ad_id\": 3\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/increase-clicks", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "increase-clicks"]}}, "response": []}]}, {"name": "اليوزر", "item": [{"name": "تعديل بيانات الملف الشخصي", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "0f0fe27edf282f695be398a505bb0ef3", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "pic", "type": "file", "src": "logo.png"}, {"key": "name", "value": "<PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "country", "value": "البرازيل", "type": "text"}, {"key": "phone", "value": "+2018515", "type": "text"}, {"key": "password", "value": "123", "type": "text"}]}, "url": {"raw": "https://admin.rain-app.com/api/update-profile", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "update-profile"]}, "description": "**حالات علشان تشتغل بيه صح :**\n\n1.  لو اليوزر غير الباسوورد في الصفحة ابعته لو مغيرهوش متبعتهوش من الاساس\n2.  لو اليوزر غير الصورة ابعتها كـ form data طبعا مع البيانات الباقية معاد الباسوورد لو هو مغيرش الباسوورد\n3.  لو اليوزر مغيرش لا باسوورد ولا صورة متبعتهمش ابعت البيانات التانية اللي هي الاسم والايميل والدولة ورقم الموبايل"}, "response": []}, {"name": "صفحة المشاركات الخاصة بيه", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "898389a333285ca82c2ac82c1bcb8d87", "type": "text"}], "url": {"raw": "https://admin.rain-app.com/api/shared-posts", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "shared-posts"]}}, "response": []}, {"name": "اضافة كوبون في صفحة الكوبونات", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "898389a333285ca82c2ac82c1bcb8d87", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"coupon\": \"تست\",\r\n    \"device_id\": \"123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/apply-coupon", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "apply-coupon"]}, "description": "الـ body موجود وهو عبارة عن :\n\n1.  الكوبون اللي هو ضافه\n2.  الـ device_id اللي قولت انك حتجيبه وتبعته علشان ميستخدمش الكوبون تاني من نفس الجهاز\n3.  طبعا التوكين بتاع اليوزر في الهيدر\n    \n\n**ملاحظات :**\n\n1.  فيه نوعين من الكوبونات اول نوع وهو كوبون عادي بيتضاف من لوحة التحكم وده بيحذف الاعلانات لمدة معينة وانا حرجعلك المدة في الريسبونس ده في حالة انه لو كوبون عادي\n2.  فيه النوع التاني وهو خاص بالمسوق وهو شخص بيكون ليه كوبون كل ما حد يستخدمه بيكسب من خلاله ولو انا مرجعتلكش الريسبونس اللي فيه الـ days وكدا ده معناه انه مسوق وبكدا انا ححطله عمولة وبس\n    \n\nللتأكيد :\n\n1.  كوبون عادي => يحذف اعلانات لمدة معينة حبعتهالك"}, "response": []}, {"name": "تسجيل اشتراك المستخدم في التطبيق", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "0f0fe27edf282f695be398a505bb0ef3", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"amount\": \"200\",\r\n    \"start_date\": \"2022-06-16\",\r\n    \"expire_date\": \"2022-12-16\",\r\n    \"pay_method\": \"google-pay\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/record-subscribe", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "record-subscribe"]}, "description": "**ملاحظات :**\n\nالـ api ده في حالة انه اشترك في التطبيق يعني دفع علشان يشيل الاعلانات بعد ما العملية عندك تبقي success خلاص حتبعتلي علي الـ api ده علشان اسجل عندي انه اشترك\n\nالـ، body :\n\n1.  amount => قيمة الاشتراك\n2.  start_date => تاريخ بداية الاشتراك\n3.  expire_date => تاريخ نهاية الاشتراك\n4.  pay_method => الاشتراك تم عن طريق google-pay ولا apple-pay"}, "response": []}, {"name": "بيانات البروفايل", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "0f0fe27edf282f695be398a505bb0ef3", "type": "text"}], "url": {"raw": "https://admin.rain-app.com/api/profile", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "profile"]}}, "response": []}]}, {"name": "خطوات صفحة هل نسيت كلمة المرور", "item": [{"name": "اعادة تعيين كلمة المرور", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\":\"<EMAIL>\",\r\n    \"code\": \"ff\",\r\n    \"password\": \"123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/reset-password", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "reset-password"]}, "description": "**ملحوظة :**\n\nدي الصفحة اللي حيغير فيها كلمة المرور حتبعتلي كل الخطوات اللي فاتت تاني اللي هي الايميل والكود تاني مع الباسوورد اللي هو ضافة"}, "response": []}, {"name": "التحقق من كود تغيير كلمة المرور", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"code\": \"ff\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/check-reset-code", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "check-reset-code"]}, "description": "**ملحوظة :**\n\nخاص بالصفحة اللي حيضيف فيها الكود اللي اتبعتله علي الايميل وهو مكون من 5 خانات (حروف وارقام) مش ارقام بس\n\nوحتبعتلي ايميله والكود اللي هو دخله ولما يرجعلك ريسبونس 200\n\nكدا الكود تمام وحتنقله علي الصفحة اللي حيغير فيها كلمة المرور"}, "response": []}, {"name": "صفحة ارسال رابط تغيير كلمة المرور", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://admin.rain-app.com/api/send-reset-password", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "send-reset-password"]}, "description": "**ملحوظة :**\n\nخاص بالصفحة اللي حيحط فيها اليوزر الايميل بتاعه علشان يتبعتله ميل فيه كود علي ايميل والكود ده اللي حيستخدمه في اعادة التعيين"}, "response": []}]}, {"name": "الدول", "request": {"method": "GET", "header": [], "url": {"raw": "https://admin.rain-app.com/api/countries", "protocol": "https", "host": ["admin", "rain-app", "com"], "path": ["api", "countries"]}, "description": "Icon link : [https://admin.rain-app.com/storage/countries/](https://admin.rain-app.com/storage/countries/)"}, "response": []}]}