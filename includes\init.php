<?php
/**
 * ملف التهيئة الرئيسي
 * Main Initialization File
 */

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملفات الإعدادات
require_once __DIR__ . '/../config/app_config.php';

// تضمين الملفات الأساسية
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/Auth.php';

// إنشاء مجلدات الرفع إذا لم تكن موجودة
createDirectoryIfNotExists(UPLOAD_PATH);
createDirectoryIfNotExists(UPLOAD_PATH . 'outlooks/');
createDirectoryIfNotExists(UPLOAD_PATH . 'weather-shots/');
createDirectoryIfNotExists(UPLOAD_PATH . 'notifications/');
createDirectoryIfNotExists(UPLOAD_PATH . 'ads/');
createDirectoryIfNotExists(UPLOAD_PATH . 'countries/');
createDirectoryIfNotExists(UPLOAD_PATH . 'profiles/');
createDirectoryIfNotExists('logs/');

// تعيين ترميز UTF-8
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// تعيين headers للـ API
if (strpos($_SERVER['REQUEST_URI'], '/api/') !== false) {
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
    
    // التعامل مع طلبات OPTIONS
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }
}

// إنشاء كائنات عامة
$db = Database::getInstance();
$auth = new Auth();

// وظائف مساعدة للـ API
function getRequestData() {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        // إذا لم يكن JSON صحيح، استخدم $_POST
        $data = $_POST;
    }
    
    return $data ? sanitizeInput($data) : [];
}

function getAuthToken() {
    $headers = getallheaders();
    
    if (isset($headers['Authorization'])) {
        return $headers['Authorization'];
    }
    
    if (isset($headers['authorization'])) {
        return $headers['authorization'];
    }
    
    return null;
}

function requireAuth($type = 'user') {
    global $auth;
    
    $token = getAuthToken();
    
    if (!$token) {
        errorResponse('Token is missing', 401);
    }
    
    $user = $auth->validateToken($token, $type);
    
    if (!$user) {
        errorResponse('Token invalid', 401);
    }
    
    return $user;
}

// معالج الأخطاء المخصص
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $error = "Error: [$errno] $errstr in $errfile on line $errline";
    logError($error);
    
    if (APP_DEBUG) {
        echo $error;
    }
}

// معالج الاستثناءات المخصص
function customExceptionHandler($exception) {
    $error = "Uncaught exception: " . $exception->getMessage() . 
             " in " . $exception->getFile() . 
             " on line " . $exception->getLine();
    logError($error);
    
    if (APP_DEBUG) {
        echo $error;
    } else {
        echo "حدث خطأ في النظام";
    }
}

// تعيين معالجات الأخطاء
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');

// وظائف للتحقق من الصلاحيات
function isAdmin() {
    return isset($_SESSION['admin_id']);
}

function isUser() {
    return isset($_SESSION['user_id']);
}

function getCurrentUserId() {
    if (isset($_SESSION['user_id'])) {
        return $_SESSION['user_id'];
    }
    return null;
}

function getCurrentAdminId() {
    if (isset($_SESSION['admin_id'])) {
        return $_SESSION['admin_id'];
    }
    return null;
}
