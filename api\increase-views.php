<?php
/**
 * زيادة مشاهدات الإعلان
 * Increase Ad Views API
 */

require_once '../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

// التحقق من المصادقة
$user = requireAuth('user');

$data = getRequestData();

// التحقق من البيانات المطلوبة
$requiredFields = ['ad_id'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$adId = $data['ad_id'];

// التحقق من وجود الإعلان
$adSql = "SELECT id, views FROM ads WHERE id = ? AND active = 1";
$ad = $db->selectOne($adSql, [$adId]);

if (!$ad) {
    errorResponse('الإعلان غير موجود', 404);
}

// زيادة عدد المشاهدات
$updateSql = "UPDATE ads SET views = views + 1 WHERE id = ?";
$result = $db->update($updateSql, [$adId]);

if ($result) {
    $newViews = $ad['views'] + 1;
    
    successResponse([
        'ad_id' => $adId,
        'views' => $newViews
    ], 'تم تسجيل المشاهدة بنجاح');
} else {
    errorResponse('حدث خطأ أثناء تسجيل المشاهدة');
}
