<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم مطر</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 5px 0;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .bg-primary-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .bg-success-gradient {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .bg-warning-gradient {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .bg-info-gradient {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #667eea !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <?php
    require_once '../includes/init.php';
    
    // التحقق من تسجيل الدخول
    if (!isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // الحصول على الإحصائيات
    $usersCount = $db->selectOne("SELECT COUNT(*) as count FROM users")['count'];
    $outlooksCount = $db->selectOne("SELECT COUNT(*) as count FROM outlooks")['count'];
    $weatherShotsCount = $db->selectOne("SELECT COUNT(*) as count FROM weather_shots")['count'];
    $subscriptionsCount = $db->selectOne("SELECT COUNT(*) as count FROM subscriptions WHERE active = 1")['count'];
    
    // الحصول على أحدث المستخدمين
    $recentUsers = $db->select("SELECT * FROM users ORDER BY id DESC LIMIT 5");
    
    // الحصول على أكثر المنشورات إعجاباً
    $topPosts = $db->select("SELECT * FROM outlooks ORDER BY likes DESC LIMIT 5");
    ?>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="p-3">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-cloud-rain me-2"></i>
                        مطر
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-2"></i>
                            الرئيسية
                        </a>
                        <a class="nav-link" href="outlooks.php">
                            <i class="fas fa-cloud-sun me-2"></i>
                            التوقعات الجوية
                        </a>
                        <a class="nav-link" href="weather-shots.php">
                            <i class="fas fa-camera me-2"></i>
                            صور الطقس
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>
                            المستخدمين
                        </a>
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                        </a>
                        <a class="nav-link" href="ads.php">
                            <i class="fas fa-ad me-2"></i>
                            الإعلانات
                        </a>
                        <a class="nav-link" href="coupons.php">
                            <i class="fas fa-ticket-alt me-2"></i>
                            الكوبونات
                        </a>
                        <a class="nav-link" href="marketers.php">
                            <i class="fas fa-user-tie me-2"></i>
                            المسوقين
                        </a>
                        <a class="nav-link" href="subscriptions.php">
                            <i class="fas fa-credit-card me-2"></i>
                            الاشتراكات
                        </a>
                        <a class="nav-link" href="support.php">
                            <i class="fas fa-headset me-2"></i>
                            الدعم الفني
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                        <hr class="my-3">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>لوحة التحكم</h2>
                    <div>
                        <span class="text-muted">مرحباً، <?php echo $_SESSION['admin_name']; ?></span>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-primary-gradient me-3">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($usersCount); ?></h3>
                                    <p class="text-muted mb-0">المستخدمين</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-success-gradient me-3">
                                    <i class="fas fa-cloud-sun"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($outlooksCount); ?></h3>
                                    <p class="text-muted mb-0">التوقعات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-warning-gradient me-3">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($weatherShotsCount); ?></h3>
                                    <p class="text-muted mb-0">صور الطقس</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-info-gradient me-3">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div>
                                    <h3 class="mb-0"><?php echo number_format($subscriptionsCount); ?></h3>
                                    <p class="text-muted mb-0">الاشتراكات النشطة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">أحدث المستخدمين</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>الاسم</th>
                                                <th>البريد الإلكتروني</th>
                                                <th>تاريخ التسجيل</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentUsers as $user): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($user['name']); ?></td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td><?php echo formatArabicDate($user['date']); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">أكثر المنشورات إعجاباً</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>العنوان</th>
                                                <th>البلد</th>
                                                <th>الإعجابات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($topPosts as $post): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars(substr($post['title'] ?: $post['details'], 0, 30)) . '...'; ?></td>
                                                <td><?php echo htmlspecialchars($post['country']); ?></td>
                                                <td><span class="badge bg-primary"><?php echo $post['likes']; ?></span></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
