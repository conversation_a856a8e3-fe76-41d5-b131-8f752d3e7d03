<?php
echo "<h1>اختبار قاعدة البيانات</h1>";

try {
    $host = 'localhost';
    $dbname = 'rainapp4_matar';
    $username = 'root';
    $password = '';
    
    echo "محاولة الاتصال بقاعدة البيانات...<br>";
    
    $dsn = "mysql:host={$host};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    ]);
    
    echo "✅ اتصال MySQL نجح<br>";
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$dbname}'");
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "✅ قاعدة البيانات {$dbname} موجودة<br>";
        
        // الاتصال بقاعدة البيانات
        $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ]);
        
        echo "✅ اتصال قاعدة البيانات نجح<br>";
        
        // عرض الجداول
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "عدد الجداول: " . count($tables) . "<br>";
        
        if (count($tables) > 0) {
            echo "الجداول الموجودة:<br>";
            foreach ($tables as $table) {
                echo "- {$table}<br>";
            }
        }
        
    } else {
        echo "❌ قاعدة البيانات {$dbname} غير موجودة<br>";
        echo "يجب إنشاء قاعدة البيانات واستيراد ملف SQL<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

echo "<br><a href='simple_test.php'>اختبار PHP</a> | <a href='test.php'>اختبار شامل</a>";
?>
