-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.9.7
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Feb 16, 2023 at 04:29 PM
-- Server version: 5.7.23-23
-- PHP Version: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `rainapp4_matar`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `pic` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `ban` int(10) UNSIGNED NOT NULL,
  `register_date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `name`, `email`, `password`, `country`, `role`, `token`, `pic`, `ban`, `register_date`) VALUES
(7, 'Admin', '<EMAIL>', '$2y$10$qzyPfuVM61okMBVSc1vBHe9KAjUPsjnrkHGoVqpLfrAR4sJTRbd.C', 'السعودية', '1', '8b579e5d273a7b68e8e6bcac3a7aeef5', 'default.jpg', 0, '2022-11-08'),
(8, 'احمد', '<EMAIL>', '$2y$10$IeYzk/jwmfOlXDFKYeKyS.dwTjZPqGynjnKQF2Ak/mPwEQzvIadmq', 'سلطنة عمان', '1', '4624a80344a97c0808af14af364fff25', 'default.jpg', 0, '2022-11-08'),
(9, 'ahmed', '<EMAIL>', '$2y$10$LIxvD/iOViIwDYyyLPjvLeo4FQ9A7Tw9CK1e/xJ3kX33p.zn8wTzW', 'سلطنة عمان', '1', 'caeef6009eedf7c221259cb8624f6bf3', 'default.jpg', 0, '2022-11-08'),
(10, 'test', '<EMAIL>', '$2y$10$mbekSt34muT2WC5ieYzNU.j5WXL33aRpHXciZGWVfgxFIsmISoajK', 'أنتيغوا وباربودا', '1', '6456c5afe6ca394b55f490e881d30040', 'default.jpg', 0, '2022-11-17'),
(11, 'admin', '<EMAIL>', '$2y$10$glkD60lP2i/o0hXzup86peIJOhPy5H9MiHeviUbm2kIOonkJ5O0LG', 'السعودية', '1', 'da8c09e71dbf7c840930b2fa588cb5d2', 'default.jpg', 0, '2023-01-17');

-- --------------------------------------------------------

--
-- Table structure for table `ads`
--

CREATE TABLE `ads` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `redirect` text COLLATE utf8mb4_unicode_ci,
  `hide` timestamp NULL DEFAULT NULL,
  `location` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `media` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ads_details`
--

CREATE TABLE `ads_details` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ad_id` int(10) UNSIGNED NOT NULL,
  `country` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `count` int(10) UNSIGNED DEFAULT NULL,
  `type` text COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `change_password`
--

CREATE TABLE `change_password` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user` text COLLATE utf8mb4_unicode_ci,
  `marketer` text COLLATE utf8mb4_unicode_ci,
  `token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `expire_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `change_password`
--

INSERT INTO `change_password` (`id`, `user`, `marketer`, `token`, `code`, `expire_time`) VALUES
(1, '<EMAIL>', NULL, 'a4bc7aa9d79c5138b0b9052bf0185410', 'ZyAzI', '2022-09-25 22:26:00'),
(11, NULL, '<EMAIL>', '1539e5d5bc05a9732de4e9cd4da04ae2', 'TixUL', '2022-06-17 19:41:00'),
(12, NULL, '<EMAIL>', 'c1049226bf728629b03ce1585d424b9e', 'nyMkg', '2022-06-18 01:31:00'),
(13, NULL, '<EMAIL>', 'b1a7e4b00a4b35f86dbce009578a464f', 'a5SIL', '2022-06-18 01:32:00'),
(16, NULL, '<EMAIL>', 'c65863b3547c4fe84fac9a0260709a4e', 'ChVTS', '2022-06-18 13:43:00'),
(18, NULL, '<EMAIL>', '9d26cd7ca3162fef2702f0d1c0d5ed51', '6rg1O', '2022-06-18 13:50:00'),
(19, 'text', NULL, 'e57f8e0a43fd688a33b5e51ce12e924d', 'aAFlX', '2023-01-11 06:15:00'),
(21, 'qw', NULL, '488bd0250f37f3500429d7f1d3dfeb6f', '5fRP3', '2023-02-02 01:01:00'),
(23, '<EMAIL>', NULL, '24c9e9ccc8cb812104acf967e218d76a', 'K1Ykg', '2023-02-15 22:44:00');

-- --------------------------------------------------------

--
-- Table structure for table `countries`
--

CREATE TABLE `countries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `country` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `countries`
--

INSERT INTO `countries` (`id`, `country`, `icon`) VALUES
(51, 'المغرب العربي', 'المغرب العربي.png'),
(52, 'مصر والسودان', 'مصر والسودان.png'),
(53, 'العراق وبلاد الشام', 'العراق وبلاد الشام.png'),
(54, 'اليمن', 'اليمن.png'),
(55, 'الكويت', 'الكويت.png'),
(56, 'البحرين', 'البحرين.png'),
(57, 'قطر', 'قطر.png'),
(58, 'السعودية', 'السعودية.png'),
(59, 'الأمارات', 'الأمارات.png'),
(60, 'عمان', 'عمان.png');

-- --------------------------------------------------------

--
-- Table structure for table `coupons`
--

CREATE TABLE `coupons` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `coupon` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` text COLLATE utf8mb4_unicode_ci,
  `days` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `expire_date` date DEFAULT NULL,
  `active` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `coupons`
--

INSERT INTO `coupons` (`id`, `coupon`, `country`, `days`, `expire_date`, `active`) VALUES
(36, 'هلا', 'عمان', '6', '2023-02-25', 1),
(37, 'hhhh', 'عمان', '8', '2023-02-18', 1),
(38, 'nnnn', 'عمان', '9', '2023-02-25', 0);

-- --------------------------------------------------------

--
-- Table structure for table `marketers`
--

CREATE TABLE `marketers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `coupon` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` text COLLATE utf8mb4_unicode_ci,
  `address` text COLLATE utf8mb4_unicode_ci,
  `country` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `coupon_expire` text COLLATE utf8mb4_unicode_ci,
  `reg_commission` float UNSIGNED DEFAULT NULL,
  `sub_commission` float UNSIGNED DEFAULT NULL,
  `facebook_acc` text COLLATE utf8mb4_unicode_ci,
  `twitter_acc` text COLLATE utf8mb4_unicode_ci,
  `instagram_acc` text COLLATE utf8mb4_unicode_ci,
  `tiktok_acc` text COLLATE utf8mb4_unicode_ci,
  `snapchat_acc` text COLLATE utf8mb4_unicode_ci,
  `token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `pic` text COLLATE utf8mb4_unicode_ci,
  `ban` int(10) UNSIGNED DEFAULT NULL,
  `active` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `marketers`
--

INSERT INTO `marketers` (`id`, `coupon`, `full_name`, `email`, `phone`, `address`, `country`, `password`, `coupon_expire`, `reg_commission`, `sub_commission`, `facebook_acc`, `twitter_acc`, `instagram_acc`, `tiktok_acc`, `snapchat_acc`, `token`, `pic`, `ban`, `active`) VALUES
(16, '5f8DJvf', 'احمد', '<EMAIL>', '9999999', 'صحار', 'سلطنة عمان', '$2y$10$Uj5DxHoTifsTWNbUDjoAx.qHB5JRyD7lwTHqi3u9kJZa5hnhpeULm', NULL, 3, 3.5, NULL, NULL, NULL, NULL, NULL, 'b7d9ac677c951aba27bfe572b4b96e8e', NULL, 0, 1),
(17, 'HVCMvsV', 'محمم', '<EMAIL>', '01062004857', 'jjjjjjjjjjjjjjjjjjjjjjjjk', 'باربادوس', '$2y$10$pfZtUgOG3KZSibPb55.RVeMXlkwvmpNrD8Qxdoa.NToWLz3hyEg06', NULL, 1, 2, NULL, NULL, NULL, NULL, NULL, '7b23a51a30420c82e95b16bdec76ba05', NULL, 0, 1),
(18, 'sCBFHRP', 'احمد', '<EMAIL>', NULL, NULL, 'أنتيغوا وباربودا', '$2y$10$A38MYm0PYtDOX3vd6i/qe.PFbYr7xPOVdT69FbTQyNQY6NRttVbLu', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '7dd86ed9ea3e54f9d8a99d54e360e210', NULL, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `marketer_details`
--

CREATE TABLE `marketer_details` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `marketer_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `name` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` float UNSIGNED NOT NULL,
  `date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `marketer_details`
--

INSERT INTO `marketer_details` (`id`, `marketer_id`, `user_id`, `name`, `email`, `country`, `type`, `amount`, `date`) VALUES
(1, 17, 34, 'احمد الشبي', '<EMAIL>', 'عمان', 'register', 1, '2022-10-04');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(8, '2022_05_29_232712_create_admins_table', 1),
(21, '2022_05_28_164006_create_users_table', 2),
(22, '2022_05_28_233925_create_outlooks_table', 2),
(23, '2022_05_29_003115_create_outlooks_files_table', 2),
(24, '2022_05_29_132521_create_countries_table', 2),
(25, '2022_05_29_191751_create_outlooks_likes_table', 2),
(26, '2022_05_29_191853_create_outlooks_comments_table', 2),
(27, '2022_05_29_195451_create_outlooks_shares_table', 2),
(28, '2022_05_30_173911_create_weather_shots_table', 2),
(29, '2022_05_30_234026_create_pending_shots_table', 2),
(30, '2022_05_31_181757_create_notifications_table', 2),
(31, '2022_05_31_230941_create_coupons_table', 2),
(32, '2022_06_01_010325_create_used_coupons_table', 2),
(33, '2022_06_01_235215_create_marketers_table', 2),
(34, '2022_06_02_225939_create_support_tickets_table', 2),
(35, '2022_06_02_234414_create_ads_table', 2),
(36, '2022_06_02_234627_create_ads_details_table', 2),
(37, '2022_06_03_191622_create_marketer_details_table', 2),
(38, '2022_06_04_185019_create_subscriptions_table', 2),
(39, '2022_06_04_230836_create_system_settings_table', 2),
(40, '2022_06_11_154157_create_withdrawals_table', 3),
(41, '2022_06_11_154423_create_payout_settings_table', 3),
(42, '2022_06_13_021719_create_change_password_table', 4);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `subject` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` timestamp NULL DEFAULT NULL,
  `country` text COLLATE utf8mb4_unicode_ci,
  `appearance_for` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `appearance_as` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `redirect` text COLLATE utf8mb4_unicode_ci,
  `schedule` text COLLATE utf8mb4_unicode_ci,
  `media` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `outlooks`
--

CREATE TABLE `outlooks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` text COLLATE utf8mb4_unicode_ci,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `country` text COLLATE utf8mb4_unicode_ci,
  `details` longtext COLLATE utf8mb4_unicode_ci,
  `schedule` text COLLATE utf8mb4_unicode_ci,
  `hide` text COLLATE utf8mb4_unicode_ci,
  `likes` int(10) UNSIGNED DEFAULT NULL,
  `shares` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `outlooks`
--

INSERT INTO `outlooks` (`id`, `title`, `date`, `country`, `details`, `schedule`, `hide`, `likes`, `shares`) VALUES
(179, 'توقعات النموذج الاوروبي', '2023-01-18 18:00:00', 'المغرب العربي', 'توقعات النموذج الاوروبي يشير لفرص الامطار على اجزاء دول المغرب العربي تشمل ليبيا والمغرب وتونس والجزائر خلال ال10 القادمة', '', '', 0, 0),
(180, 'توقعات النموذج الاوروبي يشير لفرص الامطار على اجزاء دول المغرب العربي تشمل ليبيا والمغرب وتونس والجزائر خلال ال10 القادمة', '2023-01-18 18:00:00', '', 'توقعات النموذنمممج الاوروبي يشير لفرص الامطار على اجزاء دول المغرب العربي تشمل ليبيا والمغرب وتونس والجزائر خلال ال10 القادمة', '', '', 0, 0),
(181, '', '2023-01-18 18:00:00', 'مصر والسودان', ' المغرب العربي ', '', '', 0, 0),
(182, 'تجربة', '2023-01-30 08:29:00', 'العراق وبلاد الشام', 'تجربة', '', '', 1, 0),
(187, '', '2023-01-31 22:22:33', 'عمان', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 1, 0),
(188, '', '2023-01-21 00:00:00', 'الأمارات', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 0, 0),
(189, '', '2023-01-21 00:00:00', 'السعودية', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 0, 0),
(190, '', '2023-01-21 00:00:00', 'قطر', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '2023-01-20 18:00:0', '', 0, 0),
(191, '', '2023-01-21 00:00:00', 'البحرين', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 0, 0),
(192, '', '2023-01-21 00:00:00', 'الكويت', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 0, 0),
(193, '', '2023-01-21 00:00:00', 'اليمن', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 0, 0),
(194, '', '2023-01-21 08:02:00', 'عمان', 'توقعاااات', '', '', 0, 0),
(195, 'توقعات الطقس', '2023-01-22 01:15:00', 'عمان', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 0, 0),
(196, 'توقعات الطقس', '2023-01-29 13:09:34', 'عمان', 'يتواجد المنخفض حاليا فوق افغانستان ، تأثيراته طالت اجزاء من شمال السلطنة والامارات خلال 48 ساعه الماضية بأمطار متفرقه ، يستمر بتأثيراته مع تعمقه التدريجي لشبه الجزيرة العربية ، وتأثيره الرئيسي سيبدأ مع منتصف الاسبوع القادم', '', '', 1, 0),
(197, 'امطار', '2023-01-29 13:09:15', 'عمان', 'فيديو امطار', '', '', 1, 0),
(198, 'حالة الطقس المتوقعة ليوم الأحد في المملكة', '2023-02-01 14:01:53', 'عمان', 'توقعات', '2023-01-29 20:35:0', '', 1, 0),
(199, 'ii', '2023-02-01 14:01:51', 'عمان', 'امطاااااار', '2023-01-31 15:10:0', '', 2, 0),
(201, 'حرارة', '2023-02-07 21:35:17', 'عمان', 'ارتفاع بدرجات الحرارة', '2023-02-05 06:52:0', '', 2, 0),
(202, 'امطار', '2023-02-16 07:47:00', 'عمان', 'امطاااااااار غزيرة جدا ', '', '', 0, 0),
(203, 'امطار', '2023-02-16 07:47:00', 'الأمارات', 'امطاااااااار غزيرة جدا ', '', '', 0, 0),
(204, 'امطار', '2023-02-16 07:47:00', 'السعودية', 'امطاااااااار غزيرة جدا ', '', '', 0, 0),
(205, 'امطار', '2023-02-16 07:50:00', 'عمان', 'امطااااااااار غزيرة ', '2023-02-16 01:53:0', '', 0, 0),
(206, 'هلا', '2023-02-16 07:57:00', 'عمان', 'تجرررربه', '2023-02-16 02:00:0', '', 0, 0),
(207, 'هلا', '2023-02-16 07:57:00', 'الأمارات', 'تجرررربه', '2023-02-16 02:18:0', NULL, 0, 0),
(209, 'هلا', '2023-02-16 08:14:00', 'عمان', 'تجرررررررربة', '2023-02-16 02:16:0', '', 0, 0),
(210, 'هلا', '2023-02-16 08:14:00', 'الأمارات', 'تجرررررررربة', '2023-02-16 02:16:0', '', 0, 0),
(211, 'هلا', '2023-02-16 18:00:00', 'عمان', 'توقعاااااااااات', '2023-02-16 14:44:0', '', 0, 0),
(212, 'هلا', '2023-02-16 18:00:00', 'عمان', 'امطاااااااااار تجربة', '2023-02-16 14:48:0', '', 0, 0),
(213, 'هلا', '2023-02-16 18:00:00', 'الأمارات', 'امطاااااااااار تجربة', '2023-02-16 14:48:0', '', 0, 0),
(214, 'هلا', '2023-02-16 18:00:00', 'السعودية', 'امطاااااااااار تجربة', '2023-02-16 14:49:0', NULL, 0, 0),
(215, 'هلا', '2023-02-16 18:00:00', 'قطر', 'امطاااااااااار تجربة', '2023-02-16 14:48:0', '', 0, 0),
(216, 'هلا', '2023-02-16 18:00:00', 'عمان', 'توقعععععات', '', '', 0, 0),
(220, '', '2023-02-17 18:00:00', 'عمان', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(221, '', '2023-02-17 18:00:00', 'الأمارات', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(222, '', '2023-02-17 18:00:00', 'السعودية', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(223, '', '2023-02-17 18:00:00', 'قطر', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(224, '', '2023-02-17 18:00:00', 'البحرين', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(225, '', '2023-02-17 18:00:00', 'الكويت', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(226, '', '2023-02-17 18:00:00', 'اليمن', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(227, '', '2023-02-17 18:00:00', 'العراق وبلاد الشام', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(228, '', '2023-02-17 18:00:00', 'مصر والسودان', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(229, '', '2023-02-17 18:00:00', 'المغرب العربي', 'توقعات الامطاااااااااااااااااااااااااااااااااااااااار', '', '', 0, 0),
(230, '', '2023-02-17 18:00:00', 'عمان', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(231, '', '2023-02-17 18:00:00', 'الأمارات', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(232, '', '2023-02-17 18:00:00', 'السعودية', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(233, '', '2023-02-17 18:00:00', 'قطر', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(234, '', '2023-02-17 18:00:00', 'البحرين', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(235, '', '2023-02-17 18:00:00', 'الكويت', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(236, '', '2023-02-17 18:00:00', 'اليمن', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(237, '', '2023-02-17 18:00:00', 'العراق وبلاد الشام', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(238, '', '2023-02-17 18:00:00', 'مصر والسودان', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(239, '', '2023-02-17 18:00:00', 'المغرب العربي', 'تجررررررررررررربهههههههههههه', '2023-02-17 00:52:0', '', 0, 0),
(240, '', '2023-02-17 18:00:00', 'عمان', 'امطاااااااااااااااااار تجربة ', '2023-02-17 00:56:0', '', 0, 0),
(241, '', '2023-02-17 18:00:00', 'الأمارات', 'امطاااااااااااااااااار تجربة ', '2023-02-17 00:56:0', '', 0, 0),
(242, '', '2023-02-17 18:00:00', 'السعودية', 'امطاااااااااااااااااار تجربة ', '2023-02-17 00:56:0', '', 0, 0),
(243, '', '2023-02-17 18:00:00', 'قطر', 'امطاااااااااااااااااار تجربة ', '2023-02-17 00:56:0', '', 0, 0),
(244, '', '2023-02-17 18:00:00', 'البحرين', 'امطاااااااااااااااااار تجربة ', '2023-02-17 00:56:0', '', 0, 0),
(249, 'هلا', '2023-02-17 18:00:00', 'عمان', 'امطاااااااااااار', '2023-02-17 01:26:0', '', 0, 0),
(250, 'توقعات', '2023-02-17 18:00:00', 'عمان', 'توقعااااااااات', '2023-02-18 00:10:0', '', 0, 0),
(251, 'هلا', '2023-02-17 18:00:00', 'عمان', 'توقعاااااااااااااااااااااااااااااااااااااات', '', '', 0, 0),
(252, 'هلا', '2023-02-17 18:00:00', 'الأمارات', 'توقعاااااااااااااااااااااااااااااااااااااات', '', '', 0, 0),
(253, 'هلا', '2023-02-17 18:00:00', 'السعودية', 'توقعاااااااااااااااااااااااااااااااااااااات', '', '', 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `outlooks_comments`
--

CREATE TABLE `outlooks_comments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `outlook_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `comment` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `reply` text COLLATE utf8mb4_unicode_ci,
  `date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `outlooks_comments`
--

INSERT INTO `outlooks_comments` (`id`, `outlook_id`, `user_id`, `comment`, `reply`, `date`) VALUES
(162, 179, 64, 'رائع', 'شكرا', '2023-01-18'),
(163, 197, 64, 'هلا', NULL, '2023-01-22'),
(164, 196, 65, 'هلا', 'السلام عليكم السلام عليكم السلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكمالسلام عليكم', '2023-02-01'),
(167, 199, 65, 'رائع', NULL, '2023-02-01'),
(168, 199, 64, 'جميل', 'هلا', '2023-02-01'),
(169, 199, 61, 'هلا', 'هلا', '2023-02-01'),
(170, 197, 65, 'هلا', NULL, '2023-02-01'),
(171, 199, 68, 'nn', 'ننن', '2023-02-05'),
(172, 199, 68, 'nn', 'ممم', '2023-02-05'),
(173, 199, 68, 'nn', NULL, '2023-02-05'),
(174, 199, 68, 'nn', NULL, '2023-02-05'),
(175, 199, 65, 'hi', 'السلام عليكم ورحمة الله', '2023-02-05'),
(176, 199, 65, 'جميل', 'السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة اللالسلام عليكم ورحمة الله ه', '2023-02-05'),
(177, 201, 65, 'هلا', 'السلام عليكم ورحمة الله السلام عليكم ورحمة الله  السلام عليكم ورحمة الله  السلام عليكم ورحمة الله  السلام عليكم ورحمة الله  السلام عليكم ورحمة الله  السلام عليكم ورحمة الله', '2023-02-05'),
(178, 201, 65, 'هلا', 'السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته', '2023-02-05'),
(179, 201, 64, 'هلا', 'السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته السلام عليكم ورحمة الله وبركاته', '2023-02-05'),
(180, 201, 69, 'رائع جدا', 'https://d.top4top.io/p_2592fdb3j0.jpg', '2023-02-05'),
(181, 201, 70, 'هلا', 'السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله السلام عليكم ورحمة الله', '2023-02-14');

-- --------------------------------------------------------

--
-- Table structure for table `outlooks_files`
--

CREATE TABLE `outlooks_files` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `outlook_id` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `file` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `outlooks_files`
--

INSERT INTO `outlooks_files` (`id`, `outlook_id`, `file`) VALUES
(274, '179', '63c717cd46607.jpg'),
(275, '180', '63c7180bb4cf1.jpg'),
(276, '181', '63c71bb5c0efe.jpg'),
(277, '182', '63c71f1728bf5.jpg'),
(278, '182', '63c71f172a2a0.jpg'),
(279, '182', '63c71f172a975.jpg'),
(284, '187', '63caa607b5fcf.png'),
(285, '188', '63caa6fc1d80d.png'),
(286, '189', '63caa7591d542.png'),
(287, '190', '63caa787044da.png'),
(288, '191', '63caa7a9da2cf.png'),
(289, '192', '63caa7cc2022a.png'),
(290, '193', '63caa7ed63742.png'),
(291, '194', '63cb0f87ec230.jpg'),
(292, '195', '63cbfdd733891.jpeg'),
(293, '195', '63cbfdd739672.jpg'),
(294, '195', '63cbfdd73a29a.jpg'),
(295, '195', '63cbfdd73b2a9.png'),
(296, '196', '63cbffa79017c.png'),
(297, '196', '63cbffa790dd5.mp4'),
(298, '196', '63cbffa796af0.mp4'),
(299, '197', '63cc0f70d2518.mp4'),
(300, '198', '63d69dc42f2f7.jpeg'),
(301, '199', '63d8f53878e63.jpeg'),
(302, '199', '63d8f538ad6ec.jpeg'),
(303, '201', '63df1913dd8a6.jpg'),
(304, '201', '63df1913de2be.jpg'),
(305, '202', '63ed531770f06.jpg'),
(306, '202', '63ed531773eb4.jpg'),
(307, '202', '63ed53177448f.jpg'),
(308, '203', '63ed531774cbb.jpg'),
(309, '203', '63ed53177519f.jpg'),
(310, '203', '63ed5317755ce.jpg'),
(311, '204', '63ed531775e7e.jpg'),
(312, '204', '63ed531776329.jpg'),
(313, '204', '63ed531776775.jpg'),
(314, '205', '63ed53d9bc04f.jpg'),
(315, '206', '63ed557d9d966.jpg'),
(316, '206', '63ed557d9fce5.jpg'),
(317, '207', '63ed557da0754.jpg'),
(318, '207', '63ed557da1bc3.jpg'),
(321, '209', '63ed595000977.jpg'),
(322, '210', '63ed595003baf.jpg'),
(323, '211', '63ee08ac6711f.jpeg'),
(324, '212', '63ee09a23f729.jpeg'),
(325, '213', '63ee09a2403e8.jpeg'),
(326, '214', '63ee09a240bcb.jpeg'),
(327, '215', '63ee09a241366.jpeg'),
(328, '216', '63ee17248f3c9.jpg'),
(332, '220', '63ee96b344298.jpg'),
(333, '221', '63ee96b34898e.jpg'),
(334, '222', '63ee96b3496ec.jpg'),
(335, '223', '63ee96b34a561.jpg'),
(336, '224', '63ee96b34b62d.jpg'),
(337, '225', '63ee96b35129d.jpg'),
(338, '226', '63ee96b3522a1.jpg'),
(339, '227', '63ee96b352ea9.jpg'),
(340, '228', '63ee96b353a66.jpg'),
(341, '229', '63ee96b3546e7.jpg'),
(342, '230', '63ee97b1c68da.mp4'),
(343, '231', '63ee97b1ccd5f.mp4'),
(344, '232', '63ee97b1d3028.mp4'),
(345, '233', '63ee97b1d9629.mp4'),
(346, '234', '63ee97b1df658.mp4'),
(347, '235', '63ee97b1e54d2.mp4'),
(348, '236', '63ee97b1eb3c6.mp4'),
(349, '237', '63ee97b1f0e58.mp4'),
(350, '238', '63ee97b202957.mp4'),
(351, '239', '63ee97b208431.mp4'),
(353, '241', '63ee97f6e7f12.jpeg'),
(354, '242', '63ee97f6e86fb.jpeg'),
(355, '243', '63ee97f6e8ef7.jpeg'),
(356, '244', '63ee97f6e96f2.jpeg'),
(364, '240', '63ee9eea2246b.mp4'),
(368, '249', '63eea04843990.jpg'),
(369, '249', '63eea048443a0.jpg'),
(370, '250', '63eea0ea3cff4.jpg'),
(371, '251', '63eea15c6180e.jpg'),
(372, '252', '63eea15c62410.jpg'),
(373, '253', '63eea15c62bfa.jpg');

-- --------------------------------------------------------

--
-- Table structure for table `outlooks_likes`
--

CREATE TABLE `outlooks_likes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `outlook_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `outlooks_likes`
--

INSERT INTO `outlooks_likes` (`id`, `outlook_id`, `user_id`) VALUES
(1, 40, 3),
(7, 197, 61),
(8, 196, 61),
(9, 182, 61),
(10, 187, 57),
(12, 199, 57),
(14, 199, 65),
(15, 198, 65),
(16, 201, 69),
(17, 201, 65);

-- --------------------------------------------------------

--
-- Table structure for table `outlooks_shares`
--

CREATE TABLE `outlooks_shares` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `outlook_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payout_settings`
--

CREATE TABLE `payout_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `marketer_id` int(10) UNSIGNED NOT NULL,
  `method` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `paypal_account` text COLLATE utf8mb4_unicode_ci,
  `bank_name` text COLLATE utf8mb4_unicode_ci,
  `bank_account` text COLLATE utf8mb4_unicode_ci,
  `bank_account_number` text COLLATE utf8mb4_unicode_ci,
  `bank_swift` text COLLATE utf8mb4_unicode_ci,
  `full_name` text COLLATE utf8mb4_unicode_ci,
  `id_number` text COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payout_settings`
--

INSERT INTO `payout_settings` (`id`, `marketer_id`, `method`, `paypal_account`, `bank_name`, `bank_account`, `bank_account_number`, `bank_swift`, `full_name`, `id_number`) VALUES
(1, 2, 'paypal', '<EMAIL>', NULL, NULL, NULL, NULL, NULL, NULL),
(2, 4, 'paypal', 'ahmed.alshibli@gmailcom', NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `pending_shots`
--

CREATE TABLE `pending_shots` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `photographer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `location` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `send_date` timestamp NULL DEFAULT NULL,
  `date` timestamp NULL DEFAULT NULL,
  `media` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `subscriptions`
--

CREATE TABLE `subscriptions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `amount` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_date` date NOT NULL,
  `expire_date` date NOT NULL,
  `marketer_name` text COLLATE utf8mb4_unicode_ci,
  `pay_method` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `active` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `support_tickets`
--

CREATE TABLE `support_tickets` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` text COLLATE utf8mb4_unicode_ci,
  `email` text COLLATE utf8mb4_unicode_ci,
  `subject` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` date DEFAULT NULL,
  `active` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `support_tickets`
--

INSERT INTO `support_tickets` (`id`, `user_id`, `email`, `subject`, `content`, `date`, `active`) VALUES
(1163, NULL, '<EMAIL>', 'اختبار الفورم 2', 'تست تست', '2023-01-23', 0),
(1164, NULL, '<EMAIL>', 'There they go…', 'Hey, my name’s Eric and for just a second, imagine this…\r\n\r\n- Someone does a search and winds up at rain-app.com.\r\n\r\n- They hang out for a minute to check it out.  “I’m interested… but… maybe…”\r\n\r\n- And then they hit the back button and check out the other search results instead. \r\n\r\n- Bottom line – you got an eyeball, but nothing else to show for it.\r\n\r\n- There they go.\r\n\r\nThis isn’t really your fault – it happens a LOT – studies show 7 out of 10 visitors to any site disappear without leaving a trace.\r\n\r\nBut you CAN fix that.\r\n\r\nTalk With Web Visitor is a software widget that’s works on your site, ready to capture any visitor’s Name, Email address and Phone Number.  It lets you know right then and there – enabling you to call that lead while they’re literally looking over your site.\r\n\r\nCLICK HERE https://boostleadgeneration.com to try out a Live Demo with Talk With Web Visitor now to see exactly how it works.\r\n\r\nTime is money when it comes to connecting with leads – the difference between contacting someone within 5 minutes versus 30 minutes later can be huge – like 100 times better!\r\n\r\nPlus, now that you have their phone number, with our new SMS Text With Lead feature you can automatically start a text (SMS) conversation… so even if you don’t close a deal then, you can follow up with text messages for new offers, content links, even just “how you doing?” notes to build a relationship.\r\n\r\nStrong stuff.\r\n\r\nCLICK HERE https://boostleadgeneration.com to discover what Talk With Web Visitor can do for your business.\r\n\r\nYou could be converting up to 100X more leads today!\r\n\r\nEric\r\nPS: Talk With Web Visitor offers a FREE 14 days trial – and it even includes International Long Distance Calling. \r\nYou have customers waiting to talk with you right now… don’t keep them waiting. \r\nCLICK HERE https://boostleadgeneration.com to try Talk With Web Visitor now.\r\n\r\nIf you\'d like to unsubscribe click here http://boostleadgeneration.com/unsubscribe.aspx?d=rain-app.com', '2023-01-24', 0),
(1165, NULL, '<EMAIL>', 'Improve local visibility for rain-app.com', 'If you have a local business and want to rank it on google maps in a specific area then this service is for you. \r\n \r\nGoogle Map Stacking is one of the best ways to rank your GMB in a specific mile radius. \r\n \r\nMore info: \r\nhttps://www.speed-seo.net/product/google-maps-pointers/ \r\n \r\nThanks and Regards \r\nMike Jenkin\r\n \r\n \r\nPS: Want an all in one Local Plan that includes everything? \r\nhttps://www.speed-seo.net/product/local-seo-package/', '2023-01-27', 0),
(1166, NULL, '<EMAIL>', 'Monthly SEO plans from 49$ ONLY', 'Hi there \r\n \r\nI have just analyzed  rain-app.com for its SEO Trend and saw that your website could use a boost. \r\n \r\nWe will increase your SEO metrics and ranks organically and safely, using only whitehat methods, while providing monthly reports and outstanding support. \r\n \r\nPlease check our pricelist here, we offer SEO at cheap rates. \r\nhttps://www.hilkom-digital.de/cheap-seo-packages/ \r\n \r\nRegards \r\nMike Calhoun\r\n \r\n \r\nPS: Quality SEO content is included', '2023-02-01', 0),
(1167, NULL, NULL, '', 'السلام السلام عليكمالسلام عليكالالسلام عليكمسلام عليكمم عليكالالسلام عليكمسلام عليكمم', '2023-02-01', 0),
(1168, NULL, '<EMAIL>', 'Content Marketing to rank rain-app.com', 'Hi there \r\n \r\nI Just checked your rain-app.com ranks and saw that your site is trending down for some time. \r\n \r\nIf you are looking for a trend reversal, we have the right solution for you \r\n \r\nWe are offering affordable Content Marketing plans with humanly written SEO content \r\n \r\nFor more information, please check our offers \r\nhttps://www.digital-x-press.com/product/content-marketing/ \r\n \r\nThanks and regards \r\nMike Lewis', '2023-02-06', 0),
(1169, NULL, NULL, '', 'هلا', '2023-02-16', 0),
(1170, NULL, NULL, '', 'هلا', '2023-02-16', 0),
(1171, NULL, NULL, '', 'هلاههتاا', '2023-02-16', 0);

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `satellite_link` text COLLATE utf8mb4_unicode_ci,
  `affiliate_mode` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `satellite_link`, `affiliate_mode`) VALUES
(1, 'https://www.meteoblue.com/ar/weather/maps/widget/v%c3%a4nsj%c3%b6_%d8%a7%d9%84%d8%b3%d9%88%d9%8a%d8%af_2665054?windAnimation=0&windAnimation=1&gust=0&gust=1&satellite=0&satellite=1&cloudsAndPrecipitation=0&cloudsAndPrecipitation=1&temperature=0&temperature=1&sunshine=0&sunshine=1&extremeForecastIndex=0&extremeForecastIndex=1&geoloc=fixed&tempunit=C&windunit=km%252Fh&lengthunit=metric&zoom=4&autowidth=auto#coords=3.1/21.34/46.03&map=satellite~sat~none~none~none', 1);

-- --------------------------------------------------------

--
-- Table structure for table `used_coupons`
--

CREATE TABLE `used_coupons` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `c_id` int(10) UNSIGNED NOT NULL,
  `u_id` int(10) UNSIGNED NOT NULL,
  `u_name` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_id` text COLLATE utf8mb4_unicode_ci,
  `date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `used_coupons`
--

INSERT INTO `used_coupons` (`id`, `c_id`, `u_id`, `u_name`, `device_id`, `date`) VALUES
(37, 10, 35, 'احمد', NULL, '2022-09-09'),
(38, 10, 34, 'احمد الشبلي', NULL, '2022-09-09'),
(59, 18, 40, 'طقس', NULL, '2022-09-13'),
(60, 18, 44, 'احمد الشبلي', NULL, '2022-09-13'),
(67, 37, 65, 'aa', NULL, '2023-02-16'),
(68, 38, 65, 'aa', NULL, '2023-02-16');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` text COLLATE utf8mb4_unicode_ci,
  `phone` text COLLATE utf8mb4_unicode_ci,
  `facebook_token` text COLLATE utf8mb4_unicode_ci,
  `google_token` text COLLATE utf8mb4_unicode_ci,
  `token` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` text COLLATE utf8mb4_unicode_ci,
  `pic` text COLLATE utf8mb4_unicode_ci,
  `date` date DEFAULT NULL,
  `coupon` text COLLATE utf8mb4_unicode_ci,
  `ban` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `password`, `country`, `phone`, `facebook_token`, `google_token`, `token`, `role`, `pic`, `date`, `coupon`, `ban`) VALUES
(50, 'ahmed', 'ahmed', '$2y$10$Hk.1QrUELhSUvEnUMhQPn.9Xjd5h1kVXj7PGnTa7Mzv1nlZ3NgmPq', 'عمان', NULL, NULL, NULL, '0ee0119d01ee85d9308a70c2e2fbef56', 'user', NULL, '2022-11-08', NULL, 0),
(51, 'tb', 'tb', '$2y$10$mnzfw.tTrfGs9CIX1YYVVeubKgVxGBZ7ETg6bgYQ9TVq4T6qaRM5i', 'oman', NULL, NULL, NULL, 'd2040d7b6de17189f5b50e4057b26f27', 'user', NULL, '2022-11-08', NULL, 0),
(52, 'احمد', '<EMAIL>', '$2y$10$nXQYfQUGiSfspdeF7Rb.WOIrHRjc0NxQrgkCl7MVD1mqk92cXFot6', NULL, NULL, NULL, 'jKSpq2x2GecslgZlT7iF8JVTjQX2', '2dfb487899cf4756f984a3e077e5423d', NULL, '2dfb487899cf4756f984a3e077e5423d.jpg', '2022-11-08', NULL, 0),
(53, 'tb', '1tb', '$2y$10$jgJPTw/jIHybIXXDlCUH7O.vfeEmAER6Y9Ee2aG33pqzXaq1CNMXy', 'tb', NULL, NULL, NULL, 'f6b4bd509c3f949218020d345d1d703c', 'user', NULL, '2022-11-08', NULL, 0),
(54, 'احمد', '<EMAIL>', '$2y$10$YCEdyGZItkFJ4Yn/BHDt5.rYaHUkXNz6YCWtmJiXpsJCZvuAk42ba', 'سلطنة عمان', NULL, NULL, NULL, '4624a80344a97c0808af14af364fff25', 'admin', NULL, '2022-11-08', NULL, 0),
(55, 'ahmed2', '<EMAIL>', '$2y$10$fEqYR35222U6J.LMiLy6pu.TNCPSsA8to6INS3X/ufwE4UQupagGG', 'سلطنة عمان', NULL, NULL, 'VyeYICl13KNaitZK5hCaVqUr48j2', 'caeef6009eedf7c221259cb8624f6bf3', 'admin', NULL, '2022-11-08', NULL, 0),
(56, 'افلامي', '<EMAIL>', '$2y$10$Cd1WjhYX5bhQFqo.LjHEheMIGxHh22CsL61Ne.64vOjfdi9Vf.VQO', NULL, NULL, NULL, 'q6ZOCEkjnUcDLT5lIoghQJfFcMN2', '0ab858e4cf8fdf551a8f07edf823cb3e', NULL, '0ab858e4cf8fdf551a8f07edf823cb3e.jpg', '2022-11-17', NULL, 0),
(57, 'test', '<EMAIL>', '$2y$10$012zvgsibDQ569OxPQaX9ec.cSh22MVIE6D2oycQ7vfOuL.fiDe4q', 'أنتيغوا وباربودا', NULL, NULL, NULL, '6456c5afe6ca394b55f490e881d30040', 'admin', '6456c5afe6ca394b55f490e881d30040.jpg', '2022-11-17', NULL, 0),
(61, 'qw', 'qw', '$2y$10$HeisjNbZaMD/BN8L20VOiO1BSaZztxK/a8dQ8L0b6xWKRwe1TZLJC', 'egypt', NULL, NULL, NULL, '00eb6b14440b02cf268cb39696b96062', 'user', NULL, '2023-01-04', '4444', 0),
(62, 'mohamed Mohamed', '<EMAIL>', '$2y$10$LU9Kl./7iOgRPc3bCM4Sx.tgPYkuVRJ3bXq1zyXxSZx6CkPtB8Tda', 'مصر', NULL, NULL, NULL, 'e0cf1820182471d77e91404462c4b9a0', 'user', NULL, '2023-01-10', NULL, 0),
(63, 'admin', '<EMAIL>', '$2y$10$gPWKn8qDsToQm0kt.BWls.Z27EmbrbIif7aVBxEOVjxGUZLvU8iyy', 'السعودية', NULL, NULL, NULL, 'da8c09e71dbf7c840930b2fa588cb5d2', 'admin', NULL, '2023-01-17', NULL, 0),
(64, 'sa', 'sa', '$2y$10$Bi6EFR/enYoK0Q2VNr/pE.XWDxGLo5kUIjiqxnDMmMtGZgj7G8AhC', 'Eman', NULL, NULL, NULL, 'b02b72510d6ef2875736d309e878dd5b', 'user', NULL, '2023-01-17', '4444', 0),
(65, 'aa', 'aa', '$2y$10$2/F939fsX5zyZFPRDDLxQevODcU8DUWldIuL0QA5UDTHnEDt/Y2xq', 'عمان', NULL, NULL, NULL, '136bcb92b4a167bcf03240c98781080e', 'user', '136bcb92b4a167bcf03240c98781080e.jpg', '2023-02-01', 'nnnn', 0),
(66, 'pl', 'pl', '$2y$10$OAuLBi125LrXwxBVeQjpbONUWp3dxZAYt9drRxcdXLNo9aIwBR.Uq', 'عمان', NULL, NULL, NULL, '67e8298a1f84ce674902f9bf15f12360', 'user', NULL, '2023-02-01', NULL, 0),
(67, 'mohamed', 'm', '$2y$10$uNTX0WuZna/5DrmdsVBXt.A2vqD5T9sDhpNZJF40y.3MCU/qvlQp2', 'Eman', NULL, NULL, NULL, '9cd4437b83ce79298fa2a00354c43696', 'user', NULL, '2023-02-05', NULL, 0),
(68, 'moha', 'n', '$2y$10$1liEpwxve98OR0BdPsHGHOIvf6gRt/0FtpdfffgDHw.f/twZRerMy', 'egy', NULL, NULL, NULL, '63387ab8f326ce748d73752c3d214329', 'user', NULL, '2023-02-05', NULL, 0),
(69, 'احمد', '<EMAIL>', '$2y$10$/9Nux.Y.DHjIT4Ym1ect0uQ3Ihic/QzqrbqFZiM3D6I.DrBp6oCLu', 'مصر', NULL, NULL, NULL, '70dc52c2026c6ffc7c7261fbf716bdde', 'user', NULL, '2023-02-05', NULL, 0),
(70, 'احمد', '<EMAIL>', '$2y$10$q3a9OIk0saT73Nrg8Kqoy.N69YzmYNJ87XxZeHIwGDSel4Wn6YYNS', 'عمان', NULL, NULL, NULL, '2280bd4c37ba2890c29d9c7e62a4ad05', 'user', NULL, '2023-02-14', NULL, 0),
(71, 'احمد', 'qq', '$2y$10$aXCODic2MWTp3jBAuEHqcO0EGrNqMkCh/9d/9xDQ7FPxsrMUoK1/i', 'عمان', NULL, NULL, NULL, 'fd0381b0248607da559a96d722842d53', 'user', NULL, '2023-02-17', NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `weather_shots`
--

CREATE TABLE `weather_shots` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `photographer` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `location` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `schedule` text COLLATE utf8mb4_unicode_ci,
  `hide` text COLLATE utf8mb4_unicode_ci,
  `shares` int(10) UNSIGNED DEFAULT NULL,
  `media` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `weather_shots`
--

INSERT INTO `weather_shots` (`id`, `photographer`, `location`, `date`, `schedule`, `hide`, `shares`, `media`) VALUES
(108, 'احمد', 'صحار', '2022-11-17 07:45:50', '2022-11-12 12:00:0', '', 2, '1668267192.mp4'),
(109, 'احمد', 'امريكا', '2022-11-21 10:32:00', '', '', 0, '1668990780.mp4'),
(110, 'احمد', 'صحار', '2023-01-17 20:45:22', '', '', 9, '1671199097.mp4'),
(111, 'هلا', 'السعودية', '2023-01-18 06:00:00', NULL, NULL, 0, '1673989832.mp4'),
(112, 'عغغع', 'مصر', '2023-01-18 06:00:00', NULL, NULL, 0, '1674053513.mp4'),
(114, 'عمان', 'عمان', '2023-01-18 06:00:00', NULL, NULL, 0, '1674056669.jpg'),
(115, 'توقعات', 'توقعات', '2023-01-18 06:00:00', NULL, NULL, 0, '1674057223.mp4'),
(116, 'شناص', 'شناص', '2023-01-18 06:00:00', NULL, NULL, 0, '1674057113.mp4'),
(117, 'احمد', 'السعودية', '2023-01-18 06:00:00', NULL, NULL, 0, '1673990141.jpg'),
(118, 'شناص', 'شناص', '2023-01-18 06:00:00', NULL, NULL, 0, '1674057415.mp4'),
(119, 'هلا', 'هلا', '2023-01-21 06:00:00', NULL, NULL, 0, '1674252070.jpg'),
(120, 'ممم', 'مم', '2023-01-22 11:38:01', NULL, NULL, 5, '1674261870.mp4'),
(121, 'اودية جارفة بعمان بولاية ازكي', 'ازكي', '2023-01-31 21:59:22', '', '', 2, '1674404999.mp4'),
(122, 'هلا', 'هلا', '2023-01-22 17:12:17', NULL, NULL, 1, '1674406709.jpg'),
(123, 'امطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاودية امطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاوديةامطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاودية', 'مسقط', '2023-02-04 23:10:41', NULL, NULL, 1, '1675255101.jpg'),
(124, 'امطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاودية امطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاوديةامطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاودية', 'مسقط', '2023-02-01 06:00:00', NULL, NULL, 0, '1675255065.jpg'),
(125, 'امطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاودية', 'مسقط', '2023-02-05 02:32:55', NULL, NULL, 2, '1675255198.jpg'),
(126, 'محمد', 'امريكا', '2023-02-05 03:22:25', '2023-02-05 06:54:0', '', 1, '1675565378.mp4'),
(127, 'امطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاوديةامطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاوديةامطار غزيرة جدا تأثرت بها مسقط ادت لجريان غزير للاودية', 'مسقط', '2023-02-01 06:00:00', NULL, NULL, 0, '1675255360.mp4'),
(128, 'محمد', 'امريكا', '2023-02-14 15:21:47', '', '', 4, '1675567828.jpg'),
(129, 'امطار', 'امطار', '2023-02-15 14:10:54', NULL, NULL, 2, '1675254791.mp4'),
(130, 'هلا', 'هلا', '2023-02-16 06:00:00', NULL, NULL, 0, '1676495364.jpg'),
(131, 'هلا', 'امريكا', '2023-02-16 08:00:00', '2023-02-16 02:02:0', '2023-02-16 02:00:0', 0, '1676498495.mp4'),
(132, 'هلا', 'امريكا', '2023-02-15 22:03:50', '2023-02-16 02:02:0', '2023-02-16 02:00:0', 1, '1676498496.mp4'),
(137, 'امطار', 'امطار', '2023-02-17 18:00:00', NULL, NULL, 0, '1676581192.jpeg'),
(141, 'هلا', 'هلا', '2023-02-16 21:17:56', NULL, NULL, 1, '1676582217.mp4'),
(142, 'هلا', 'هلا', '2023-02-17 07:15:49', NULL, NULL, 0, '1676582150.jpg');

-- --------------------------------------------------------

--
-- Table structure for table `withdrawals`
--

CREATE TABLE `withdrawals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `marketer_id` int(10) UNSIGNED NOT NULL,
  `method` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `ads`
--
ALTER TABLE `ads`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `ads_details`
--
ALTER TABLE `ads_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `change_password`
--
ALTER TABLE `change_password`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `coupons`
--
ALTER TABLE `coupons`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `marketers`
--
ALTER TABLE `marketers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `marketer_details`
--
ALTER TABLE `marketer_details`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `outlooks`
--
ALTER TABLE `outlooks`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `outlooks_comments`
--
ALTER TABLE `outlooks_comments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `outlooks_files`
--
ALTER TABLE `outlooks_files`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `outlooks_likes`
--
ALTER TABLE `outlooks_likes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `outlooks_shares`
--
ALTER TABLE `outlooks_shares`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `payout_settings`
--
ALTER TABLE `payout_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `pending_shots`
--
ALTER TABLE `pending_shots`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `support_tickets`
--
ALTER TABLE `support_tickets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `used_coupons`
--
ALTER TABLE `used_coupons`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `weather_shots`
--
ALTER TABLE `weather_shots`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `withdrawals`
--
ALTER TABLE `withdrawals`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `ads`
--
ALTER TABLE `ads`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `ads_details`
--
ALTER TABLE `ads_details`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `change_password`
--
ALTER TABLE `change_password`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `countries`
--
ALTER TABLE `countries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=61;

--
-- AUTO_INCREMENT for table `coupons`
--
ALTER TABLE `coupons`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `marketers`
--
ALTER TABLE `marketers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `marketer_details`
--
ALTER TABLE `marketer_details`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `outlooks`
--
ALTER TABLE `outlooks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=254;

--
-- AUTO_INCREMENT for table `outlooks_comments`
--
ALTER TABLE `outlooks_comments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=182;

--
-- AUTO_INCREMENT for table `outlooks_files`
--
ALTER TABLE `outlooks_files`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=374;

--
-- AUTO_INCREMENT for table `outlooks_likes`
--
ALTER TABLE `outlooks_likes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `outlooks_shares`
--
ALTER TABLE `outlooks_shares`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payout_settings`
--
ALTER TABLE `payout_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `pending_shots`
--
ALTER TABLE `pending_shots`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=98;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `support_tickets`
--
ALTER TABLE `support_tickets`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1172;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `used_coupons`
--
ALTER TABLE `used_coupons`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=69;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=72;

--
-- AUTO_INCREMENT for table `weather_shots`
--
ALTER TABLE `weather_shots`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=143;

--
-- AUTO_INCREMENT for table `withdrawals`
--
ALTER TABLE `withdrawals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
