.vue2leaflet-map {
  height: 400px;
  z-index: 1;
}

html[dir='rtl'] .leaflet-pane,
html[dir='rtl'] .leaflet-tile,
html[dir='rtl'] .leaflet-marker-icon,
html[dir='rtl'] .leaflet-marker-shadow,
html[dir='rtl'] .leaflet-tile-container,
html[dir='rtl'] .leaflet-pane > svg,
html[dir='rtl'] .leaflet-pane > canvas,
html[dir='rtl'] .leaflet-zoom-box,
html[dir='rtl'] .leaflet-image-layer,
html[dir='rtl'] .leaflet-layer {
  left: 0;
  right: unset;
}
/*# sourceMappingURL=map-leaflet.css.map */