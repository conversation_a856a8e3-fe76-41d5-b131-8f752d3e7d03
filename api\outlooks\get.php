<?php
/**
 * الحصول على التوقعات الجوية
 * Get Weather Outlooks API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// التحقق من المعاملات
$onlyFutureScheduled = isset($_GET['onlyFutureScheduled']) && $_GET['onlyFutureScheduled'] === 'true';
$ignoreSchedule = isset($_GET['ignoreSchedule']) && $_GET['ignoreSchedule'] === 'true';

$now = date('Y-m-d H:i:s');

if ($onlyFutureScheduled) {
    // الحصول على التوقعات المجدولة في المستقبل فقط
    $sql = "SELECT o.*, 
                   GROUP_CONCAT(of.file) as files,
                   COUNT(DISTINCT oc.id) as comments_count,
                   COUNT(DISTINCT ol.id) as likes_count
            FROM outlooks o
            LEFT JOIN outlooks_files of ON o.id = of.outlook_id
            LEFT JOIN outlooks_comments oc ON o.id = oc.outlook_id
            LEFT JOIN outlooks_likes ol ON o.id = ol.outlook_id
            WHERE o.schedule > ?
            GROUP BY o.id
            ORDER BY o.id DESC";
    
    $outlooks = $db->select($sql, [$now]);
} else {
    // الحصول على التوقعات العادية
    $scheduleCondition = $ignoreSchedule ? 
        "1=1" : 
        "(o.schedule <= ? OR o.schedule IS NULL OR o.schedule = '')";
    
    $sql = "SELECT o.*, 
                   GROUP_CONCAT(of.file) as files,
                   COUNT(DISTINCT oc.id) as comments_count,
                   COUNT(DISTINCT ol.id) as likes_count
            FROM outlooks o
            LEFT JOIN outlooks_files of ON o.id = of.outlook_id
            LEFT JOIN outlooks_comments oc ON o.id = oc.outlook_id
            LEFT JOIN outlooks_likes ol ON o.id = ol.outlook_id
            WHERE {$scheduleCondition}
            GROUP BY o.id
            ORDER BY o.id DESC";
    
    $params = $ignoreSchedule ? [] : [$now];
    $outlooks = $db->select($sql, $params);
}

// تنسيق البيانات
foreach ($outlooks as &$outlook) {
    // تحويل الملفات إلى مصفوفة
    if ($outlook['files']) {
        $outlook['files'] = array_map(function($file) {
            return [
                'file' => $file,
                'url' => APP_URL . '/uploads/outlooks/' . $file,
                'type' => getFileType($file)
            ];
        }, explode(',', $outlook['files']));
    } else {
        $outlook['files'] = [];
    }
    
    // الحصول على التعليقات
    $commentsSql = "SELECT oc.*, u.name as user_name, u.pic as user_pic 
                    FROM outlooks_comments oc 
                    LEFT JOIN users u ON oc.user_id = u.id 
                    WHERE oc.outlook_id = ? 
                    ORDER BY oc.id DESC";
    $outlook['comments'] = $db->select($commentsSql, [$outlook['id']]);
    
    // تنسيق التاريخ
    $outlook['formatted_date'] = formatArabicDate($outlook['date']);
}

successResponse($outlooks);
