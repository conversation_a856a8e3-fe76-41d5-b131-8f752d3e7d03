<?php

namespace App\Http\Controllers;

use App\Models\WeatherShots;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class WeatherShotsController extends Controller
{
    public function get()
    {
        if(request()->get("onlyFutureScheduled", false)){
            $now = now();

            return WeatherShots::query()
                ->where("schedule", ">", $now)
                ->orderBy('id', 'DESC')
                ->get();
        }

        $now = request()->get("ignoreSchedule", false) ? now()->addCenturiesNoOverflow(50) : now();

        return WeatherShots::query()
            ->where(function ($query) use ($now) {
                $query->where("schedule", "<=", $now)
                ->orWhereNull("schedule");
            })
            ->orderBy('id', 'DESC')
            ->get();
    }
    public function getByID($id)
    {
        return WeatherShots::where('id', $id)->first();
    }
    public function add(Request $request)
    {
        $validate = $request->validate([
            'media' => 'required|mimes:jpeg,png,jpg,mp4,flv,3gp,mov,avi,wmv'
        ]);
        if ($validate) {
            $dataDecode = json_decode($request->data, true);
            $filename = time() . '.' . $request->media->getClientOriginalExtension();
            $file_path = $request->file('media')->storeAs('/weather-shots/', $filename, 'public');

            return WeatherShots::create([
                'photographer' => $dataDecode['photographer'],
                'location' => $dataDecode['location'],
                'date' => $dataDecode['date'],
                'schedule' => $dataDecode['schedule'],
                'hide' => $dataDecode['hide'],
                'shares' => 0,
                'media' => $filename
            ]);
        }
    }
    public function delete(Request $request)
    {
        WeatherShots::where('id', $request->id)->delete();
    }
    public function multiDelete(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            WeatherShots::where('id', $id)->delete();
        }

        return true;
    }
    public function edit(Request $request)
    {
        $dataDecode = json_decode($request->data, true);

        $validate = $request->validate([
            'media' => 'nullable|mimes:jpeg,png,jpg,mp4,flv,3gp,mov,avi,wmv'
        ]);


        $weatherShots = WeatherShots::where("id", $dataDecode['id'])->first();

        if(is_null($weatherShots)) return abort (404);

        $data = [
            'photographer' => $dataDecode['photographer'] ?? null,
            'location' => $dataDecode['location'] ?? null,
            'date' => $dataDecode['date'] ?? null,
            'schedule' => $dataDecode['publishDate'] ?? null,
            'hide' => $dataDecode['hideDate'] ?? null,
        ];

        if($request->hasFile("media"))
        {
            // remove the old file
            $oldFileName = $weatherShots->media;
            if (Storage::disk('public')->exists('/weather-shots/'.$oldFileName))
            {
                Storage::disk('public')->delete("/weather-shots/.$oldFileName");
            }

            // store the new file
            $filename = time() . '.' . $request->media->getClientOriginalExtension();
            $request->file('media')->storeAs('/weather-shots/', $filename, 'public');
            $data['media'] = $filename;
        }

        WeatherShots::where('id', $dataDecode['id'])->update($data);
    }
    public function delete_unused(Request $request)
    {
        $getExpired = WeatherShots::where('hide', '<=', date('Y-m-d H:i:s'))->get();
        foreach ($getExpired as $shot) {
            if ($shot->hide !== null && $shot->hide !== '') {
                File::delete(public_path() . '/storage/weather-shots/' . $shot->media);
                WeatherShots::where('id', $shot->id)->delete();
            }
        }
    }
}
