<?php
/**
 * كلاس المصادقة والحماية
 * Authentication and Security Class
 */

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * تسجيل دخول المستخدم
     */
    public function loginUser($email, $password) {
        $sql = "SELECT * FROM users WHERE email = ? AND ban = 0";
        $user = $this->db->selectOne($sql, [$email]);
        
        if ($user && password_verify($password, $user['password'])) {
            // إنشاء جلسة
            session_start();
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_token'] = $user['token'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['login_time'] = time();
            
            return [
                'success' => true,
                'user' => $this->getUserWithSubscription($user['id']),
                'message' => 'تم تسجيل الدخول بنجاح'
            ];
        }
        
        return [
            'success' => false,
            'message' => $user ? 'كلمة المرور غير صحيحة' : 'الحساب غير موجود'
        ];
    }

    /**
     * تسجيل دخول المشرف
     */
    public function loginAdmin($email, $password) {
        $sql = "SELECT * FROM admins WHERE email = ? AND ban = 0";
        $admin = $this->db->selectOne($sql, [$email]);
        
        if ($admin && password_verify($password, $admin['password'])) {
            session_start();
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_token'] = $admin['token'];
            $_SESSION['admin_role'] = $admin['role'];
            $_SESSION['admin_name'] = $admin['name'];
            $_SESSION['login_time'] = time();
            
            return [
                'success' => true,
                'admin' => $admin,
                'message' => 'تم تسجيل الدخول بنجاح'
            ];
        }
        
        return [
            'success' => false,
            'message' => $admin ? 'كلمة المرور غير صحيحة' : 'الحساب غير موجود'
        ];
    }

    /**
     * تسجيل مستخدم جديد
     */
    public function registerUser($name, $email, $password, $country = null, $phone = null, $coupon = null) {
        // التحقق من وجود المستخدم
        $existingUser = $this->db->selectOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existingUser) {
            return [
                'success' => false,
                'message' => 'البريد الإلكتروني مسجل من قبل'
            ];
        }

        // إنشاء المستخدم الجديد
        $token = md5(time() . $email);
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO users (name, email, password, country, phone, token, role, date, coupon, ban) 
                VALUES (?, ?, ?, ?, ?, ?, 'user', ?, ?, 0)";
        
        $userId = $this->db->insert($sql, [
            $name, $email, $hashedPassword, $country, $phone, $token, date('Y-m-d'), $coupon
        ]);

        if ($userId) {
            return [
                'success' => true,
                'user_id' => $userId,
                'message' => 'تم إنشاء الحساب بنجاح'
            ];
        }

        return [
            'success' => false,
            'message' => 'حدث خطأ أثناء إنشاء الحساب'
        ];
    }

    /**
     * التحقق من صحة التوكن
     */
    public function validateToken($token, $type = 'user') {
        $table = $type === 'admin' ? 'admins' : 'users';
        $sql = "SELECT * FROM {$table} WHERE token = ? AND ban = 0";
        return $this->db->selectOne($sql, [$token]);
    }

    /**
     * التحقق من صحة الجلسة
     */
    public function validateSession() {
        session_start();
        
        if (!isset($_SESSION['login_time'])) {
            return false;
        }
        
        // التحقق من انتهاء الجلسة
        if (time() - $_SESSION['login_time'] > SESSION_TIMEOUT) {
            $this->logout();
            return false;
        }
        
        return true;
    }

    /**
     * التحقق من صلاحيات المشرف
     */
    public function requireAdmin() {
        if (!$this->validateSession() || !isset($_SESSION['admin_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'غير مصرح لك بالوصول']);
            exit;
        }
    }

    /**
     * التحقق من صلاحيات المستخدم
     */
    public function requireUser() {
        if (!$this->validateSession() || !isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo json_encode(['error' => 'غير مصرح لك بالوصول']);
            exit;
        }
    }

    /**
     * الحصول على المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->validateSession()) {
            return null;
        }
        
        if (isset($_SESSION['user_id'])) {
            return $this->getUserWithSubscription($_SESSION['user_id']);
        }
        
        if (isset($_SESSION['admin_id'])) {
            $sql = "SELECT * FROM admins WHERE id = ?";
            return $this->db->selectOne($sql, [$_SESSION['admin_id']]);
        }
        
        return null;
    }

    /**
     * الحصول على المستخدم مع بيانات الاشتراك
     */
    private function getUserWithSubscription($userId) {
        $sql = "SELECT u.*, s.* FROM users u 
                LEFT JOIN subscriptions s ON u.id = s.user_id AND s.active = 1 
                WHERE u.id = ?";
        return $this->db->selectOne($sql, [$userId]);
    }

    /**
     * تسجيل الخروج
     */
    public function logout() {
        session_start();
        session_destroy();
        return ['success' => true, 'message' => 'تم تسجيل الخروج بنجاح'];
    }

    /**
     * إنشاء توكن إعادة تعيين كلمة المرور
     */
    public function createPasswordResetToken($email, $type = 'user') {
        $table = $type === 'admin' ? 'admins' : 'users';
        $user = $this->db->selectOne("SELECT id FROM {$table} WHERE email = ?", [$email]);
        
        if (!$user) {
            return ['success' => false, 'message' => 'البريد الإلكتروني غير موجود'];
        }

        $token = md5(time() . $email);
        $code = strtoupper(substr(md5(time()), 0, 5));
        $expireTime = date('Y-m-d H:i:s', time() + 3600); // ساعة واحدة

        $userField = $type === 'admin' ? 'marketer' : 'user';
        $sql = "INSERT INTO change_password ({$userField}, token, code, expire_time) VALUES (?, ?, ?, ?)";
        
        if ($this->db->insert($sql, [$email, $token, $code, $expireTime])) {
            return [
                'success' => true,
                'token' => $token,
                'code' => $code,
                'message' => 'تم إرسال رمز إعادة التعيين'
            ];
        }

        return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء رمز إعادة التعيين'];
    }
}
