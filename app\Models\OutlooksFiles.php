<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class OutlooksFiles extends Model
{
    use HasFactory;
    public $table = "outlooks_files";
    protected $fillable = [
        'outlook_id',
        'file',
    ];
    protected $hidden = [];
    public $timestamps = false;

    public function DeleteStorageFile(): bool
    {
        return Storage::disk("public")->delete("outlooks/".$this->file);
    }
}
