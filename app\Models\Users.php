<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Users extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'email',
        'password',
        'country',
        'phone',
        'facebook_token',
        'google_token',
        'token',
        'role',
        'pic',
        'date',
        'coupon',
        'ban',
        'device_key'
    ];
    protected $hidden = [
        'password',
    ];
    public $timestamps = false;

    public function getIsSubscriptionActiveAttribute(): ?bool
    {
        // user has subscription record
        if(!is_null($this->subscription()->latest('id')->first()))
        {
            $subscription_record = $this->subscription()->latest('id')->first();

            // check if the subscription is inactive
            if($subscription_record->active != true)
            {
                return false;
            }

            // check if the subscription should still active
            $now = now();

            if($subscription_record->expire_date < $now)
            {
                Subscriptions::query()->where('id', $subscription_record->id)->update(['active' => false]);
                return false;
            }


            return true;
        }

        return false;
    }

    public function subscription()
    {
        return $this->hasMany(Subscriptions::class, 'user_id', 'id');
    }

    public function outlooks_subscription()
    {
        return $this->hasOne(OutlooksSubscription::class);
    }
}
