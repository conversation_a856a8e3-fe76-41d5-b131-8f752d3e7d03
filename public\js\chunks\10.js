/*! For license information please see 10.js.LICENSE.txt */
(window.webpackJsonp=window.webpackJsonp||[]).push([[10,7],{"+s0g":function(e,t,n){!function(e){"use strict";var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),n="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),a=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],r=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i;e.defineLocale("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",w:"één week",ww:"%d weken",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(n("wd/R"))},"//9w":function(e,t,n){!function(e){"use strict";e.defineLocale("se",{months:"ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu".split("_"),monthsShort:"ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov".split("_"),weekdays:"sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat".split("_"),weekdaysShort:"sotn_vuos_maŋ_gask_duor_bear_láv".split("_"),weekdaysMin:"s_v_m_g_d_b_L".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"MMMM D. [b.] YYYY",LLL:"MMMM D. [b.] YYYY [ti.] HH:mm",LLLL:"dddd, MMMM D. [b.] YYYY [ti.] HH:mm"},calendar:{sameDay:"[otne ti] LT",nextDay:"[ihttin ti] LT",nextWeek:"dddd [ti] LT",lastDay:"[ikte ti] LT",lastWeek:"[ovddit] dddd [ti] LT",sameElse:"L"},relativeTime:{future:"%s geažes",past:"maŋit %s",s:"moadde sekunddat",ss:"%d sekunddat",m:"okta minuhta",mm:"%d minuhtat",h:"okta diimmu",hh:"%d diimmut",d:"okta beaivi",dd:"%d beaivvit",M:"okta mánnu",MM:"%d mánut",y:"okta jahki",yy:"%d jagit"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},"/X5v":function(e,t,n){!function(e){"use strict";e.defineLocale("x-pseudo",{months:"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér".split("_"),monthsShort:"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc".split("_"),monthsParseExact:!0,weekdays:"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý".split("_"),weekdaysShort:"S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát".split("_"),weekdaysMin:"S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[T~ódá~ý át] LT",nextDay:"[T~ómó~rró~w át] LT",nextWeek:"dddd [át] LT",lastDay:"[Ý~ést~érdá~ý át] LT",lastWeek:"[L~ást] dddd [át] LT",sameElse:"L"},relativeTime:{future:"í~ñ %s",past:"%s á~gó",s:"á ~féw ~sécó~ñds",ss:"%d s~écóñ~ds",m:"á ~míñ~úté",mm:"%d m~íñú~tés",h:"á~ñ hó~úr",hh:"%d h~óúrs",d:"á ~dáý",dd:"%d d~áýs",M:"á ~móñ~th",MM:"%d m~óñt~hs",y:"á ~ýéár",yy:"%d ý~éárs"},dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(n("wd/R"))},"0mo+":function(e,t,n){!function(e){"use strict";var t={1:"༡",2:"༢",3:"༣",4:"༤",5:"༥",6:"༦",7:"༧",8:"༨",9:"༩",0:"༠"},n={"༡":"1","༢":"2","༣":"3","༤":"4","༥":"5","༦":"6","༧":"7","༨":"8","༩":"9","༠":"0"};e.defineLocale("bo",{months:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),monthsShort:"ཟླ་1_ཟླ་2_ཟླ་3_ཟླ་4_ཟླ་5_ཟླ་6_ཟླ་7_ཟླ་8_ཟླ་9_ཟླ་10_ཟླ་11_ཟླ་12".split("_"),monthsShortRegex:/^(ཟླ་\d{1,2})/,monthsParseExact:!0,weekdays:"གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་".split("_"),weekdaysShort:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),weekdaysMin:"ཉི_ཟླ_མིག_ལྷག_ཕུར_སངས_སྤེན".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[དི་རིང] LT",nextDay:"[སང་ཉིན] LT",nextWeek:"[བདུན་ཕྲག་རྗེས་མ], LT",lastDay:"[ཁ་སང] LT",lastWeek:"[བདུན་ཕྲག་མཐའ་མ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ལ་",past:"%s སྔན་ལ",s:"ལམ་སང",ss:"%d སྐར་ཆ།",m:"སྐར་མ་གཅིག",mm:"%d སྐར་མ",h:"ཆུ་ཚོད་གཅིག",hh:"%d ཆུ་ཚོད",d:"ཉིན་གཅིག",dd:"%d ཉིན་",M:"ཟླ་བ་གཅིག",MM:"%d ཟླ་བ",y:"ལོ་གཅིག",yy:"%d ལོ"},preparse:function(e){return e.replace(/[༡༢༣༤༥༦༧༨༩༠]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,meridiemHour:function(e,t){return 12===e&&(e=0),"མཚན་མོ"===t&&e>=4||"ཉིན་གུང"===t&&e<5||"དགོང་དག"===t?e+12:e},meridiem:function(e,t,n){return e<4?"མཚན་མོ":e<10?"ཞོགས་ཀས":e<17?"ཉིན་གུང":e<20?"དགོང་དག":"མཚན་མོ"},week:{dow:0,doy:6}})}(n("wd/R"))},"0tRk":function(e,t,n){!function(e){"use strict";e.defineLocale("pt-br",{months:"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_sáb".split("_"),weekdaysMin:"do_2ª_3ª_4ª_5ª_6ª_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [às] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [às] HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"poucos segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",invalidDate:"Data inválida"})}(n("wd/R"))},"1ppg":function(e,t,n){!function(e){"use strict";e.defineLocale("fil",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}})}(n("wd/R"))},"1rYy":function(e,t,n){!function(e){"use strict";e.defineLocale("hy-am",{months:{format:"հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի".split("_"),standalone:"հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր".split("_")},monthsShort:"հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ".split("_"),weekdays:"կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ".split("_"),weekdaysShort:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),weekdaysMin:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY թ.",LLL:"D MMMM YYYY թ., HH:mm",LLLL:"dddd, D MMMM YYYY թ., HH:mm"},calendar:{sameDay:"[այսօր] LT",nextDay:"[վաղը] LT",lastDay:"[երեկ] LT",nextWeek:function(){return"dddd [օրը ժամը] LT"},lastWeek:function(){return"[անցած] dddd [օրը ժամը] LT"},sameElse:"L"},relativeTime:{future:"%s հետո",past:"%s առաջ",s:"մի քանի վայրկյան",ss:"%d վայրկյան",m:"րոպե",mm:"%d րոպե",h:"ժամ",hh:"%d ժամ",d:"օր",dd:"%d օր",M:"ամիս",MM:"%d ամիս",y:"տարի",yy:"%d տարի"},meridiemParse:/գիշերվա|առավոտվա|ցերեկվա|երեկոյան/,isPM:function(e){return/^(ցերեկվա|երեկոյան)$/.test(e)},meridiem:function(e){return e<4?"գիշերվա":e<12?"առավոտվա":e<17?"ցերեկվա":"երեկոյան"},dayOfMonthOrdinalParse:/\d{1,2}|\d{1,2}-(ին|րդ)/,ordinal:function(e,t){switch(t){case"DDD":case"w":case"W":case"DDDo":return 1===e?e+"-ին":e+"-րդ";default:return e}},week:{dow:1,doy:7}})}(n("wd/R"))},"1xZ4":function(e,t,n){!function(e){"use strict";e.defineLocale("ca",{months:{standalone:"gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre".split("_"),format:"de gener_de febrer_de març_d'abril_de maig_de juny_de juliol_d'agost_de setembre_d'octubre_de novembre_de desembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._maig_juny_jul._ag._set._oct._nov._des.".split("_"),monthsParseExact:!0,weekdays:"diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dt._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dt_dc_dj_dv_ds".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a les] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a les] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:function(){return"[avui a "+(1!==this.hours()?"les":"la")+"] LT"},nextDay:function(){return"[demà a "+(1!==this.hours()?"les":"la")+"] LT"},nextWeek:function(){return"dddd [a "+(1!==this.hours()?"les":"la")+"] LT"},lastDay:function(){return"[ahir a "+(1!==this.hours()?"les":"la")+"] LT"},lastWeek:function(){return"[el] dddd [passat a "+(1!==this.hours()?"les":"la")+"] LT"},sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"uns segons",ss:"%d segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|è|a)/,ordinal:function(e,t){var n=1===e?"r":2===e?"n":3===e?"r":4===e?"t":"è";return"w"!==t&&"W"!==t||(n="a"),e+n},week:{dow:1,doy:4}})}(n("wd/R"))},"2B1R":function(e,t,n){"use strict";var a=n("I+eb"),r=n("tycR").map,s=n("Hd5f"),i=n("rkAj"),o=s("map"),d=i("map");a({target:"Array",proto:!0,forced:!o||!d},{map:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}})},"2fjn":function(e,t,n){!function(e){"use strict";e.defineLocale("fr-ca",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}}})}(n("wd/R"))},"2ykv":function(e,t,n){!function(e){"use strict";var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),n="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),a=[/^jan/i,/^feb/i,/^maart|mrt.?$/i,/^apr/i,/^mei$/i,/^jun[i.]?$/i,/^jul[i.]?$/i,/^aug/i,/^sep/i,/^okt/i,/^nov/i,/^dec/i],r=/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december|jan\.?|feb\.?|mrt\.?|apr\.?|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i;e.defineLocale("nl-be",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(januari|februari|maart|april|mei|ju[nl]i|augustus|september|oktober|november|december)/i,monthsShortStrictRegex:/^(jan\.?|feb\.?|mrt\.?|apr\.?|mei|ju[nl]\.?|aug\.?|sep\.?|okt\.?|nov\.?|dec\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"zo_ma_di_wo_do_vr_za".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",ss:"%d seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(n("wd/R"))},"3E1r":function(e,t,n){!function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},n={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"},a=[/^जन/i,/^फ़र|फर/i,/^मार्च/i,/^अप्रै/i,/^मई/i,/^जून/i,/^जुल/i,/^अग/i,/^सितं|सित/i,/^अक्टू/i,/^नव|नवं/i,/^दिसं|दिस/i];e.defineLocale("hi",{months:{format:"जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर".split("_"),standalone:"जनवरी_फरवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितंबर_अक्टूबर_नवंबर_दिसंबर".split("_")},monthsShort:"जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.".split("_"),weekdays:"रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm बजे",LTS:"A h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm बजे",LLLL:"dddd, D MMMM YYYY, A h:mm बजे"},monthsParse:a,longMonthsParse:a,shortMonthsParse:[/^जन/i,/^फ़र/i,/^मार्च/i,/^अप्रै/i,/^मई/i,/^जून/i,/^जुल/i,/^अग/i,/^सित/i,/^अक्टू/i,/^नव/i,/^दिस/i],monthsRegex:/^(जनवरी|जन\.?|फ़रवरी|फरवरी|फ़र\.?|मार्च?|अप्रैल|अप्रै\.?|मई?|जून?|जुलाई|जुल\.?|अगस्त|अग\.?|सितम्बर|सितंबर|सित\.?|अक्टूबर|अक्टू\.?|नवम्बर|नवंबर|नव\.?|दिसम्बर|दिसंबर|दिस\.?)/i,monthsShortRegex:/^(जनवरी|जन\.?|फ़रवरी|फरवरी|फ़र\.?|मार्च?|अप्रैल|अप्रै\.?|मई?|जून?|जुलाई|जुल\.?|अगस्त|अग\.?|सितम्बर|सितंबर|सित\.?|अक्टूबर|अक्टू\.?|नवम्बर|नवंबर|नव\.?|दिसम्बर|दिसंबर|दिस\.?)/i,monthsStrictRegex:/^(जनवरी?|फ़रवरी|फरवरी?|मार्च?|अप्रैल?|मई?|जून?|जुलाई?|अगस्त?|सितम्बर|सितंबर|सित?\.?|अक्टूबर|अक्टू\.?|नवम्बर|नवंबर?|दिसम्बर|दिसंबर?)/i,monthsShortStrictRegex:/^(जन\.?|फ़र\.?|मार्च?|अप्रै\.?|मई?|जून?|जुल\.?|अग\.?|सित\.?|अक्टू\.?|नव\.?|दिस\.?)/i,calendar:{sameDay:"[आज] LT",nextDay:"[कल] LT",nextWeek:"dddd, LT",lastDay:"[कल] LT",lastWeek:"[पिछले] dddd, LT",sameElse:"L"},relativeTime:{future:"%s में",past:"%s पहले",s:"कुछ ही क्षण",ss:"%d सेकंड",m:"एक मिनट",mm:"%d मिनट",h:"एक घंटा",hh:"%d घंटे",d:"एक दिन",dd:"%d दिन",M:"एक महीने",MM:"%d महीने",y:"एक वर्ष",yy:"%d वर्ष"},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/रात|सुबह|दोपहर|शाम/,meridiemHour:function(e,t){return 12===e&&(e=0),"रात"===t?e<4?e:e+12:"सुबह"===t?e:"दोपहर"===t?e>=10?e:e+12:"शाम"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"रात":e<10?"सुबह":e<17?"दोपहर":e<20?"शाम":"रात"},week:{dow:0,doy:6}})}(n("wd/R"))},"4MV3":function(e,t,n){!function(e){"use strict";var t={1:"૧",2:"૨",3:"૩",4:"૪",5:"૫",6:"૬",7:"૭",8:"૮",9:"૯",0:"૦"},n={"૧":"1","૨":"2","૩":"3","૪":"4","૫":"5","૬":"6","૭":"7","૮":"8","૯":"9","૦":"0"};e.defineLocale("gu",{months:"જાન્યુઆરી_ફેબ્રુઆરી_માર્ચ_એપ્રિલ_મે_જૂન_જુલાઈ_ઑગસ્ટ_સપ્ટેમ્બર_ઑક્ટ્બર_નવેમ્બર_ડિસેમ્બર".split("_"),monthsShort:"જાન્યુ._ફેબ્રુ._માર્ચ_એપ્રિ._મે_જૂન_જુલા._ઑગ._સપ્ટે._ઑક્ટ્._નવે._ડિસે.".split("_"),monthsParseExact:!0,weekdays:"રવિવાર_સોમવાર_મંગળવાર_બુધ્વાર_ગુરુવાર_શુક્રવાર_શનિવાર".split("_"),weekdaysShort:"રવિ_સોમ_મંગળ_બુધ્_ગુરુ_શુક્ર_શનિ".split("_"),weekdaysMin:"ર_સો_મં_બુ_ગુ_શુ_શ".split("_"),longDateFormat:{LT:"A h:mm વાગ્યે",LTS:"A h:mm:ss વાગ્યે",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm વાગ્યે",LLLL:"dddd, D MMMM YYYY, A h:mm વાગ્યે"},calendar:{sameDay:"[આજ] LT",nextDay:"[કાલે] LT",nextWeek:"dddd, LT",lastDay:"[ગઇકાલે] LT",lastWeek:"[પાછલા] dddd, LT",sameElse:"L"},relativeTime:{future:"%s મા",past:"%s પહેલા",s:"અમુક પળો",ss:"%d સેકંડ",m:"એક મિનિટ",mm:"%d મિનિટ",h:"એક કલાક",hh:"%d કલાક",d:"એક દિવસ",dd:"%d દિવસ",M:"એક મહિનો",MM:"%d મહિનો",y:"એક વર્ષ",yy:"%d વર્ષ"},preparse:function(e){return e.replace(/[૧૨૩૪૫૬૭૮૯૦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/રાત|બપોર|સવાર|સાંજ/,meridiemHour:function(e,t){return 12===e&&(e=0),"રાત"===t?e<4?e:e+12:"સવાર"===t?e:"બપોર"===t?e>=10?e:e+12:"સાંજ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"રાત":e<10?"સવાર":e<17?"બપોર":e<20?"સાંજ":"રાત"},week:{dow:0,doy:6}})}(n("wd/R"))},"4dOw":function(e,t,n){!function(e){"use strict";e.defineLocale("en-ie",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(n("wd/R"))},"6+QB":function(e,t,n){!function(e){"use strict";e.defineLocale("ms",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?e>=11?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}})}(n("wd/R"))},"6B0Y":function(e,t,n){!function(e){"use strict";var t={1:"១",2:"២",3:"៣",4:"៤",5:"៥",6:"៦",7:"៧",8:"៨",9:"៩",0:"០"},n={"១":"1","២":"2","៣":"3","៤":"4","៥":"5","៦":"6","៧":"7","៨":"8","៩":"9","០":"0"};e.defineLocale("km",{months:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),monthsShort:"មករា_កុម្ភៈ_មីនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),weekdays:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),weekdaysShort:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysMin:"អា_ច_អ_ព_ព្រ_សុ_ស".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ព្រឹក|ល្ងាច/,isPM:function(e){return"ល្ងាច"===e},meridiem:function(e,t,n){return e<12?"ព្រឹក":"ល្ងាច"},calendar:{sameDay:"[ថ្ងៃនេះ ម៉ោង] LT",nextDay:"[ស្អែក ម៉ោង] LT",nextWeek:"dddd [ម៉ោង] LT",lastDay:"[ម្សិលមិញ ម៉ោង] LT",lastWeek:"dddd [សប្តាហ៍មុន] [ម៉ោង] LT",sameElse:"L"},relativeTime:{future:"%sទៀត",past:"%sមុន",s:"ប៉ុន្មានវិនាទី",ss:"%d វិនាទី",m:"មួយនាទី",mm:"%d នាទី",h:"មួយម៉ោង",hh:"%d ម៉ោង",d:"មួយថ្ងៃ",dd:"%d ថ្ងៃ",M:"មួយខែ",MM:"%d ខែ",y:"មួយឆ្នាំ",yy:"%d ឆ្នាំ"},dayOfMonthOrdinalParse:/ទី\d{1,2}/,ordinal:"ទី%d",preparse:function(e){return e.replace(/[១២៣៤៥៦៧៨៩០]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},week:{dow:1,doy:4}})}(n("wd/R"))},"7BjC":function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={s:["mõne sekundi","mõni sekund","paar sekundit"],ss:[e+"sekundi",e+"sekundit"],m:["ühe minuti","üks minut"],mm:[e+" minuti",e+" minutit"],h:["ühe tunni","tund aega","üks tund"],hh:[e+" tunni",e+" tundi"],d:["ühe päeva","üks päev"],M:["kuu aja","kuu aega","üks kuu"],MM:[e+" kuu",e+" kuud"],y:["ühe aasta","aasta","üks aasta"],yy:[e+" aasta",e+" aastat"]};return t?r[n][2]?r[n][2]:r[n][1]:a?r[n][0]:r[n][1]}e.defineLocale("et",{months:"jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember".split("_"),monthsShort:"jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets".split("_"),weekdays:"pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev".split("_"),weekdaysShort:"P_E_T_K_N_R_L".split("_"),weekdaysMin:"P_E_T_K_N_R_L".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[Täna,] LT",nextDay:"[Homme,] LT",nextWeek:"[Järgmine] dddd LT",lastDay:"[Eile,] LT",lastWeek:"[Eelmine] dddd LT",sameElse:"L"},relativeTime:{future:"%s pärast",past:"%s tagasi",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:"%d päeva",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},"7C5Q":function(e,t,n){!function(e){"use strict";e.defineLocale("en-in",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:0,doy:6}})}(n("wd/R"))},"7aV9":function(e,t,n){!function(e){"use strict";e.defineLocale("si",{months:"ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්".split("_"),monthsShort:"ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ".split("_"),weekdays:"ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා".split("_"),weekdaysShort:"ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන".split("_"),weekdaysMin:"ඉ_ස_අ_බ_බ්‍ර_සි_සෙ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"a h:mm",LTS:"a h:mm:ss",L:"YYYY/MM/DD",LL:"YYYY MMMM D",LLL:"YYYY MMMM D, a h:mm",LLLL:"YYYY MMMM D [වැනි] dddd, a h:mm:ss"},calendar:{sameDay:"[අද] LT[ට]",nextDay:"[හෙට] LT[ට]",nextWeek:"dddd LT[ට]",lastDay:"[ඊයේ] LT[ට]",lastWeek:"[පසුගිය] dddd LT[ට]",sameElse:"L"},relativeTime:{future:"%sකින්",past:"%sකට පෙර",s:"තත්පර කිහිපය",ss:"තත්පර %d",m:"මිනිත්තුව",mm:"මිනිත්තු %d",h:"පැය",hh:"පැය %d",d:"දිනය",dd:"දින %d",M:"මාසය",MM:"මාස %d",y:"වසර",yy:"වසර %d"},dayOfMonthOrdinalParse:/\d{1,2} වැනි/,ordinal:function(e){return e+" වැනි"},meridiemParse:/පෙර වරු|පස් වරු|පෙ.ව|ප.ව./,isPM:function(e){return"ප.ව."===e||"පස් වරු"===e},meridiem:function(e,t,n){return e>11?n?"ප.ව.":"පස් වරු":n?"පෙ.ව.":"පෙර වරු"}})}(n("wd/R"))},"8/+R":function(e,t,n){!function(e){"use strict";var t={1:"੧",2:"੨",3:"੩",4:"੪",5:"੫",6:"੬",7:"੭",8:"੮",9:"੯",0:"੦"},n={"੧":"1","੨":"2","੩":"3","੪":"4","੫":"5","੬":"6","੭":"7","੮":"8","੯":"9","੦":"0"};e.defineLocale("pa-in",{months:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),monthsShort:"ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ".split("_"),weekdays:"ਐਤਵਾਰ_ਸੋਮਵਾਰ_ਮੰਗਲਵਾਰ_ਬੁਧਵਾਰ_ਵੀਰਵਾਰ_ਸ਼ੁੱਕਰਵਾਰ_ਸ਼ਨੀਚਰਵਾਰ".split("_"),weekdaysShort:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),weekdaysMin:"ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ".split("_"),longDateFormat:{LT:"A h:mm ਵਜੇ",LTS:"A h:mm:ss ਵਜੇ",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm ਵਜੇ",LLLL:"dddd, D MMMM YYYY, A h:mm ਵਜੇ"},calendar:{sameDay:"[ਅਜ] LT",nextDay:"[ਕਲ] LT",nextWeek:"[ਅਗਲਾ] dddd, LT",lastDay:"[ਕਲ] LT",lastWeek:"[ਪਿਛਲੇ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ਵਿੱਚ",past:"%s ਪਿਛਲੇ",s:"ਕੁਝ ਸਕਿੰਟ",ss:"%d ਸਕਿੰਟ",m:"ਇਕ ਮਿੰਟ",mm:"%d ਮਿੰਟ",h:"ਇੱਕ ਘੰਟਾ",hh:"%d ਘੰਟੇ",d:"ਇੱਕ ਦਿਨ",dd:"%d ਦਿਨ",M:"ਇੱਕ ਮਹੀਨਾ",MM:"%d ਮਹੀਨੇ",y:"ਇੱਕ ਸਾਲ",yy:"%d ਸਾਲ"},preparse:function(e){return e.replace(/[੧੨੩੪੫੬੭੮੯੦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/ਰਾਤ|ਸਵੇਰ|ਦੁਪਹਿਰ|ਸ਼ਾਮ/,meridiemHour:function(e,t){return 12===e&&(e=0),"ਰਾਤ"===t?e<4?e:e+12:"ਸਵੇਰ"===t?e:"ਦੁਪਹਿਰ"===t?e>=10?e:e+12:"ਸ਼ਾਮ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"ਰਾਤ":e<10?"ਸਵੇਰ":e<17?"ਦੁਪਹਿਰ":e<20?"ਸ਼ਾਮ":"ਰਾਤ"},week:{dow:0,doy:6}})}(n("wd/R"))},"8mBD":function(e,t,n){!function(e){"use strict";e.defineLocale("pt",{months:"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"Domingo_Segunda-feira_Terça-feira_Quarta-feira_Quinta-feira_Sexta-feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Do_2ª_3ª_4ª_5ª_6ª_Sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return 0===this.day()||6===this.day()?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"segundos",ss:"%d segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",w:"uma semana",ww:"%d semanas",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(n("wd/R"))},"9hfn":function(e,t,n){"use strict";(function(e){function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],a=!0,r=!1,s=void 0;try{for(var i,o=e[Symbol.iterator]();!(a=(i=o.next()).done)&&(n.push(i.value),!t||n.length!==t);a=!0);}catch(e){r=!0,s=e}finally{try{a||null==o.return||o.return()}finally{if(r)throw s}}return n}(e,t)||o(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||o(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){if(e){if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}n.d(t,"a",(function(){return Et}));var u="asc",l="desc",_="none",c="records",m=[10,20,30,40,50],h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{};var f=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e,t){var n="[object Arguments]",a="[object Map]",r="[object Object]",s="[object Set]",i=/^\[object .+?Constructor\]$/,o=/^(?:0|[1-9]\d*)$/,d={};d["[object Float32Array]"]=d["[object Float64Array]"]=d["[object Int8Array]"]=d["[object Int16Array]"]=d["[object Int32Array]"]=d["[object Uint8Array]"]=d["[object Uint8ClampedArray]"]=d["[object Uint16Array]"]=d["[object Uint32Array]"]=!0,d[n]=d["[object Array]"]=d["[object ArrayBuffer]"]=d["[object Boolean]"]=d["[object DataView]"]=d["[object Date]"]=d["[object Error]"]=d["[object Function]"]=d[a]=d["[object Number]"]=d[r]=d["[object RegExp]"]=d[s]=d["[object String]"]=d["[object WeakMap]"]=!1;var u="object"==typeof h&&h&&h.Object===Object&&h,l="object"==typeof self&&self&&self.Object===Object&&self,_=u||l||Function("return this")(),c=t&&!t.nodeType&&t,m=c&&e&&!e.nodeType&&e,f=m&&m.exports===c,M=f&&u.process,p=function(){try{return M&&M.binding&&M.binding("util")}catch(e){}}(),y=p&&p.isTypedArray;function g(e,t){for(var n=-1,a=null==e?0:e.length;++n<a;)if(t(e[n],n,e))return!0;return!1}function L(e){var t=-1,n=Array(e.size);return e.forEach((function(e,a){n[++t]=[a,e]})),n}function Y(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}var w,b,k,v=Array.prototype,D=Function.prototype,T=Object.prototype,S=_["__core-js_shared__"],j=D.toString,x=T.hasOwnProperty,H=(w=/[^.]+$/.exec(S&&S.keys&&S.keys.IE_PROTO||""))?"Symbol(src)_1."+w:"",P=T.toString,O=RegExp("^"+j.call(x).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),C=f?_.Buffer:void 0,E=_.Symbol,R=_.Uint8Array,F=T.propertyIsEnumerable,W=v.splice,A=E?E.toStringTag:void 0,z=Object.getOwnPropertySymbols,N=C?C.isBuffer:void 0,I=(b=Object.keys,k=Object,function(e){return b(k(e))}),U=pe(_,"DataView"),J=pe(_,"Map"),B=pe(_,"Promise"),G=pe(_,"Set"),$=pe(_,"WeakMap"),V=pe(Object,"create"),q=Ye(U),Q=Ye(J),K=Ye(B),X=Ye(G),Z=Ye($),ee=E?E.prototype:void 0,te=ee?ee.valueOf:void 0;function ne(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}function ae(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}function re(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}function se(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new re;++t<n;)this.add(e[t])}function ie(e){var t=this.__data__=new ae(e);this.size=t.size}function oe(e,t){var n=ke(e),a=!n&&be(e),r=!n&&!a&&ve(e),s=!n&&!a&&!r&&xe(e),i=n||a||r||s,o=i?function(e,t){for(var n=-1,a=Array(e);++n<e;)a[n]=t(n);return a}(e.length,String):[],d=o.length;for(var u in e)!t&&!x.call(e,u)||i&&("length"==u||r&&("offset"==u||"parent"==u)||s&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Le(u,d))||o.push(u);return o}function de(e,t){for(var n=e.length;n--;)if(we(e[n][0],t))return n;return-1}function ue(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":A&&A in Object(e)?function(e){var t=x.call(e,A),n=e[A];try{e[A]=void 0;var a=!0}catch(e){}var r=P.call(e);a&&(t?e[A]=n:delete e[A]);return r}(e):function(e){return P.call(e)}(e)}function le(e){return je(e)&&ue(e)==n}function _e(e,t,i,o,d){return e===t||(null==e||null==t||!je(e)&&!je(t)?e!=e&&t!=t:function(e,t,i,o,d,u){var l=ke(e),_=ke(t),c=l?"[object Array]":ge(e),m=_?"[object Array]":ge(t),h=(c=c==n?r:c)==r,f=(m=m==n?r:m)==r,M=c==m;if(M&&ve(e)){if(!ve(t))return!1;l=!0,h=!1}if(M&&!h)return u||(u=new ie),l||xe(e)?he(e,t,i,o,d,u):function(e,t,n,r,i,o,d){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!o(new R(e),new R(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return we(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case a:var u=L;case s:var l=1&r;if(u||(u=Y),e.size!=t.size&&!l)return!1;var _=d.get(e);if(_)return _==t;r|=2,d.set(e,t);var c=he(u(e),u(t),r,i,o,d);return d.delete(e),c;case"[object Symbol]":if(te)return te.call(e)==te.call(t)}return!1}(e,t,c,i,o,d,u);if(!(1&i)){var p=h&&x.call(e,"__wrapped__"),y=f&&x.call(t,"__wrapped__");if(p||y){var g=p?e.value():e,w=y?t.value():t;return u||(u=new ie),d(g,w,i,o,u)}}if(!M)return!1;return u||(u=new ie),function(e,t,n,a,r,s){var i=1&n,o=fe(e),d=o.length,u=fe(t).length;if(d!=u&&!i)return!1;var l=d;for(;l--;){var _=o[l];if(!(i?_ in t:x.call(t,_)))return!1}var c=s.get(e);if(c&&s.get(t))return c==t;var m=!0;s.set(e,t),s.set(t,e);var h=i;for(;++l<d;){_=o[l];var f=e[_],M=t[_];if(a)var p=i?a(M,f,_,t,e,s):a(f,M,_,e,t,s);if(!(void 0===p?f===M||r(f,M,n,a,s):p)){m=!1;break}h||(h="constructor"==_)}if(m&&!h){var y=e.constructor,g=t.constructor;y==g||!("constructor"in e)||!("constructor"in t)||"function"==typeof y&&y instanceof y&&"function"==typeof g&&g instanceof g||(m=!1)}return s.delete(e),s.delete(t),m}(e,t,i,o,d,u)}(e,t,i,o,_e,d))}function ce(e){return!(!Se(e)||function(e){return!!H&&H in e}(e))&&(De(e)?O:i).test(Ye(e))}function me(e){if(n=(t=e)&&t.constructor,a="function"==typeof n&&n.prototype||T,t!==a)return I(e);var t,n,a,r=[];for(var s in Object(e))x.call(e,s)&&"constructor"!=s&&r.push(s);return r}function he(e,t,n,a,r,s){var i=1&n,o=e.length,d=t.length;if(o!=d&&!(i&&d>o))return!1;var u=s.get(e);if(u&&s.get(t))return u==t;var l=-1,_=!0,c=2&n?new se:void 0;for(s.set(e,t),s.set(t,e);++l<o;){var m=e[l],h=t[l];if(a)var f=i?a(h,m,l,t,e,s):a(m,h,l,e,t,s);if(void 0!==f){if(f)continue;_=!1;break}if(c){if(!g(t,(function(e,t){if(i=t,!c.has(i)&&(m===e||r(m,e,n,a,s)))return c.push(t);var i}))){_=!1;break}}else if(m!==h&&!r(m,h,n,a,s)){_=!1;break}}return s.delete(e),s.delete(t),_}function fe(e){return function(e,t,n){var a=t(e);return ke(e)?a:function(e,t){for(var n=-1,a=t.length,r=e.length;++n<a;)e[r+n]=t[n];return e}(a,n(e))}(e,He,ye)}function Me(e,t){var n,a,r=e.__data__;return("string"==(a=typeof(n=t))||"number"==a||"symbol"==a||"boolean"==a?"__proto__"!==n:null===n)?r["string"==typeof t?"string":"hash"]:r.map}function pe(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return ce(n)?n:void 0}ne.prototype.clear=function(){this.__data__=V?V(null):{},this.size=0},ne.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ne.prototype.get=function(e){var t=this.__data__;if(V){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return x.call(t,e)?t[e]:void 0},ne.prototype.has=function(e){var t=this.__data__;return V?void 0!==t[e]:x.call(t,e)},ne.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=V&&void 0===t?"__lodash_hash_undefined__":t,this},ae.prototype.clear=function(){this.__data__=[],this.size=0},ae.prototype.delete=function(e){var t=this.__data__,n=de(t,e);return!(n<0)&&(n==t.length-1?t.pop():W.call(t,n,1),--this.size,!0)},ae.prototype.get=function(e){var t=this.__data__,n=de(t,e);return n<0?void 0:t[n][1]},ae.prototype.has=function(e){return de(this.__data__,e)>-1},ae.prototype.set=function(e,t){var n=this.__data__,a=de(n,e);return a<0?(++this.size,n.push([e,t])):n[a][1]=t,this},re.prototype.clear=function(){this.size=0,this.__data__={hash:new ne,map:new(J||ae),string:new ne}},re.prototype.delete=function(e){var t=Me(this,e).delete(e);return this.size-=t?1:0,t},re.prototype.get=function(e){return Me(this,e).get(e)},re.prototype.has=function(e){return Me(this,e).has(e)},re.prototype.set=function(e,t){var n=Me(this,e),a=n.size;return n.set(e,t),this.size+=n.size==a?0:1,this},se.prototype.add=se.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},se.prototype.has=function(e){return this.__data__.has(e)},ie.prototype.clear=function(){this.__data__=new ae,this.size=0},ie.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},ie.prototype.get=function(e){return this.__data__.get(e)},ie.prototype.has=function(e){return this.__data__.has(e)},ie.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ae){var a=n.__data__;if(!J||a.length<199)return a.push([e,t]),this.size=++n.size,this;n=this.__data__=new re(a)}return n.set(e,t),this.size=n.size,this};var ye=z?function(e){return null==e?[]:(e=Object(e),function(e,t){for(var n=-1,a=null==e?0:e.length,r=0,s=[];++n<a;){var i=e[n];t(i,n,e)&&(s[r++]=i)}return s}(z(e),(function(t){return F.call(e,t)})))}:function(){return[]},ge=ue;function Le(e,t){return!!(t=null==t?9007199254740991:t)&&("number"==typeof e||o.test(e))&&e>-1&&e%1==0&&e<t}function Ye(e){if(null!=e){try{return j.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function we(e,t){return e===t||e!=e&&t!=t}(U&&"[object DataView]"!=ge(new U(new ArrayBuffer(1)))||J&&ge(new J)!=a||B&&"[object Promise]"!=ge(B.resolve())||G&&ge(new G)!=s||$&&"[object WeakMap]"!=ge(new $))&&(ge=function(e){var t=ue(e),n=t==r?e.constructor:void 0,i=n?Ye(n):"";if(i)switch(i){case q:return"[object DataView]";case Q:return a;case K:return"[object Promise]";case X:return s;case Z:return"[object WeakMap]"}return t});var be=le(function(){return arguments}())?le:function(e){return je(e)&&x.call(e,"callee")&&!F.call(e,"callee")},ke=Array.isArray;var ve=N||function(){return!1};function De(e){if(!Se(e))return!1;var t=ue(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Te(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function Se(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function je(e){return null!=e&&"object"==typeof e}var xe=y?function(e){return function(t){return e(t)}}(y):function(e){return je(e)&&Te(e.length)&&!!d[ue(e)]};function He(e){return null!=(t=e)&&Te(t.length)&&!De(t)?oe(e):me(e);var t}e.exports=function(e,t){return _e(e,t)}})),M={a:["a","à","á","â","ã","ä","å","æ","ā","ă","ą","ǎ","ǟ","ǡ","ǻ","ȁ","ȃ","ȧ","ɐ","ɑ","ɒ","ͣ","а","ӑ","ӓ","ᵃ","ᵄ","ᶏ","ḁ","ẚ","ạ","ả","ấ","ầ","ẩ","ẫ","ậ","ắ","ằ","ẳ","ẵ","ặ","ₐ","ⱥ","ａ"],b:["b","ƀ","ƃ","ɓ","ᖯ","ᵇ","ᵬ","ᶀ","ḃ","ḅ","ḇ","ｂ"],c:["c","ç","ć","ĉ","ċ","č","ƈ","ȼ","ɕ","ͨ","ᴄ","ᶜ","ḉ","ↄ","ｃ"],d:["d","ď","đ","Ƌ","ƌ","ȡ","ɖ","ɗ","ͩ","ᵈ","ᵭ","ᶁ","ᶑ","ḋ","ḍ","ḏ","ḑ","ḓ","ｄ"],e:["e","è","é","ê","ë","ē","ĕ","ė","ę","ě","ǝ","ȅ","ȇ","ȩ","ɇ","ɘ","ͤ","ᵉ","ᶒ","ḕ","ḗ","ḙ","ḛ","ḝ","ẹ","ẻ","ẽ","ế","ề","ể","ễ","ệ","ₑ","ｅ"],f:["f","ƒ","ᵮ","ᶂ","ᶠ","ḟ","ｆ"],g:["g","ĝ","ğ","ġ","ģ","ǥ","ǧ","ǵ","ɠ","ɡ","ᵍ","ᵷ","ᵹ","ᶃ","ᶢ","ḡ","ｇ"],h:["h","ĥ","ħ","ƕ","ȟ","ɥ","ɦ","ʮ","ʯ","ʰ","ʱ","ͪ","Һ","һ","ᑋ","ᶣ","ḣ","ḥ","ḧ","ḩ","ḫ","ⱨ","ｈ"],i:["i","ì","í","î","ï","ĩ","ī","ĭ","į","ǐ","ȉ","ȋ","ɨ","ͥ","ᴉ","ᵎ","ᵢ","ᶖ","ᶤ","ḭ","ḯ","ỉ","ị","ｉ"],j:["j","ĵ","ǰ","ɉ","ʝ","ʲ","ᶡ","ᶨ","ｊ"],k:["k","ķ","ƙ","ǩ","ʞ","ᵏ","ᶄ","ḱ","ḳ","ḵ","ⱪ","ｋ"],l:["l","ĺ","ļ","ľ","ŀ","ł","ƚ","ȴ","ɫ","ɬ","ɭ","ˡ","ᶅ","ᶩ","ᶪ","ḷ","ḹ","ḻ","ḽ","ℓ","ⱡ"],m:["m","ɯ","ɰ","ɱ","ͫ","ᴟ","ᵐ","ᵚ","ᵯ","ᶆ","ᶬ","ᶭ","ḿ","ṁ","ṃ","㎡","㎥","ｍ"],n:["n","ñ","ń","ņ","ň","ŉ","ƞ","ǹ","ȵ","ɲ","ɳ","ᵰ","ᶇ","ᶮ","ᶯ","ṅ","ṇ","ṉ","ṋ","ⁿ","ｎ"],o:["o","ò","ó","ô","õ","ö","ø","ō","ŏ","ő","ơ","ǒ","ǫ","ǭ","ǿ","ȍ","ȏ","ȫ","ȭ","ȯ","ȱ","ɵ","ͦ","о","ӧ","ө","ᴏ","ᴑ","ᴓ","ᴼ","ᵒ","ᶱ","ṍ","ṏ","ṑ","ṓ","ọ","ỏ","ố","ồ","ổ","ỗ","ộ","ớ","ờ","ở","ỡ","ợ","ₒ","ｏ","𐐬"],p:["p","ᵖ","ᵱ","ᵽ","ᶈ","ṕ","ṗ","ｐ"],q:["q","ɋ","ʠ","ᛩ","ｑ"],r:["r","ŕ","ŗ","ř","ȑ","ȓ","ɍ","ɹ","ɻ","ʳ","ʴ","ʵ","ͬ","ᵣ","ᵲ","ᶉ","ṙ","ṛ","ṝ","ṟ"],s:["s","ś","ŝ","ş","š","ș","ʂ","ᔆ","ᶊ","ṡ","ṣ","ṥ","ṧ","ṩ","ｓ"],t:["t","ţ","ť","ŧ","ƫ","ƭ","ț","ʇ","ͭ","ᵀ","ᵗ","ᵵ","ᶵ","ṫ","ṭ","ṯ","ṱ","ẗ","ｔ"],u:["u","ù","ú","û","ü","ũ","ū","ŭ","ů","ű","ų","ư","ǔ","ǖ","ǘ","ǚ","ǜ","ȕ","ȗ","ͧ","ߎ","ᵘ","ᵤ","ṳ","ṵ","ṷ","ṹ","ṻ","ụ","ủ","ứ","ừ","ử","ữ","ự","ｕ"],v:["v","ʋ","ͮ","ᵛ","ᵥ","ᶹ","ṽ","ṿ","ⱱ","ｖ","ⱴ"],w:["w","ŵ","ʷ","ᵂ","ẁ","ẃ","ẅ","ẇ","ẉ","ẘ","ⱳ","ｗ"],x:["x","̽","͓","ᶍ","ͯ","ẋ","ẍ","ₓ","ｘ"],y:["y","ý","ÿ","ŷ","ȳ","ɏ","ʸ","ẏ","ỳ","ỵ","ỷ","ỹ","ｙ"],z:["z","ź","ż","ž","ƶ","ȥ","ɀ","ʐ","ʑ","ᙆ","ᙇ","ᶻ","ᶼ","ᶽ","ẑ","ẓ","ẕ","ⱬ","ｚ"]},p=function(){var e={};for(var t in M){var n=M[t];for(var a in n){var r=n[a];r!==t&&(e[r]=t)}}return e}(),y=/[^a-z0-9\s,.-]/,g=function(e){if(-1===e.search(y))return e;for(var t="",n=e.length,a=0;a<n;a++){var r=e.charAt(a);t+=r in p?p[r]:r}return t},L=function(e){return e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")},Y={format:function(e){return e},filterPredicate:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(null==e)return!1;var r=n?String(e).toLowerCase():g(L(String(e)).toLowerCase()),s=n?t.toLowerCase():g(L(t).toLowerCase());return a?r===s:r.indexOf(s)>-1},compare:function(e,t){function n(e){return null==e?"":g(String(e).toLowerCase())}return(e=n(e))<(t=n(t))?-1:e>t?1:0}};function w(e,t,n,a,r,s,i,o,d,u){"boolean"!=typeof i&&(d=o,o=i,i=!1);const l="function"==typeof n?n.options:n;let _;if(e&&e.render&&(l.render=e.render,l.staticRenderFns=e.staticRenderFns,l._compiled=!0,r&&(l.functional=!0)),a&&(l._scopeId=a),s?(_=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,d(e)),e&&e._registeredComponents&&e._registeredComponents.add(s)},l._ssrRegister=_):t&&(_=i?function(e){t.call(this,u(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,o(e))}),_)if(l.functional){const e=l.render;l.render=function(t,n){return _.call(n),e(t,n)}}else{const e=l.beforeCreate;l.beforeCreate=e?[].concat(e,_):[_]}return n}var b=w({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"vgt-wrap__footer vgt-clearfix"},[e.perPageDropdownEnabled?n("div",{staticClass:"footer__row-count vgt-pull-left"},[n("form",[n("label",{staticClass:"footer__row-count__label",attrs:{for:e.id}},[e._v(e._s(e.rowsPerPageText)+":")]),e._v(" "),n("select",{directives:[{name:"model",rawName:"v-model",value:e.currentPerPage,expression:"currentPerPage"}],staticClass:"footer__row-count__select",attrs:{id:e.id,autocomplete:"off",name:"perPageSelect","aria-controls":"vgt-table"},on:{change:[function(t){var n=Array.prototype.filter.call(t.target.options,(function(e){return e.selected})).map((function(e){return"_value"in e?e._value:e.value}));e.currentPerPage=t.target.multiple?n:n[0]},e.perPageChanged]}},[e._l(e.rowsPerPageOptions,(function(t,a){return n("option",{key:a,domProps:{value:t}},[e._v("\n          "+e._s(t)+"\n        ")])})),e._v(" "),e.paginateDropdownAllowAll?n("option",{domProps:{value:-1}},[e._v(e._s(e.allText))]):e._e()],2)])]):e._e(),e._v(" "),n("div",{staticClass:"footer__navigation vgt-pull-right"},[n("pagination-page-info",{attrs:{"total-records":e.total,"last-page":e.pagesCount,"current-page":e.currentPage,"current-per-page":e.currentPerPage,"of-text":e.ofText,"page-text":e.pageText,"info-fn":e.infoFn,mode:e.mode},on:{"page-changed":e.changePage}}),e._v(" "),e.jumpFirstOrLast?n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.firstIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.firstPage(t)}}},[n("span",{staticClass:"chevron",class:{left:!e.rtl,right:e.rtl},attrs:{"aria-hidden":"true"}}),e._v(" "),n("span",[e._v(e._s(e.firstText))])]):e._e(),e._v(" "),n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.prevIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.previousPage(t)}}},[n("span",{staticClass:"chevron",class:{left:!e.rtl,right:e.rtl},attrs:{"aria-hidden":"true"}}),e._v(" "),n("span",[e._v(e._s(e.prevText))])]),e._v(" "),n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.nextIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.nextPage(t)}}},[n("span",[e._v(e._s(e.nextText))]),e._v(" "),n("span",{staticClass:"chevron",class:{right:!e.rtl,left:e.rtl},attrs:{"aria-hidden":"true"}})]),e._v(" "),e.jumpFirstOrLast?n("button",{staticClass:"footer__navigation__page-btn",class:{disabled:!e.lastIsPossible},attrs:{type:"button","aria-controls":"vgt-table"},on:{click:function(t){return t.preventDefault(),t.stopPropagation(),e.lastPage(t)}}},[n("span",[e._v(e._s(e.lastText))]),e._v(" "),n("span",{staticClass:"chevron",class:{right:!e.rtl,left:e.rtl},attrs:{"aria-hidden":"true"}})]):e._e()],1)])},staticRenderFns:[]},void 0,{name:"VgtPagination",props:{styleClass:{default:"table table-bordered"},total:{default:null},perPage:{},rtl:{default:!1},perPageDropdownEnabled:{default:!0},customRowsPerPageDropdown:{default:function(){return[]}},paginateDropdownAllowAll:{default:!0},mode:{default:c},jumpFirstOrLast:{default:!1},firstText:{default:"First"},lastText:{default:"Last"},nextText:{default:"Next"},prevText:{default:"Prev"},rowsPerPageText:{default:"Rows per page:"},ofText:{default:"of"},pageText:{default:"page"},allText:{default:"All"},infoFn:{default:null}},data:function(){return{id:this.getId(),currentPage:1,prevPage:0,currentPerPage:10,rowsPerPageOptions:[]}},watch:{perPage:{handler:function(e,t){this.handlePerPage(),this.perPageChanged(t)},immediate:!0},customRowsPerPageDropdown:function(){this.handlePerPage()},total:{handler:function(e,t){-1===this.rowsPerPageOptions.indexOf(this.currentPerPage)&&(this.currentPerPage=e)}}},computed:{pagesCount:function(){if(-1===this.currentPerPage)return 1;var e=Math.floor(this.total/this.currentPerPage);return 0===this.total%this.currentPerPage?e:e+1},firstIsPossible:function(){return this.currentPage>1},lastIsPossible:function(){return this.currentPage<Math.ceil(this.total/this.currentPerPage)},nextIsPossible:function(){return this.currentPage<this.pagesCount},prevIsPossible:function(){return this.currentPage>1}},methods:{getId:function(){return"vgt-select-rpp-".concat(Math.floor(Math.random()*Date.now()))},changePage:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];e>0&&this.total>this.currentPerPage*(e-1)&&(this.prevPage=this.currentPage,this.currentPage=e,this.pageChanged(t))},firstPage:function(){this.firstIsPossible&&(this.currentPage=1,this.prevPage=0,this.pageChanged())},lastPage:function(){this.lastIsPossible&&(this.currentPage=this.pagesCount,this.prev=this.currentPage-1,this.pageChanged())},nextPage:function(){this.nextIsPossible&&(this.prevPage=this.currentPage,++this.currentPage,this.pageChanged())},previousPage:function(){this.prevIsPossible&&(this.prevPage=this.currentPage,--this.currentPage,this.pageChanged())},pageChanged:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={currentPage:this.currentPage,prevPage:this.prevPage};e||(t.noEmit=!0),this.$emit("page-changed",t)},perPageChanged:function(e){e&&this.$emit("per-page-changed",{currentPerPage:this.currentPerPage}),this.changePage(1,!1)},handlePerPage:function(){if(null!==this.customRowsPerPageDropdown&&Array.isArray(this.customRowsPerPageDropdown)&&0!==this.customRowsPerPageDropdown.length?this.rowsPerPageOptions=JSON.parse(JSON.stringify(this.customRowsPerPageDropdown)):this.rowsPerPageOptions=JSON.parse(JSON.stringify(m)),this.perPage){this.currentPerPage=this.perPage;for(var e=!1,t=0;t<this.rowsPerPageOptions.length;t++)this.rowsPerPageOptions[t]===this.perPage&&(e=!0);e||-1===this.perPage||this.rowsPerPageOptions.unshift(this.perPage)}else this.currentPerPage=10}},mounted:function(){},components:{"pagination-page-info":w({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"footer__navigation__page-info"},[e.infoFn?n("div",[e._v("\n    "+e._s(e.infoFn(e.infoParams))+"\n  ")]):"pages"===e.mode?n("form",{on:{submit:function(e){e.preventDefault()}}},[n("label",{staticClass:"page-info__label",attrs:{for:e.id}},[n("span",[e._v(e._s(e.pageText))]),e._v(" "),n("input",{staticClass:"footer__navigation__page-info__current-entry",attrs:{id:e.id,"aria-describedby":"change-page-hint","aria-controls":"vgb-table",type:"text"},domProps:{value:e.currentPage},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.stopPropagation(),e.changePage(t))}}}),e._v(" "),n("span",[e._v(e._s(e.pageInfo))])]),e._v(" "),n("span",{staticStyle:{display:"none"},attrs:{id:"change-page-hint"}},[e._v("\n      Type a page number and press Enter to change the page.\n    ")])]):n("div",[e._v("\n    "+e._s(e.recordInfo)+"\n  ")])])},staticRenderFns:[]},void 0,{name:"VgtPaginationPageInfo",props:{currentPage:{default:1},lastPage:{default:1},totalRecords:{default:0},ofText:{default:"of",type:String},pageText:{default:"page",type:String},currentPerPage:{},mode:{default:c},infoFn:{default:null}},data:function(){return{id:this.getId()}},computed:{pageInfo:function(){return"".concat(this.ofText," ").concat(this.lastPage)},firstRecordOnPage:function(){return(this.currentPage-1)*this.currentPerPage+1},lastRecordOnPage:function(){return-1===this.currentPerPage?this.totalRecords:Math.min(this.totalRecords,this.currentPage*this.currentPerPage)},recordInfo:function(){var e=this.firstRecordOnPage,t=this.lastRecordOnPage;return 0===t&&(e=0),"".concat(e," - ").concat(t," ").concat(this.ofText," ").concat(this.totalRecords)},infoParams:function(){var e=this.firstRecordOnPage,t=this.lastRecordOnPage;return 0===t&&(e=0),{firstRecordOnPage:e,lastRecordOnPage:t,totalRecords:this.totalRecords,currentPage:this.currentPage,totalPage:this.lastPage}}},methods:{getId:function(){return"vgt-page-input-".concat(Math.floor(Math.random()*Date.now()))},changePage:function(e){var t=parseInt(e.target.value,10);if(Number.isNaN(t)||t>this.lastPage||t<1)return e.target.value=this.currentPage,!1;e.target.value=t,this.$emit("page-changed",t)}},mounted:function(){},components:{}},"data-v-347cbcfa",!1,void 0,!1,void 0,void 0,void 0)}},void 0,!1,void 0,!1,void 0,void 0,void 0),k=w({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.showControlBar?n("div",{staticClass:"vgt-global-search vgt-clearfix"},[n("div",{staticClass:"vgt-global-search__input vgt-pull-left"},[e.searchEnabled?n("form",{attrs:{role:"search"},on:{submit:function(e){e.preventDefault()}}},[n("label",{attrs:{for:e.id}},[e._m(0),e._v(" "),n("span",{staticClass:"sr-only"},[e._v("Search")])]),e._v(" "),n("input",{staticClass:"vgt-input vgt-pull-left",attrs:{id:e.id,type:"text",placeholder:e.globalSearchPlaceholder},domProps:{value:e.value},on:{input:function(t){return e.updateValue(t.target.value)},keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.entered(t.target.value)}}})]):e._e()]),e._v(" "),n("div",{staticClass:"vgt-global-search__actions vgt-pull-right"},[e._t("internal-table-actions")],2)]):e._e()},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("span",{staticClass:"input__icon",attrs:{"aria-hidden":"true"}},[t("div",{staticClass:"magnifying-glass"})])}]},void 0,{name:"VgtGlobalSearch",props:["value","searchEnabled","globalSearchPlaceholder"],data:function(){return{globalSearchTerm:null,id:this.getId()}},computed:{showControlBar:function(){return!!this.searchEnabled||!(!this.$slots||!this.$slots["internal-table-actions"])}},methods:{updateValue:function(e){this.$emit("input",e),this.$emit("on-keyup",e)},entered:function(e){this.$emit("on-enter",e)},getId:function(){return"vgt-search-".concat(Math.floor(Math.random()*Date.now()))}}},void 0,!1,void 0,!1,void 0,void 0,void 0);function v(e){return e.firstSortType||"asc"}function D(e,t){return l===v(t)&&e===u?_:e===u?l:l===v(t)&&e===l?u:e===l?_:l===v(t)&&e===_?l:u}var T=w({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("thead",[n("tr",[e.lineNumbers?n("th",{staticClass:"line-numbers",attrs:{scope:"col"}}):e._e(),e._v(" "),e.selectable?n("th",{staticClass:"vgt-checkbox-col",attrs:{scope:"col"}},[n("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected,indeterminate:e.allSelectedIndeterminate},on:{change:e.toggleSelectAll}})]):e._e(),e._v(" "),e._l(e.columns,(function(t,a){return t.hidden?e._e():n("th",{key:a,class:e.getHeaderClasses(t,a),style:e.columnStyles[a],attrs:{scope:"col",title:t.tooltip,"aria-sort":e.getColumnSortLong(t),"aria-controls":"col-"+a}},[e._t("table-column",[e._v("\n        "+e._s(t.label)+"\n      ")],{column:t}),e._v(" "),e.isSortableColumn(t)?n("button",{on:{click:function(n){return e.sort(n,t)}}},[n("span",{staticClass:"sr-only"},[e._v("\n          Sort table by "+e._s(t.label)+" in "+e._s(e.getColumnSortLong(t))+" order\n          ")])]):e._e()],2)}))],2),e._v(" "),n("vgt-filter-row",{ref:"filter-row",tag:"tr",attrs:{"global-search-enabled":e.searchEnabled,"line-numbers":e.lineNumbers,selectable:e.selectable,columns:e.columns,mode:e.mode,"typed-columns":e.typedColumns},on:{"filter-changed":e.filterRows},scopedSlots:e._u([{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)})],1)},staticRenderFns:[]},void 0,{name:"VgtTableHeader",props:{lineNumbers:{default:!1,type:Boolean},selectable:{default:!1,type:Boolean},allSelected:{default:!1,type:Boolean},allSelectedIndeterminate:{default:!1,type:Boolean},columns:{type:Array},mode:{type:String},typedColumns:{},sortable:{type:Boolean},multipleColumnSort:{type:Boolean,default:!0},getClasses:{type:Function},searchEnabled:{type:Boolean},tableRef:{},paginated:{}},watch:{columns:{handler:function(){this.setColumnStyles()},immediate:!0},tableRef:{handler:function(){this.setColumnStyles()},immediate:!0},paginated:{handler:function(){this.tableRef&&this.setColumnStyles()},deep:!0}},data:function(){return{checkBoxThStyle:{},lineNumberThStyle:{},columnStyles:[],sorts:[],ro:null}},computed:{},methods:{reset:function(){this.$refs["filter-row"].reset(!0)},toggleSelectAll:function(){this.$emit("on-toggle-select-all")},isSortableColumn:function(e){var t=e.sortable;return"boolean"==typeof t?t:this.sortable},sort:function(e,t){this.isSortableColumn(t)&&(e.shiftKey&&this.multipleColumnSort?this.sorts=function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++)if(t.field===e[n].field)return n;return-1}(e,t);return-1===n?e.push({field:t.field,type:v(t)}):e[n].type=D(e[n].type,t),e}(this.sorts,t):this.sorts=function(e,t){var n=function(e,t){return 1===e.length&&e[0].field===t.field?e[0].type:void 0}(e,t),a=D(n,t);return[{field:t.field,type:n?a:v(t)}]}(this.sorts,t),this.$emit("on-sort-change",this.sorts))},setInitialSort:function(e){this.sorts=e,this.$emit("on-sort-change",this.sorts)},getColumnSort:function(e){for(var t=0;t<this.sorts.length;t+=1)if(this.sorts[t].field===e.field)return this.sorts[t].type||"asc";return null},getColumnSortLong:function(e){return"asc"===this.getColumnSort(e)?"ascending":"descending"},getHeaderClasses:function(e,t){return Object.assign({},this.getClasses(t,"th"),{sortable:this.isSortableColumn(e),"sorting sorting-desc":"desc"===this.getColumnSort(e),"sorting sorting-asc":"asc"===this.getColumnSort(e)})},filterRows:function(e){this.$emit("filter-changed",e)},getWidthStyle:function(e){return window&&window.getComputedStyle&&e?{width:window.getComputedStyle(e,null).width}:{width:"auto"}},setColumnStyles:function(){for(var e=[],t=0;t<this.columns.length;t++)if(this.tableRef){var n=0;this.selectable&&n++,this.lineNumbers&&n++;var a=this.tableRef.rows[0].cells[t+n];e.push(this.getWidthStyle(a))}else e.push({minWidth:this.columns[t].width?this.columns[t].width:"auto",maxWidth:this.columns[t].width?this.columns[t].width:"auto",width:this.columns[t].width?this.columns[t].width:"auto"});this.columnStyles=e},getColumnStyle:function(e,t){var n={minWidth:e.width?e.width:"auto",maxWidth:e.width?e.width:"auto",width:e.width?e.width:"auto"};if(this.tableRef){this.selectable&&t++,this.lineNumbers&&t++;var a=this.tableRef.rows[0].cells[t],r=window.getComputedStyle(a,null);n.width=r.width}return n}},mounted:function(){var e=this;this.$nextTick((function(){"ResizeObserver"in window&&(e.ro=new ResizeObserver((function(){e.setColumnStyles()})),e.ro.observe(e.$parent.$el),e.tableRef&&Array.from(e.$parent.$refs["table-header-primary"].$el.children[0].children).forEach((function(t){e.ro.observe(t)})))}))},beforeDestroy:function(){this.ro&&this.ro.disconnect()},components:{"vgt-filter-row":w({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.hasFilterRow?n("tr",[e.lineNumbers?n("th"):e._e(),e._v(" "),e.selectable?n("th"):e._e(),e._v(" "),e._l(e.columns,(function(t,a){return t.hidden?e._e():n("th",{key:a,class:e.getClasses(t)},[e._t("column-filter",[e.isFilterable(t)?n("div",[e.isDropdown(t)?e._e():n("input",{staticClass:"vgt-input",attrs:{name:e.getName(t),type:"text",placeholder:e.getPlaceholder(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{keyup:function(n){return!n.type.indexOf("key")&&e._k(n.keyCode,"enter",13,n.key,"Enter")?null:e.updateFiltersOnEnter(t,n.target.value)},input:function(n){return e.updateFiltersOnKeyup(t,n.target.value)}}}),e._v(" "),e.isDropdownArray(t)?n("select",{staticClass:"vgt-select",attrs:{name:e.getName(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{change:function(n){return e.updateFiltersImmediately(t.field,n.target.value)}}},[n("option",{key:"-1",attrs:{value:""}},[e._v(e._s(e.getPlaceholder(t)))]),e._v(" "),e._l(t.filterOptions.filterDropdownItems,(function(t,a){return n("option",{key:a,domProps:{value:t}},[e._v("\n              "+e._s(t)+"\n            ")])}))],2):e._e(),e._v(" "),e.isDropdownObjects(t)?n("select",{staticClass:"vgt-select",attrs:{name:e.getName(t)},domProps:{value:e.columnFilters[e.fieldKey(t.field)]},on:{change:function(n){return e.updateFiltersImmediately(t.field,n.target.value)}}},[n("option",{key:"-1",attrs:{value:""}},[e._v(e._s(e.getPlaceholder(t)))]),e._v(" "),e._l(t.filterOptions.filterDropdownItems,(function(t,a){return n("option",{key:a,domProps:{value:t.value}},[e._v(e._s(t.text))])}))],2):e._e()]):e._e()],{column:t,updateFilters:e.updateSlotFilter})],2)}))],2):e._e()},staticRenderFns:[]},void 0,{name:"VgtFilterRow",props:["lineNumbers","columns","typedColumns","globalSearchEnabled","selectable","mode"],watch:{columns:{handler:function(e,t){this.populateInitialFilters()},deep:!0,immediate:!0}},data:function(){return{columnFilters:{},timer:null}},computed:{hasFilterRow:function(){for(var e=0;e<this.columns.length;e++){var t=this.columns[e];if(t.filterOptions&&t.filterOptions.enabled)return!0}return!1}},methods:{fieldKey:function(e){return"function"==typeof e&&e.name?e.name:e},reset:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.columnFilters={},e&&this.$emit("filter-changed",this.columnFilters)},isFilterable:function(e){return e.filterOptions&&e.filterOptions.enabled},isDropdown:function(e){return this.isFilterable(e)&&e.filterOptions.filterDropdownItems&&e.filterOptions.filterDropdownItems.length},isDropdownObjects:function(e){return this.isDropdown(e)&&"object"===a(e.filterOptions.filterDropdownItems[0])},isDropdownArray:function(e){return this.isDropdown(e)&&"object"!==a(e.filterOptions.filterDropdownItems[0])},getClasses:function(e){var t="filter-th";return e.filterOptions&&e.filterOptions.styleClass?[t].concat(i(e.filterOptions.styleClass.split(" "))).join(" "):t},getPlaceholder:function(e){return this.isFilterable(e)&&e.filterOptions.placeholder||"Filter ".concat(e.label)},getName:function(e){return"vgt-".concat(this.fieldKey(e.field))},updateFiltersOnEnter:function(e,t){this.timer&&clearTimeout(this.timer),this.updateFiltersImmediately(e.field,t)},updateFiltersOnKeyup:function(e,t){"enter"!==e.filterOptions.trigger&&this.updateFilters(e,t)},updateSlotFilter:function(e,t){var n=e.filterOptions.slotFilterField||e.field;"function"==typeof e.filterOptions.formatValue&&(t=e.filterOptions.formatValue(t)),this.updateFiltersImmediately(n,t)},updateFilters:function(e,t){var n=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout((function(){n.updateFiltersImmediately(e.field,t)}),400)},updateFiltersImmediately:function(e,t){this.$set(this.columnFilters,this.fieldKey(e),t),this.$emit("filter-changed",this.columnFilters)},populateInitialFilters:function(){for(var e=0;e<this.columns.length;e++){var t=this.columns[e];this.isFilterable(t)&&void 0!==t.filterOptions.filterValue&&null!==t.filterOptions.filterValue&&this.$set(this.columnFilters,this.fieldKey(t.field),t.filterOptions.filterValue)}this.$emit("filter-changed",this.columnFilters)}}},"data-v-6869bf1c",!1,void 0,!1,void 0,void 0,void 0)}},void 0,!1,void 0,!1,void 0,void 0,void 0),S=w({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("tr",["span"===e.headerRow.mode?n("th",{staticClass:"vgt-left-align vgt-row-header",attrs:{colspan:e.fullColspan}},[e.selectAllByGroup?[e._t("table-header-group-select",[n("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected},on:{change:function(t){return e.toggleSelectGroup(t)}}})],{columns:e.columns,row:e.headerRow})]:e._e(),e._v(" "),n("span",{on:{click:function(t){e.collapsable&&e.$emit("vgtExpand",!e.headerRow.vgtIsExpanded)}}},[e.collapsable?n("span",{staticClass:"triangle",class:{expand:e.headerRow.vgtIsExpanded}}):e._e(),e._v(" "),e._t("table-header-row",[e.headerRow.html?n("span",{domProps:{innerHTML:e._s(e.headerRow.label)}}):n("span",[e._v("\n          "+e._s(e.headerRow.label)+"\n        ")])],{row:e.headerRow})],2)],2):e._e(),e._v(" "),"span"!==e.headerRow.mode&&e.lineNumbers?n("th",{staticClass:"vgt-row-header"}):e._e(),e._v(" "),"span"!==e.headerRow.mode&&e.selectable?n("th",{staticClass:"vgt-row-header"},[e.selectAllByGroup?[e._t("table-header-group-select",[n("input",{attrs:{type:"checkbox"},domProps:{checked:e.allSelected},on:{change:function(t){return e.toggleSelectGroup(t)}}})],{columns:e.columns,row:e.headerRow})]:e._e()],2):e._e(),e._v(" "),e._l(e.columns,(function(t,a){return"span"===e.headerRow.mode||t.hidden?e._e():n("th",{key:a,staticClass:"vgt-row-header",class:e.getClasses(a,"td"),on:{click:function(t){e.columnCollapsable(a)&&e.$emit("vgtExpand",!e.headerRow.vgtIsExpanded)}}},[e.columnCollapsable(a)?n("span",{staticClass:"triangle",class:{expand:e.headerRow.vgtIsExpanded}}):e._e(),e._v(" "),e._t("table-header-row",[t.html?e._e():n("span",[e._v("\n        "+e._s(e.collectFormatted(e.headerRow,t,!0))+"\n      ")]),e._v(" "),t.html?n("span",{domProps:{innerHTML:e._s(e.collectFormatted(e.headerRow,t,!0))}}):e._e()],{row:e.headerRow,column:t,formattedRow:e.formattedRow(e.headerRow,!0)})],2)}))],2)},staticRenderFns:[]},void 0,{name:"VgtHeaderRow",props:{headerRow:{type:Object},columns:{type:Array},lineNumbers:{type:Boolean},selectable:{type:Boolean},selectAllByGroup:{type:Boolean},collapsable:{type:[Boolean,Number],default:!1},collectFormatted:{type:Function},formattedRow:{type:Function},getClasses:{type:Function},fullColspan:{type:Number},groupIndex:{type:Number}},data:function(){return{}},computed:{allSelected:function(){var e=this.headerRow;this.groupChildObject;return e.children.filter((function(e){return e.vgtSelected})).length===e.children.length}},methods:{columnCollapsable:function(e){return!0===this.collapsable?0===e:e===this.collapsable},toggleSelectGroup:function(e){this.$emit("on-select-group-change",{groupIndex:this.groupIndex,checked:e.target.checked})}},mounted:function(){},components:{}},void 0,!1,void 0,!1,void 0,void 0,void 0);function j(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}function x(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function H(e){x(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):("string"!=typeof e&&"[object String]"!==t||"undefined"==typeof console||(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://git.io/fjule"),console.warn((new Error).stack)),new Date(NaN))}function P(e,t){x(2,arguments);var n=H(e).getTime(),a=j(t);return new Date(n+a)}function O(e){return e.getTime()%6e4}function C(e){var t=new Date(e.getTime()),n=Math.ceil(t.getTimezoneOffset());return t.setSeconds(0,0),6e4*n+(n>0?(6e4+O(t))%6e4:O(t))}function E(e){x(1,arguments);var t=H(e);return!isNaN(t)}var R={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function F(e){return function(t){var n=t||{},a=n.width?String(n.width):e.defaultWidth;return e.formats[a]||e.formats[e.defaultWidth]}}var W={date:F({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:F({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:F({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},A={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function z(e){return function(t,n){var a,r=n||{};if("formatting"===(r.context?String(r.context):"standalone")&&e.formattingValues){var s=e.defaultFormattingWidth||e.defaultWidth,i=r.width?String(r.width):s;a=e.formattingValues[i]||e.formattingValues[s]}else{var o=e.defaultWidth,d=r.width?String(r.width):e.defaultWidth;a=e.values[d]||e.values[o]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function N(e){return function(t,n){var a=String(t),r=n||{},s=r.width,i=s&&e.matchPatterns[s]||e.matchPatterns[e.defaultMatchWidth],o=a.match(i);if(!o)return null;var d,u=o[0],l=s&&e.parsePatterns[s]||e.parsePatterns[e.defaultParseWidth];return d="[object Array]"===Object.prototype.toString.call(l)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(l,(function(e){return e.test(u)})):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(l,(function(e){return e.test(u)})),d=e.valueCallback?e.valueCallback(d):d,{value:d=r.valueCallback?r.valueCallback(d):d,rest:a.slice(u.length)}}}var I,U={code:"en-US",formatDistance:function(e,t,n){var a;return n=n||{},a="string"==typeof R[e]?R[e]:1===t?R[e].one:R[e].other.replace("{{count}}",t),n.addSuffix?n.comparison>0?"in "+a:a+" ago":a},formatLong:W,formatRelative:function(e,t,n,a){return A[e]},localize:{ordinalNumber:function(e,t){var n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:z({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:z({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return Number(e)-1}}),month:z({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:z({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:z({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(I={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}},function(e,t){var n=String(e),a=t||{},r=n.match(I.matchPattern);if(!r)return null;var s=r[0],i=n.match(I.parsePattern);if(!i)return null;var o=I.valueCallback?I.valueCallback(i[0]):i[0];return{value:o=a.valueCallback?a.valueCallback(o):o,rest:n.slice(s.length)}}),era:N({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:N({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:N({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:N({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:N({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function J(e,t){x(2,arguments);var n=j(t);return P(e,-n)}function B(e,t){for(var n=e<0?"-":"",a=Math.abs(e).toString();a.length<t;)a="0"+a;return n+a}var G=function(e,t){var n=e.getUTCFullYear(),a=n>0?n:1-n;return B("yy"===t?a%100:a,t.length)},$=function(e,t){var n=e.getUTCMonth();return"M"===t?String(n+1):B(n+1,2)},V=function(e,t){return B(e.getUTCDate(),t.length)},q=function(e,t){return B(e.getUTCHours()%12||12,t.length)},Q=function(e,t){return B(e.getUTCHours(),t.length)},K=function(e,t){return B(e.getUTCMinutes(),t.length)},X=function(e,t){return B(e.getUTCSeconds(),t.length)},Z=function(e,t){var n=t.length,a=e.getUTCMilliseconds();return B(Math.floor(a*Math.pow(10,n-3)),t.length)};function ee(e){x(1,arguments);var t=1,n=H(e),a=n.getUTCDay(),r=(a<t?7:0)+a-t;return n.setUTCDate(n.getUTCDate()-r),n.setUTCHours(0,0,0,0),n}function te(e){x(1,arguments);var t=H(e),n=t.getUTCFullYear(),a=new Date(0);a.setUTCFullYear(n+1,0,4),a.setUTCHours(0,0,0,0);var r=ee(a),s=new Date(0);s.setUTCFullYear(n,0,4),s.setUTCHours(0,0,0,0);var i=ee(s);return t.getTime()>=r.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function ne(e){x(1,arguments);var t=te(e),n=new Date(0);n.setUTCFullYear(t,0,4),n.setUTCHours(0,0,0,0);var a=ee(n);return a}function ae(e){x(1,arguments);var t=H(e),n=ee(t).getTime()-ne(t).getTime();return Math.round(n/6048e5)+1}function re(e,t){x(1,arguments);var n=t||{},a=n.locale,r=a&&a.options&&a.options.weekStartsOn,s=null==r?0:j(r),i=null==n.weekStartsOn?s:j(n.weekStartsOn);if(!(i>=0&&i<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var o=H(e),d=o.getUTCDay(),u=(d<i?7:0)+d-i;return o.setUTCDate(o.getUTCDate()-u),o.setUTCHours(0,0,0,0),o}function se(e,t){x(1,arguments);var n=H(e,t),a=n.getUTCFullYear(),r=t||{},s=r.locale,i=s&&s.options&&s.options.firstWeekContainsDate,o=null==i?1:j(i),d=null==r.firstWeekContainsDate?o:j(r.firstWeekContainsDate);if(!(d>=1&&d<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var u=new Date(0);u.setUTCFullYear(a+1,0,d),u.setUTCHours(0,0,0,0);var l=re(u,t),_=new Date(0);_.setUTCFullYear(a,0,d),_.setUTCHours(0,0,0,0);var c=re(_,t);return n.getTime()>=l.getTime()?a+1:n.getTime()>=c.getTime()?a:a-1}function ie(e,t){x(1,arguments);var n=t||{},a=n.locale,r=a&&a.options&&a.options.firstWeekContainsDate,s=null==r?1:j(r),i=null==n.firstWeekContainsDate?s:j(n.firstWeekContainsDate),o=se(e,t),d=new Date(0);d.setUTCFullYear(o,0,i),d.setUTCHours(0,0,0,0);var u=re(d,t);return u}function oe(e,t){x(1,arguments);var n=H(e),a=re(n,t).getTime()-ie(n,t).getTime();return Math.round(a/6048e5)+1}var de="midnight",ue="noon",le="morning",_e="afternoon",ce="evening",me="night",he={G:function(e,t,n){var a=e.getUTCFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});case"GGGG":default:return n.era(a,{width:"wide"})}},y:function(e,t,n){if("yo"===t){var a=e.getUTCFullYear(),r=a>0?a:1-a;return n.ordinalNumber(r,{unit:"year"})}return G(e,t)},Y:function(e,t,n,a){var r=se(e,a),s=r>0?r:1-r;return"YY"===t?B(s%100,2):"Yo"===t?n.ordinalNumber(s,{unit:"year"}):B(s,t.length)},R:function(e,t){return B(te(e),t.length)},u:function(e,t){return B(e.getUTCFullYear(),t.length)},Q:function(e,t,n){var a=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return B(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,n){var a=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return B(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,n){var a=e.getUTCMonth();switch(t){case"M":case"MM":return $(e,t);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,n){var a=e.getUTCMonth();switch(t){case"L":return String(a+1);case"LL":return B(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,n,a){var r=oe(e,a);return"wo"===t?n.ordinalNumber(r,{unit:"week"}):B(r,t.length)},I:function(e,t,n){var a=ae(e);return"Io"===t?n.ordinalNumber(a,{unit:"week"}):B(a,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getUTCDate(),{unit:"date"}):V(e,t)},D:function(e,t,n){var a=function(e){x(1,arguments);var t=H(e),n=t.getTime();t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0);var a=t.getTime(),r=n-a;return Math.floor(r/864e5)+1}(e);return"Do"===t?n.ordinalNumber(a,{unit:"dayOfYear"}):B(a,t.length)},E:function(e,t,n){var a=e.getUTCDay();switch(t){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});case"EEEE":default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,n,a){var r=e.getUTCDay(),s=(r-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return B(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});case"eeee":default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,n,a){var r=e.getUTCDay(),s=(r-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return B(s,t.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});case"cccc":default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,n){var a=e.getUTCDay(),r=0===a?7:a;switch(t){case"i":return String(r);case"ii":return B(r,t.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});case"iiii":default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,n){var a=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,n){var a,r=e.getUTCHours();switch(a=12===r?ue:0===r?de:r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){var a,r=e.getUTCHours();switch(a=r>=17?ce:r>=12?_e:r>=4?le:me,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){var a=e.getUTCHours()%12;return 0===a&&(a=12),n.ordinalNumber(a,{unit:"hour"})}return q(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getUTCHours(),{unit:"hour"}):Q(e,t)},K:function(e,t,n){var a=e.getUTCHours()%12;return"Ko"===t?n.ordinalNumber(a,{unit:"hour"}):B(a,t.length)},k:function(e,t,n){var a=e.getUTCHours();return 0===a&&(a=24),"ko"===t?n.ordinalNumber(a,{unit:"hour"}):B(a,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):K(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):X(e,t)},S:function(e,t){return Z(e,t)},X:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return Me(r);case"XXXX":case"XX":return pe(r);case"XXXXX":case"XXX":default:return pe(r,":")}},x:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();switch(t){case"x":return Me(r);case"xxxx":case"xx":return pe(r);case"xxxxx":case"xxx":default:return pe(r,":")}},O:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+fe(r,":");case"OOOO":default:return"GMT"+pe(r,":")}},z:function(e,t,n,a){var r=(a._originalDate||e).getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+fe(r,":");case"zzzz":default:return"GMT"+pe(r,":")}},t:function(e,t,n,a){var r=a._originalDate||e;return B(Math.floor(r.getTime()/1e3),t.length)},T:function(e,t,n,a){return B((a._originalDate||e).getTime(),t.length)}};function fe(e,t){var n=e>0?"-":"+",a=Math.abs(e),r=Math.floor(a/60),s=a%60;if(0===s)return n+String(r);var i=t||"";return n+String(r)+i+B(s,2)}function Me(e,t){return e%60==0?(e>0?"-":"+")+B(Math.abs(e)/60,2):pe(e,t)}function pe(e,t){var n=t||"",a=e>0?"-":"+",r=Math.abs(e);return a+B(Math.floor(r/60),2)+n+B(r%60,2)}function ye(e,t){switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}}function ge(e,t){switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}}var Le={p:ge,P:function(e,t){var n,a=e.match(/(P+)(p+)?/),r=a[1],s=a[2];if(!s)return ye(e,t);switch(r){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;case"PPPP":default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",ye(r,t)).replace("{{time}}",ge(s,t))}},Ye=["D","DD"],we=["YY","YYYY"];function be(e){return-1!==Ye.indexOf(e)}function ke(e){return-1!==we.indexOf(e)}function ve(e,t,n){if("YYYY"===e)throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://git.io/fxCyr"));if("YY"===e)throw new RangeError("Use `yy` instead of `YY` (in `".concat(t,"`) for formatting years to the input `").concat(n,"`; see: https://git.io/fxCyr"));if("D"===e)throw new RangeError("Use `d` instead of `D` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://git.io/fxCyr"));if("DD"===e)throw new RangeError("Use `dd` instead of `DD` (in `".concat(t,"`) for formatting days of the month to the input `").concat(n,"`; see: https://git.io/fxCyr"))}var De=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Te=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Se=/^'([^]*?)'?$/,je=/''/g,xe=/[a-zA-Z]/;function He(e){return e.match(Se)[1].replace(je,"'")}function Pe(e,t){if(null==e)throw new TypeError("assign requires that input parameter not be null or undefined");for(var n in t=t||{})t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function Oe(e,t,n){x(2,arguments);var a=n||{},r=a.locale,s=r&&r.options&&r.options.weekStartsOn,i=null==s?0:j(s),o=null==a.weekStartsOn?i:j(a.weekStartsOn);if(!(o>=0&&o<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var d=H(e),u=j(t),l=d.getUTCDay(),_=u%7,c=(_+7)%7,m=(c<o?7:0)+u-l;return d.setUTCDate(d.getUTCDate()+m),d}var Ce=/^(1[0-2]|0?\d)/,Ee=/^(3[0-1]|[0-2]?\d)/,Re=/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,Fe=/^(5[0-3]|[0-4]?\d)/,We=/^(2[0-3]|[0-1]?\d)/,Ae=/^(2[0-4]|[0-1]?\d)/,ze=/^(1[0-1]|0?\d)/,Ne=/^(1[0-2]|0?\d)/,Ie=/^[0-5]?\d/,Ue=/^[0-5]?\d/,Je=/^\d/,Be=/^\d{1,2}/,Ge=/^\d{1,3}/,$e=/^\d{1,4}/,Ve=/^-?\d+/,qe=/^-?\d/,Qe=/^-?\d{1,2}/,Ke=/^-?\d{1,3}/,Xe=/^-?\d{1,4}/,Ze=/^([+-])(\d{2})(\d{2})?|Z/,et=/^([+-])(\d{2})(\d{2})|Z/,tt=/^([+-])(\d{2})(\d{2})((\d{2}))?|Z/,nt=/^([+-])(\d{2}):(\d{2})|Z/,at=/^([+-])(\d{2}):(\d{2})(:(\d{2}))?|Z/;function rt(e,t,n){var a=t.match(e);if(!a)return null;var r=parseInt(a[0],10);return{value:n?n(r):r,rest:t.slice(a[0].length)}}function st(e,t){var n=t.match(e);return n?"Z"===n[0]?{value:0,rest:t.slice(1)}:{value:("+"===n[1]?1:-1)*(36e5*(n[2]?parseInt(n[2],10):0)+6e4*(n[3]?parseInt(n[3],10):0)+1e3*(n[5]?parseInt(n[5],10):0)),rest:t.slice(n[0].length)}:null}function it(e,t){return rt(Ve,e,t)}function ot(e,t,n){switch(e){case 1:return rt(Je,t,n);case 2:return rt(Be,t,n);case 3:return rt(Ge,t,n);case 4:return rt($e,t,n);default:return rt(new RegExp("^\\d{1,"+e+"}"),t,n)}}function dt(e,t,n){switch(e){case 1:return rt(qe,t,n);case 2:return rt(Qe,t,n);case 3:return rt(Ke,t,n);case 4:return rt(Xe,t,n);default:return rt(new RegExp("^-?\\d{1,"+e+"}"),t,n)}}function ut(e){switch(e){case"morning":return 4;case"evening":return 17;case"pm":case"noon":case"afternoon":return 12;case"am":case"midnight":case"night":default:return 0}}function lt(e,t){var n,a=t>0,r=a?t:1-t;if(r<=50)n=e||100;else{var s=r+50;n=e+100*Math.floor(s/100)-(e>=s%100?100:0)}return a?n:1-n}var _t=[31,28,31,30,31,30,31,31,30,31,30,31],ct=[31,29,31,30,31,30,31,31,30,31,30,31];function mt(e){return e%400==0||e%4==0&&e%100!=0}var ht={G:{priority:140,parse:function(e,t,n,a){switch(t){case"G":case"GG":case"GGG":return n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"});case"GGGGG":return n.era(e,{width:"narrow"});case"GGGG":default:return n.era(e,{width:"wide"})||n.era(e,{width:"abbreviated"})||n.era(e,{width:"narrow"})}},set:function(e,t,n,a){return t.era=n,e.setUTCFullYear(n,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["R","u","t","T"]},y:{priority:130,parse:function(e,t,n,a){var r=function(e){return{year:e,isTwoDigitYear:"yy"===t}};switch(t){case"y":return ot(4,e,r);case"yo":return n.ordinalNumber(e,{unit:"year",valueCallback:r});default:return ot(t.length,e,r)}},validate:function(e,t,n){return t.isTwoDigitYear||t.year>0},set:function(e,t,n,a){var r=e.getUTCFullYear();if(n.isTwoDigitYear){var s=lt(n.year,r);return e.setUTCFullYear(s,0,1),e.setUTCHours(0,0,0,0),e}var i="era"in t&&1!==t.era?1-n.year:n.year;return e.setUTCFullYear(i,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","u","w","I","i","e","c","t","T"]},Y:{priority:130,parse:function(e,t,n,a){var r=function(e){return{year:e,isTwoDigitYear:"YY"===t}};switch(t){case"Y":return ot(4,e,r);case"Yo":return n.ordinalNumber(e,{unit:"year",valueCallback:r});default:return ot(t.length,e,r)}},validate:function(e,t,n){return t.isTwoDigitYear||t.year>0},set:function(e,t,n,a){var r=se(e,a);if(n.isTwoDigitYear){var s=lt(n.year,r);return e.setUTCFullYear(s,0,a.firstWeekContainsDate),e.setUTCHours(0,0,0,0),re(e,a)}var i="era"in t&&1!==t.era?1-n.year:n.year;return e.setUTCFullYear(i,0,a.firstWeekContainsDate),e.setUTCHours(0,0,0,0),re(e,a)},incompatibleTokens:["y","R","u","Q","q","M","L","I","d","D","i","t","T"]},R:{priority:130,parse:function(e,t,n,a){return dt("R"===t?4:t.length,e)},set:function(e,t,n,a){var r=new Date(0);return r.setUTCFullYear(n,0,4),r.setUTCHours(0,0,0,0),ee(r)},incompatibleTokens:["G","y","Y","u","Q","q","M","L","w","d","D","e","c","t","T"]},u:{priority:130,parse:function(e,t,n,a){return dt("u"===t?4:t.length,e)},set:function(e,t,n,a){return e.setUTCFullYear(n,0,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["G","y","Y","R","w","I","i","e","c","t","T"]},Q:{priority:120,parse:function(e,t,n,a){switch(t){case"Q":case"QQ":return ot(t.length,e);case"Qo":return n.ordinalNumber(e,{unit:"quarter"});case"QQQ":return n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQQ":return n.quarter(e,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(e,{width:"wide",context:"formatting"})||n.quarter(e,{width:"abbreviated",context:"formatting"})||n.quarter(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=1&&t<=4},set:function(e,t,n,a){return e.setUTCMonth(3*(n-1),1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","M","L","w","I","d","D","i","e","c","t","T"]},q:{priority:120,parse:function(e,t,n,a){switch(t){case"q":case"qq":return ot(t.length,e);case"qo":return n.ordinalNumber(e,{unit:"quarter"});case"qqq":return n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"});case"qqqqq":return n.quarter(e,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(e,{width:"wide",context:"standalone"})||n.quarter(e,{width:"abbreviated",context:"standalone"})||n.quarter(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,n){return t>=1&&t<=4},set:function(e,t,n,a){return e.setUTCMonth(3*(n-1),1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","Q","M","L","w","I","d","D","i","e","c","t","T"]},M:{priority:110,parse:function(e,t,n,a){var r=function(e){return e-1};switch(t){case"M":return rt(Ce,e,r);case"MM":return ot(2,e,r);case"Mo":return n.ordinalNumber(e,{unit:"month",valueCallback:r});case"MMM":return n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"});case"MMMMM":return n.month(e,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(e,{width:"wide",context:"formatting"})||n.month(e,{width:"abbreviated",context:"formatting"})||n.month(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=0&&t<=11},set:function(e,t,n,a){return e.setUTCMonth(n,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","L","w","I","D","i","e","c","t","T"]},L:{priority:110,parse:function(e,t,n,a){var r=function(e){return e-1};switch(t){case"L":return rt(Ce,e,r);case"LL":return ot(2,e,r);case"Lo":return n.ordinalNumber(e,{unit:"month",valueCallback:r});case"LLL":return n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"});case"LLLLL":return n.month(e,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(e,{width:"wide",context:"standalone"})||n.month(e,{width:"abbreviated",context:"standalone"})||n.month(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,n){return t>=0&&t<=11},set:function(e,t,n,a){return e.setUTCMonth(n,1),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","M","w","I","D","i","e","c","t","T"]},w:{priority:100,parse:function(e,t,n,a){switch(t){case"w":return rt(Fe,e);case"wo":return n.ordinalNumber(e,{unit:"week"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=53},set:function(e,t,n,a){return re(function(e,t,n){x(2,arguments);var a=H(e),r=j(t),s=oe(a,n)-r;return a.setUTCDate(a.getUTCDate()-7*s),a}(e,n,a),a)},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","i","t","T"]},I:{priority:100,parse:function(e,t,n,a){switch(t){case"I":return rt(Fe,e);case"Io":return n.ordinalNumber(e,{unit:"week"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=53},set:function(e,t,n,a){return ee(function(e,t){x(2,arguments);var n=H(e),a=j(t),r=ae(n)-a;return n.setUTCDate(n.getUTCDate()-7*r),n}(e,n,a),a)},incompatibleTokens:["y","Y","u","q","Q","M","L","w","d","D","e","c","t","T"]},d:{priority:90,subPriority:1,parse:function(e,t,n,a){switch(t){case"d":return rt(Ee,e);case"do":return n.ordinalNumber(e,{unit:"date"});default:return ot(t.length,e)}},validate:function(e,t,n){var a=mt(e.getUTCFullYear()),r=e.getUTCMonth();return a?t>=1&&t<=ct[r]:t>=1&&t<=_t[r]},set:function(e,t,n,a){return e.setUTCDate(n),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","w","I","D","i","e","c","t","T"]},D:{priority:90,subPriority:1,parse:function(e,t,n,a){switch(t){case"D":case"DD":return rt(Re,e);case"Do":return n.ordinalNumber(e,{unit:"date"});default:return ot(t.length,e)}},validate:function(e,t,n){return mt(e.getUTCFullYear())?t>=1&&t<=366:t>=1&&t<=365},set:function(e,t,n,a){return e.setUTCMonth(0,n),e.setUTCHours(0,0,0,0),e},incompatibleTokens:["Y","R","q","Q","M","L","w","I","d","E","i","e","c","t","T"]},E:{priority:90,parse:function(e,t,n,a){switch(t){case"E":case"EE":case"EEE":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEEE":return n.day(e,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"EEEE":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=0&&t<=6},set:function(e,t,n,a){return(e=Oe(e,n,a)).setUTCHours(0,0,0,0),e},incompatibleTokens:["D","i","e","c","t","T"]},e:{priority:90,parse:function(e,t,n,a){var r=function(e){var t=7*Math.floor((e-1)/7);return(e+a.weekStartsOn+6)%7+t};switch(t){case"e":case"ee":return ot(t.length,e,r);case"eo":return n.ordinalNumber(e,{unit:"day",valueCallback:r});case"eee":return n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeeee":return n.day(e,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"});case"eeee":default:return n.day(e,{width:"wide",context:"formatting"})||n.day(e,{width:"abbreviated",context:"formatting"})||n.day(e,{width:"short",context:"formatting"})||n.day(e,{width:"narrow",context:"formatting"})}},validate:function(e,t,n){return t>=0&&t<=6},set:function(e,t,n,a){return(e=Oe(e,n,a)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","E","i","c","t","T"]},c:{priority:90,parse:function(e,t,n,a){var r=function(e){var t=7*Math.floor((e-1)/7);return(e+a.weekStartsOn+6)%7+t};switch(t){case"c":case"cc":return ot(t.length,e,r);case"co":return n.ordinalNumber(e,{unit:"day",valueCallback:r});case"ccc":return n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"ccccc":return n.day(e,{width:"narrow",context:"standalone"});case"cccccc":return n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"});case"cccc":default:return n.day(e,{width:"wide",context:"standalone"})||n.day(e,{width:"abbreviated",context:"standalone"})||n.day(e,{width:"short",context:"standalone"})||n.day(e,{width:"narrow",context:"standalone"})}},validate:function(e,t,n){return t>=0&&t<=6},set:function(e,t,n,a){return(e=Oe(e,n,a)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","R","u","q","Q","M","L","I","d","D","E","i","e","t","T"]},i:{priority:90,parse:function(e,t,n,a){var r=function(e){return 0===e?7:e};switch(t){case"i":case"ii":return ot(t.length,e);case"io":return n.ordinalNumber(e,{unit:"day"});case"iii":return n.day(e,{width:"abbreviated",context:"formatting",valueCallback:r})||n.day(e,{width:"short",context:"formatting",valueCallback:r})||n.day(e,{width:"narrow",context:"formatting",valueCallback:r});case"iiiii":return n.day(e,{width:"narrow",context:"formatting",valueCallback:r});case"iiiiii":return n.day(e,{width:"short",context:"formatting",valueCallback:r})||n.day(e,{width:"narrow",context:"formatting",valueCallback:r});case"iiii":default:return n.day(e,{width:"wide",context:"formatting",valueCallback:r})||n.day(e,{width:"abbreviated",context:"formatting",valueCallback:r})||n.day(e,{width:"short",context:"formatting",valueCallback:r})||n.day(e,{width:"narrow",context:"formatting",valueCallback:r})}},validate:function(e,t,n){return t>=1&&t<=7},set:function(e,t,n,a){return(e=function(e,t){x(2,arguments);var n=j(t);n%7==0&&(n-=7);var a=1,r=H(e),s=r.getUTCDay(),i=n%7,o=(i+7)%7,d=(o<a?7:0)+n-s;return r.setUTCDate(r.getUTCDate()+d),r}(e,n,a)).setUTCHours(0,0,0,0),e},incompatibleTokens:["y","Y","u","q","Q","M","L","w","d","D","E","e","c","t","T"]},a:{priority:80,parse:function(e,t,n,a){switch(t){case"a":case"aa":case"aaa":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaaa":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,n,a){return e.setUTCHours(ut(n),0,0,0),e},incompatibleTokens:["b","B","H","K","k","t","T"]},b:{priority:80,parse:function(e,t,n,a){switch(t){case"b":case"bb":case"bbb":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbbb":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,n,a){return e.setUTCHours(ut(n),0,0,0),e},incompatibleTokens:["a","B","H","K","k","t","T"]},B:{priority:80,parse:function(e,t,n,a){switch(t){case"B":case"BB":case"BBB":return n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBBB":return n.dayPeriod(e,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(e,{width:"wide",context:"formatting"})||n.dayPeriod(e,{width:"abbreviated",context:"formatting"})||n.dayPeriod(e,{width:"narrow",context:"formatting"})}},set:function(e,t,n,a){return e.setUTCHours(ut(n),0,0,0),e},incompatibleTokens:["a","b","t","T"]},h:{priority:70,parse:function(e,t,n,a){switch(t){case"h":return rt(Ne,e);case"ho":return n.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=12},set:function(e,t,n,a){var r=e.getUTCHours()>=12;return r&&n<12?e.setUTCHours(n+12,0,0,0):r||12!==n?e.setUTCHours(n,0,0,0):e.setUTCHours(0,0,0,0),e},incompatibleTokens:["H","K","k","t","T"]},H:{priority:70,parse:function(e,t,n,a){switch(t){case"H":return rt(We,e);case"Ho":return n.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=23},set:function(e,t,n,a){return e.setUTCHours(n,0,0,0),e},incompatibleTokens:["a","b","h","K","k","t","T"]},K:{priority:70,parse:function(e,t,n,a){switch(t){case"K":return rt(ze,e);case"Ko":return n.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=11},set:function(e,t,n,a){return e.getUTCHours()>=12&&n<12?e.setUTCHours(n+12,0,0,0):e.setUTCHours(n,0,0,0),e},incompatibleTokens:["a","b","h","H","k","t","T"]},k:{priority:70,parse:function(e,t,n,a){switch(t){case"k":return rt(Ae,e);case"ko":return n.ordinalNumber(e,{unit:"hour"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=1&&t<=24},set:function(e,t,n,a){var r=n<=24?n%24:n;return e.setUTCHours(r,0,0,0),e},incompatibleTokens:["a","b","h","H","K","t","T"]},m:{priority:60,parse:function(e,t,n,a){switch(t){case"m":return rt(Ie,e);case"mo":return n.ordinalNumber(e,{unit:"minute"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=59},set:function(e,t,n,a){return e.setUTCMinutes(n,0,0),e},incompatibleTokens:["t","T"]},s:{priority:50,parse:function(e,t,n,a){switch(t){case"s":return rt(Ue,e);case"so":return n.ordinalNumber(e,{unit:"second"});default:return ot(t.length,e)}},validate:function(e,t,n){return t>=0&&t<=59},set:function(e,t,n,a){return e.setUTCSeconds(n,0),e},incompatibleTokens:["t","T"]},S:{priority:30,parse:function(e,t,n,a){return ot(t.length,e,(function(e){return Math.floor(e*Math.pow(10,3-t.length))}))},set:function(e,t,n,a){return e.setUTCMilliseconds(n),e},incompatibleTokens:["t","T"]},X:{priority:10,parse:function(e,t,n,a){switch(t){case"X":return st(Ze,e);case"XX":return st(et,e);case"XXXX":return st(tt,e);case"XXXXX":return st(at,e);case"XXX":default:return st(nt,e)}},set:function(e,t,n,a){return t.timestampIsSet?e:new Date(e.getTime()-n)},incompatibleTokens:["t","T","x"]},x:{priority:10,parse:function(e,t,n,a){switch(t){case"x":return st(Ze,e);case"xx":return st(et,e);case"xxxx":return st(tt,e);case"xxxxx":return st(at,e);case"xxx":default:return st(nt,e)}},set:function(e,t,n,a){return t.timestampIsSet?e:new Date(e.getTime()-n)},incompatibleTokens:["t","T","X"]},t:{priority:40,parse:function(e,t,n,a){return it(e)},set:function(e,t,n,a){return[new Date(1e3*n),{timestampIsSet:!0}]},incompatibleTokens:"*"},T:{priority:20,parse:function(e,t,n,a){return it(e)},set:function(e,t,n,a){return[new Date(n),{timestampIsSet:!0}]},incompatibleTokens:"*"}},ft=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Mt=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,pt=/^'([^]*?)'?$/,yt=/''/g,gt=/\S/,Lt=/[a-zA-Z]/;function Yt(e,t,n,a){x(3,arguments);var r=String(e),s=String(t),i=a||{},o=i.locale||U;if(!o.match)throw new RangeError("locale must contain match property");var d=o.options&&o.options.firstWeekContainsDate,u=null==d?1:j(d),l=null==i.firstWeekContainsDate?u:j(i.firstWeekContainsDate);if(!(l>=1&&l<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var _=o.options&&o.options.weekStartsOn,c=null==_?0:j(_),m=null==i.weekStartsOn?c:j(i.weekStartsOn);if(!(m>=0&&m<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(""===s)return""===r?H(n):new Date(NaN);var h,f={firstWeekContainsDate:l,weekStartsOn:m,locale:o},M=[{priority:10,subPriority:-1,set:wt,index:0}],p=s.match(Mt).map((function(e){var t=e[0];return"p"===t||"P"===t?(0,Le[t])(e,o.formatLong,f):e})).join("").match(ft),y=[];for(h=0;h<p.length;h++){var g=p[h];!i.useAdditionalWeekYearTokens&&ke(g)&&ve(g,s,e),!i.useAdditionalDayOfYearTokens&&be(g)&&ve(g,s,e);var L=g[0],Y=ht[L];if(Y){var w=Y.incompatibleTokens;if(Array.isArray(w)){for(var b=void 0,k=0;k<y.length;k++){var v=y[k].token;if(-1!==w.indexOf(v)||v===L){b=y[k];break}}if(b)throw new RangeError("The format string mustn't contain `".concat(b.fullToken,"` and `").concat(g,"` at the same time"))}else if("*"===Y.incompatibleTokens&&y.length)throw new RangeError("The format string mustn't contain `".concat(g,"` and any other token at the same time"));y.push({token:L,fullToken:g});var D=Y.parse(r,g,o.match,f);if(!D)return new Date(NaN);M.push({priority:Y.priority,subPriority:Y.subPriority||0,set:Y.set,validate:Y.validate,value:D.value,index:M.length}),r=D.rest}else{if(L.match(Lt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+L+"`");if("''"===g?g="'":"'"===L&&(g=bt(g)),0!==r.indexOf(g))return new Date(NaN);r=r.slice(g.length)}}if(r.length>0&&gt.test(r))return new Date(NaN);var T=M.map((function(e){return e.priority})).sort((function(e,t){return t-e})).filter((function(e,t,n){return n.indexOf(e)===t})).map((function(e){return M.filter((function(t){return t.priority===e})).sort((function(e,t){return t.subPriority-e.subPriority}))})).map((function(e){return e[0]})),S=H(n);if(isNaN(S))return new Date(NaN);var P=J(S,C(S)),O={};for(h=0;h<T.length;h++){var E=T[h];if(E.validate&&!E.validate(P,E.value,f))return new Date(NaN);var R=E.set(P,O,E.value,f);R[0]?(P=R[0],Pe(O,R[1])):P=R}return P}function wt(e,t){if(t.timestampIsSet)return e;var n=new Date(0);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}function bt(e){return e.match(pt)[1].replace(yt,"'")}var kt=Object.assign({},Y);kt.isRight=!0,kt.compare=function(e,t,n){function a(e){return n&&n.dateInputFormat?Yt("".concat(e),"".concat(n.dateInputFormat),new Date):e}return e=a(e),t=a(t),E(e)?E(t)?function(e,t){x(2,arguments);var n=H(e),a=H(t),r=n.getTime()-a.getTime();return r<0?-1:r>0?1:r}(e,t):1:-1},kt.format=function(e,t){if(null==e)return"";var n=Yt(e,t.dateInputFormat,new Date);return E(n)?function(e,t,n){x(2,arguments);var a=String(t),r=n||{},s=r.locale||U,i=s.options&&s.options.firstWeekContainsDate,o=null==i?1:j(i),d=null==r.firstWeekContainsDate?o:j(r.firstWeekContainsDate);if(!(d>=1&&d<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var u=s.options&&s.options.weekStartsOn,l=null==u?0:j(u),_=null==r.weekStartsOn?l:j(r.weekStartsOn);if(!(_>=0&&_<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!s.localize)throw new RangeError("locale must contain localize property");if(!s.formatLong)throw new RangeError("locale must contain formatLong property");var c=H(e);if(!E(c))throw new RangeError("Invalid time value");var m=C(c),h=J(c,m),f={firstWeekContainsDate:d,weekStartsOn:_,locale:s,_originalDate:c},M=a.match(Te).map((function(e){var t=e[0];return"p"===t||"P"===t?(0,Le[t])(e,s.formatLong,f):e})).join("").match(De).map((function(n){if("''"===n)return"'";var a=n[0];if("'"===a)return He(n);var i=he[a];if(i)return!r.useAdditionalWeekYearTokens&&ke(n)&&ve(n,t,e),!r.useAdditionalDayOfYearTokens&&be(n)&&ve(n,t,e),i(h,n,s.localize,f);if(a.match(xe))throw new RangeError("Format string contains an unescaped latin alphabet character `"+a+"`");return n})).join("");return M}(n,t.dateOutputFormat):(console.error('Not a valid date: "'.concat(e,'"')),null)};var vt=Object.freeze({__proto__:null,default:kt}),Dt=Object.assign({},Y);Dt.isRight=!0,Dt.filterPredicate=function(e,t){return 0===Dt.compare(e,t)},Dt.compare=function(e,t){function n(e){return null==e?-1/0:e.indexOf(".")>=0?parseFloat(e):parseInt(e,10)}return(e="number"==typeof e?e:n(e))<(t="number"==typeof t?t:n(t))?-1:e>t?1:0};var Tt=Object.freeze({__proto__:null,default:Dt}),St=Object.assign({},Dt);St.format=function(e){return null==e?"":parseFloat(Math.round(100*e)/100).toFixed(2)};var jt=Object.freeze({__proto__:null,default:St}),xt=Object.assign({},Dt);xt.format=function(e){return null==e?"":"".concat(parseFloat(100*e).toFixed(2),"%")};var Ht=Object.freeze({__proto__:null,default:xt}),Pt=Object.assign({},Y);Pt.isRight=!0,Pt.filterPredicate=function(e,t){return 0===Pt.compare(e,t)},Pt.compare=function(e,t){function n(e){return"boolean"==typeof e?e?1:0:"string"==typeof e?"true"===e?1:0:-1/0}return(e=n(e))<(t=n(t))?-1:e>t?1:0};var Ot={},Ct={date:vt,decimal:jt,number:Tt,percentage:Ht,boolean:Object.freeze({__proto__:null,default:Pt})};Object.keys(Ct).forEach((function(e){var t=e.replace(/^\.\//,"").replace(/\.js/,"");Ot[t]=Ct[e].default}));var Et=w({render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:e.wrapStyleClasses},[e.isLoading?n("div",{staticClass:"vgt-loading vgt-center-align"},[e._t("loadingContent",[n("span",{staticClass:"vgt-loading__content"},[e._v("\n        Loading...\n      ")])])],2):e._e(),e._v(" "),n("div",{staticClass:"vgt-inner-wrap",class:{"is-loading":e.isLoading}},[e.paginate&&e.paginateOnTop?e._t("pagination-top",[n("vgt-pagination",{ref:"paginationTop",attrs:{perPage:e.perPage,rtl:e.rtl,total:e.totalRows||e.totalRowCount,mode:e.paginationMode,jumpFirstOrLast:e.paginationOptions.jumpFirstOrLast,firstText:e.firstText,lastText:e.lastText,nextText:e.nextText,prevText:e.prevText,rowsPerPageText:e.rowsPerPageText,perPageDropdownEnabled:e.paginationOptions.perPageDropdownEnabled,customRowsPerPageDropdown:e.customRowsPerPageDropdown,paginateDropdownAllowAll:e.paginateDropdownAllowAll,ofText:e.ofText,pageText:e.pageText,allText:e.allText,"info-fn":e.paginationInfoFn},on:{"page-changed":e.pageChanged,"per-page-changed":e.perPageChanged}})],{pageChanged:e.pageChanged,perPageChanged:e.perPageChanged,total:e.totalRows||e.totalRowCount}):e._e(),e._v(" "),n("vgt-global-search",{attrs:{"search-enabled":e.searchEnabled&&null==e.externalSearchQuery,"global-search-placeholder":e.searchPlaceholder},on:{"on-keyup":e.searchTableOnKeyUp,"on-enter":e.searchTableOnEnter},model:{value:e.globalSearchTerm,callback:function(t){e.globalSearchTerm=t},expression:"globalSearchTerm"}},[n("template",{slot:"internal-table-actions"},[e._t("table-actions")],2)],2),e._v(" "),e.selectedRowCount&&!e.disableSelectInfo?n("div",{staticClass:"vgt-selection-info-row clearfix",class:e.selectionInfoClass},[e._v("\n      "+e._s(e.selectionInfo)+"\n      "),n("a",{attrs:{href:""},on:{click:function(t){return t.preventDefault(),e.unselectAllInternal(!0)}}},[e._v("\n        "+e._s(e.clearSelectionText)+"\n      ")]),e._v(" "),n("div",{staticClass:"vgt-selection-info-row__actions vgt-pull-right"},[e._t("selected-row-actions")],2)]):e._e(),e._v(" "),n("div",{staticClass:"vgt-fixed-header"},[e.fixedHeader?n("table",{class:e.tableStyleClasses,attrs:{id:"vgt-table"}},[n("colgroup",e._l(e.columns,(function(e,t){return n("col",{key:t,attrs:{id:"col-"+t}})})),0),e._v(" "),n("vgt-table-header",{ref:"table-header-secondary",tag:"thead",attrs:{columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"all-selected":e.allSelected,"all-selected-indeterminate":e.allSelectedIndeterminate,mode:e.mode,sortable:e.sortable,"multiple-column-sort":e.multipleColumnSort,"typed-columns":e.typedColumns,getClasses:e.getClasses,searchEnabled:e.searchEnabled,paginated:e.paginated,"table-ref":e.$refs.table},on:{"on-toggle-select-all":e.toggleSelectAll,"on-sort-change":e.changeSort,"filter-changed":e.filterRows},scopedSlots:e._u([{key:"table-column",fn:function(t){return[e._t("table-column",[n("span",[e._v(e._s(t.column.label))])],{column:t.column})]}},{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)})],1):e._e()]),e._v(" "),n("div",{class:{"vgt-responsive":e.responsive},style:e.wrapperStyles},[n("table",{ref:"table",class:e.tableStyles,attrs:{id:"vgt-table"}},[n("colgroup",e._l(e.columns,(function(e,t){return n("col",{key:t,attrs:{id:"col-"+t}})})),0),e._v(" "),n("vgt-table-header",{ref:"table-header-primary",tag:"thead",attrs:{columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"all-selected":e.allSelected,"all-selected-indeterminate":e.allSelectedIndeterminate,mode:e.mode,sortable:e.sortable,"multiple-column-sort":e.multipleColumnSort,"typed-columns":e.typedColumns,getClasses:e.getClasses,searchEnabled:e.searchEnabled},on:{"on-toggle-select-all":e.toggleSelectAll,"on-sort-change":e.changeSort,"filter-changed":e.filterRows},scopedSlots:e._u([{key:"table-column",fn:function(t){return[e._t("table-column",[n("span",[e._v(e._s(t.column.label))])],{column:t.column})]}},{key:"column-filter",fn:function(t){return[e._t("column-filter",null,{column:t.column,updateFilters:t.updateFilters})]}}],null,!0)}),e._v(" "),e._l(e.paginated,(function(t,a){return n("tbody",{key:a},[e.groupHeaderOnTop?n("vgt-header-row",{class:e.getRowStyleClass(t),attrs:{"header-row":t,columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"select-all-by-group":e.selectAllByGroup,collapsable:e.groupOptions.collapsable,"collect-formatted":e.collectFormatted,"formatted-row":e.formattedRow,"get-classes":e.getClasses,"full-colspan":e.fullColspan,groupIndex:a},on:{vgtExpand:function(n){return e.toggleExpand(t[e.rowKeyField])},"on-select-group-change":function(n){return e.toggleSelectGroup(n,t)}},scopedSlots:e._u([{key:"table-header-row",fn:function(t){return e.hasHeaderRowTemplate?[e._t("table-header-row",null,{column:t.column,formattedRow:t.formattedRow,row:t.row})]:void 0}}],null,!0)}):e._e(),e._v(" "),e._l(t.children,(function(a,r){return!e.groupOptions.collapsable||t.vgtIsExpanded?n("tr",{key:a.originalIndex,class:e.getRowStyleClass(a),on:{mouseenter:function(t){return e.onMouseenter(a,r)},mouseleave:function(t){return e.onMouseleave(a,r)},dblclick:function(t){return e.onRowDoubleClicked(a,r,t)},click:function(t){return e.onRowClicked(a,r,t)},auxclick:function(t){return e.onRowAuxClicked(a,r,t)}}},[e.lineNumbers?n("th",{staticClass:"line-numbers"},[e._v("\n              "+e._s(e.getCurrentIndex(a.originalIndex))+"\n            ")]):e._e(),e._v(" "),e.selectable?n("th",{staticClass:"vgt-checkbox-col",on:{click:function(t){return t.stopPropagation(),e.onCheckboxClicked(a,r,t)}}},[n("input",{attrs:{type:"checkbox",disabled:a.vgtDisabled},domProps:{checked:a.vgtSelected}})]):e._e(),e._v(" "),e._l(e.columns,(function(t,s){return!t.hidden&&t.field?n("td",{key:s,class:e.getClasses(s,"td",a),attrs:{"data-label":e.compactMode?t.label:void 0},on:{click:function(n){return e.onCellClicked(a,t,r,n)}}},[e._t("table-row",[t.html?n("span",{domProps:{innerHTML:e._s(e.collect(a,t.field))}}):n("span",[e._v("\n                  "+e._s(e.collectFormatted(a,t))+"\n                ")])],{row:a,column:t,formattedRow:e.formattedRow(a),index:r})],2):e._e()}))],2):e._e()})),e._v(" "),e.groupHeaderOnBottom?n("vgt-header-row",{attrs:{"header-row":t,columns:e.columns,"line-numbers":e.lineNumbers,selectable:e.selectable,"select-all-by-group":e.selectAllByGroup,"collect-formatted":e.collectFormatted,"formatted-row":e.formattedRow,"get-classes":e.getClasses,"full-colspan":e.fullColspan,groupIndex:e.index},on:{"on-select-group-change":function(n){return e.toggleSelectGroup(n,t)}},scopedSlots:e._u([{key:"table-header-row",fn:function(t){return e.hasHeaderRowTemplate?[e._t("table-header-row",null,{column:t.column,formattedRow:t.formattedRow,row:t.row})]:void 0}}],null,!0)}):e._e()],2)})),e._v(" "),e.showEmptySlot?n("tbody",[n("tr",[n("td",{attrs:{colspan:e.fullColspan}},[e._t("emptystate",[n("div",{staticClass:"vgt-center-align vgt-text-disabled"},[e._v("\n                  No data for table\n                ")])])],2)])]):e._e()],2)]),e._v(" "),e.hasFooterSlot?n("div",{staticClass:"vgt-wrap__actions-footer"},[e._t("table-actions-bottom")],2):e._e(),e._v(" "),e.paginate&&e.paginateOnBottom?e._t("pagination-bottom",[n("vgt-pagination",{ref:"paginationBottom",attrs:{perPage:e.perPage,rtl:e.rtl,total:e.totalRows||e.totalRowCount,mode:e.paginationMode,jumpFirstOrLast:e.paginationOptions.jumpFirstOrLast,firstText:e.firstText,lastText:e.lastText,nextText:e.nextText,prevText:e.prevText,rowsPerPageText:e.rowsPerPageText,perPageDropdownEnabled:e.paginationOptions.perPageDropdownEnabled,customRowsPerPageDropdown:e.customRowsPerPageDropdown,paginateDropdownAllowAll:e.paginateDropdownAllowAll,ofText:e.ofText,pageText:e.pageText,allText:e.allText,"info-fn":e.paginationInfoFn},on:{"page-changed":e.pageChanged,"per-page-changed":e.perPageChanged}})],{pageChanged:e.pageChanged,perPageChanged:e.perPageChanged,total:e.totalRows||e.totalRowCount}):e._e()],2)])},staticRenderFns:[]},void 0,{name:"vue-good-table",props:{isLoading:{default:null,type:Boolean},maxHeight:{default:null,type:String},fixedHeader:Boolean,theme:{default:""},mode:{default:"local"},totalRows:{},styleClass:{default:"vgt-table bordered"},columns:{},rows:{},lineNumbers:Boolean,responsive:{default:!0,type:Boolean},rtl:Boolean,rowStyleClass:{default:null,type:[Function,String]},compactMode:Boolean,groupOptions:{default:function(){return{enabled:!1,collapsable:!1,rowKey:null}}},selectOptions:{default:function(){return{enabled:!1,selectionInfoClass:"",selectionText:"rows selected",clearSelectionText:"clear",disableSelectInfo:!1,selectAllByGroup:!1}}},sortOptions:{default:function(){return{enabled:!0,multipleColumns:!0,initialSortBy:{}}}},paginationOptions:{default:function(){var e;return r(e={enabled:!1,position:"bottom",perPage:10,perPageDropdown:null,perPageDropdownEnabled:!0},"position","bottom"),r(e,"dropdownAllowAll",!0),r(e,"mode","records"),r(e,"infoFn",null),r(e,"jumpFirstOrLast",!1),e}},searchOptions:{default:function(){return{enabled:!1,trigger:null,externalQuery:null,searchFn:null,placeholder:"Search Table"}}}},data:function(){return{tableLoading:!1,firstText:"First",lastText:"Last",nextText:"Next",prevText:"Previous",rowsPerPageText:"Rows per page",ofText:"of",allText:"All",pageText:"page",selectable:!1,selectOnCheckboxOnly:!1,selectAllByPage:!0,disableSelectInfo:!1,selectionInfoClass:"",selectionText:"rows selected",clearSelectionText:"clear",maintainExpanded:!0,expandedRowKeys:new Set,sortable:!0,defaultSortBy:null,multipleColumnSort:!0,searchEnabled:!1,searchTrigger:null,externalSearchQuery:null,searchFn:null,searchPlaceholder:"Search Table",searchSkipDiacritics:!1,perPage:null,paginate:!1,paginateOnTop:!1,paginateOnBottom:!0,customRowsPerPageDropdown:[],paginateDropdownAllowAll:!0,paginationMode:"records",paginationInfoFn:null,currentPage:1,currentPerPage:10,sorts:[],globalSearchTerm:"",filteredRows:[],columnFilters:{},forceSearch:!1,sortChanged:!1,dataTypes:Ot||{}}},watch:{rows:{handler:function(){this.$emit("update:isLoading",!1),this.filterRows(this.columnFilters,!1)},deep:!0,immediate:!0},selectOptions:{handler:function(){this.initializeSelect()},deep:!0,immediate:!0},paginationOptions:{handler:function(e,t){f(e,t)||this.initializePagination()},deep:!0,immediate:!0},searchOptions:{handler:function(){void 0!==this.searchOptions.externalQuery&&this.searchOptions.externalQuery!==this.searchTerm&&(this.externalSearchQuery=this.searchOptions.externalQuery,this.handleSearch()),this.initializeSearch()},deep:!0,immediate:!0},sortOptions:{handler:function(e,t){f(e,t)||this.initializeSort()},deep:!0},selectedRows:function(e,t){f(e,t)||this.$emit("on-selected-rows-change",{selectedRows:this.selectedRows})}},computed:{tableStyles:function(){return this.compactMode?this.tableStyleClasses+"vgt-compact":this.tableStyleClasses},hasFooterSlot:function(){return!!this.$slots["table-actions-bottom"]},wrapperStyles:function(){return{overflow:"scroll-y",maxHeight:this.maxHeight?this.maxHeight:"auto"}},rowKeyField:function(){return this.groupOptions.rowKey||"vgt_header_id"},hasHeaderRowTemplate:function(){return!!this.$slots["table-header-row"]||!!this.$scopedSlots["table-header-row"]},showEmptySlot:function(){return!this.paginated.length||"no groups"===this.paginated[0].label&&!this.paginated[0].children.length},allSelected:function(){return this.selectedRowCount>0&&(this.selectAllByPage&&this.selectedPageRowsCount===this.totalPageRowCount||!this.selectAllByPage&&this.selectedRowCount===this.totalRowCount)},allSelectedIndeterminate:function(){return!this.allSelected&&(this.selectAllByPage&&this.selectedPageRowsCount>0||!this.selectAllByPage&&this.selectedRowCount>0)},selectionInfo:function(){return"".concat(this.selectedRowCount," ").concat(this.selectionText)},selectedRowCount:function(){return this.selectedRows.length},selectedPageRowsCount:function(){return this.selectedPageRows.length},selectedPageRows:function(){var e=[];return this.paginated.forEach((function(t){t.children.forEach((function(t){t.vgtSelected&&e.push(t)}))})),e},selectedRows:function(){var e=[];return this.processedRows.forEach((function(t){t.children.forEach((function(t){t.vgtSelected&&e.push(t)}))})),e.sort((function(e,t){return e.originalIndex-t.originalIndex}))},fullColspan:function(){for(var e=0,t=0;t<this.columns.length;t+=1)this.columns[t].hidden||(e+=1);return this.lineNumbers&&e++,this.selectable&&e++,e},groupHeaderOnTop:function(){return!(this.groupOptions&&this.groupOptions.enabled&&this.groupOptions.headerPosition&&"bottom"===this.groupOptions.headerPosition)&&!(!this.groupOptions||!this.groupOptions.enabled)},groupHeaderOnBottom:function(){return!!(this.groupOptions&&this.groupOptions.enabled&&this.groupOptions.headerPosition&&"bottom"===this.groupOptions.headerPosition)},totalRowCount:function(){return this.processedRows.reduce((function(e,t){return e+(t.children?t.children.length:0)}),0)},totalPageRowCount:function(){return this.paginated.reduce((function(e,t){return e+(t.children?t.children.length:0)}),0)},wrapStyleClasses:function(){var e="vgt-wrap";return this.rtl&&(e+=" rtl"),e+=" ".concat(this.theme)},tableStyleClasses:function(){var e=this.styleClass;return e+=" ".concat(this.theme)},searchTerm:function(){return null!=this.externalSearchQuery?this.externalSearchQuery:this.globalSearchTerm},globalSearchAllowed:function(){return!(!this.searchEnabled||!this.globalSearchTerm||"enter"===this.searchTrigger)||(null!=this.externalSearchQuery&&"enter"!==this.searchTrigger||!!this.forceSearch&&(this.forceSearch=!1,!0))},processedRows:function(){var e=this,t=this.filteredRows;if("remote"===this.mode)return t;if(this.globalSearchAllowed){var n=[];this.filteredRows.forEach((function(e){n.push.apply(n,i(e.children))}));var a=[];n.forEach((function(t){for(var n=0;n<e.columns.length;n+=1){var r=e.columns[n];if(!r.globalSearchDisabled)if(e.searchFn){if(e.searchFn(t,r,e.collectFormatted(t,r),e.searchTerm)){a.push(t);break}}else if(Y.filterPredicate(e.collectFormatted(t,r),e.searchTerm,e.searchSkipDiacritics)){a.push(t);break}}})),this.$emit("on-search",{searchTerm:this.searchTerm,rowCount:a.length}),t=[],this.filteredRows.forEach((function(e){var n=e.vgt_header_id,r=a.filter((function(e){return e.vgt_id===n}));if(r.length){var s=JSON.parse(JSON.stringify(e));s.children=r,t.push(s)}}))}return this.sorts.length&&t.forEach((function(t){t.children.sort((function(t,n){for(var a,r=0;r<e.sorts.length;r+=1){var s=e.sorts[r];if(s.type===_)a=a||t.originalIndex-n.originalIndex;else{var i=e.getColumnForField(s.field),o=e.collect(t,s.field),d=e.collect(n,s.field),u=i.sortFn;a=u&&"function"==typeof u?a||u(o,d,i,t,n)*(s.type===l?-1:1):a||i.typeDef.compare(o,d,i)*(s.type===l?-1:1)}}return a}))})),"enter"===this.searchTrigger&&(this.filteredRows=t),t},paginated:function(){var e=this;if(!this.processedRows.length)return[];if("remote"===this.mode)return this.processedRows;var t=[];if(this.processedRows.forEach((function(n){var a;e.groupOptions.enabled&&t.push(n),(a=t).push.apply(a,i(n.children))})),this.paginate){var n=(this.currentPage-1)*this.currentPerPage;(n>=t.length||-1===this.currentPerPage)&&(this.currentPage=1,n=0);var a=t.length+1;-1!==this.currentPerPage&&(a=this.currentPage*this.currentPerPage),t=t.slice(n,a)}var r=[];return t.forEach((function(t){if(void 0!==t.vgt_header_id){e.handleExpanded(t);var n=JSON.parse(JSON.stringify(t));n.children=[],r.push(n)}else{var a=r.find((function(e){return e.vgt_header_id===t.vgt_id}));a||(a=e.processedRows.find((function(e){return e.vgt_header_id===t.vgt_id})))&&((a=JSON.parse(JSON.stringify(a))).children=[],r.push(a)),a.children.push(t)}})),r},originalRows:function(){var e=this.rows&&this.rows.length?JSON.parse(JSON.stringify(this.rows)):[],t=[];t=this.groupOptions.enabled?this.handleGrouped(e):this.handleGrouped([{label:"no groups",children:e}]);var n=0;return t.forEach((function(e){e.children.forEach((function(e){e.originalIndex=n++}))})),t},typedColumns:function(){for(var e=this.columns,t=0;t<this.columns.length;t++){var n=e[t];n.typeDef=this.dataTypes[n.type]||Y}return e},hasRowClickListener:function(){return this.$listeners&&this.$listeners["on-row-click"]}},methods:{handleExpanded:function(e){this.maintainExpanded&&this.expandedRowKeys.has(e[this.rowKeyField])?this.$set(e,"vgtIsExpanded",!0):this.$set(e,"vgtIsExpanded",!1)},toggleExpand:function(e){var t=this,n=this.filteredRows.find((function(n){return n[t.rowKeyField]===e}));n&&this.$set(n,"vgtIsExpanded",!n.vgtIsExpanded),this.maintainExpanded&&n.vgtIsExpanded?this.expandedRowKeys.add(n[this.rowKeyField]):this.expandedRowKeys.delete(n[this.rowKeyField])},expandAll:function(){var e=this;this.filteredRows.forEach((function(t){e.$set(t,"vgtIsExpanded",!0),e.maintainExpanded&&e.expandedRowKeys.add(t[e.rowKeyField])}))},collapseAll:function(){var e=this;this.filteredRows.forEach((function(t){e.$set(t,"vgtIsExpanded",!1),e.expandedRowKeys.clear()}))},getColumnForField:function(e){for(var t=0;t<this.typedColumns.length;t+=1)if(this.typedColumns[t].field===e)return this.typedColumns[t]},handleSearch:function(){this.resetTable(),"remote"===this.mode&&this.$emit("on-search",{searchTerm:this.searchTerm})},reset:function(){this.initializeSort(),this.changePage(1),this.$refs["table-header-primary"].reset(!0),this.$refs["table-header-secondary"]&&this.$refs["table-header-secondary"].reset(!0)},emitSelectedRows:function(){this.$emit("on-select-all",{selected:this.selectedRowCount===this.totalRowCount,selectedRows:this.selectedRows})},unselectAllInternal:function(e){var t=this;(this.selectAllByPage&&!e?this.paginated:this.filteredRows).forEach((function(e,n){e.children.forEach((function(e,n){t.$set(e,"vgtSelected",!1)}))})),this.emitSelectedRows()},toggleSelectAll:function(){var e=this;this.allSelected?this.unselectAllInternal():((this.selectAllByPage?this.paginated:this.filteredRows).forEach((function(t){t.children.forEach((function(t){e.$set(t,"vgtSelected",!0)}))})),this.emitSelectedRows())},toggleSelectGroup:function(e,t){var n=this;t.children.forEach((function(t){n.$set(t,"vgtSelected",e.checked)}))},changePage:function(e){var t=this.paginate,n=this.$refs,a=n.paginationBottom,r=n.paginationTop;t&&(this.paginateOnTop&&r&&(r.currentPage=e),this.paginateOnBottom&&a&&(a.currentPage=e),this.currentPage=e)},pageChangedEvent:function(){return{currentPage:this.currentPage,currentPerPage:this.currentPerPage,total:Math.floor(this.totalRowCount/this.currentPerPage)}},pageChanged:function(e){if(this.currentPage=e.currentPage,!e.noEmit){var t=this.pageChangedEvent();t.prevPage=e.prevPage,this.$emit("on-page-change",t),"remote"===this.mode&&this.$emit("update:isLoading",!0)}},perPageChanged:function(e){this.currentPerPage=e.currentPerPage;var t=this.paginationOptions.position;!this.$refs.paginationTop||"top"!==t&&"both"!==t||(this.$refs.paginationTop.currentPerPage=this.currentPerPage),!this.$refs.paginationBottom||"bottom"!==t&&"both"!==t||(this.$refs.paginationBottom.currentPerPage=this.currentPerPage);var n=this.pageChangedEvent();this.$emit("on-per-page-change",n),"remote"===this.mode&&this.$emit("update:isLoading",!0)},changeSort:function(e){this.sorts=e,this.$emit("on-sort-change",e),this.changePage(1),"remote"!==this.mode?this.sortChanged=!0:this.$emit("update:isLoading",!0)},onCheckboxClicked:function(e,t,n){this.$set(e,"vgtSelected",!e.vgtSelected),this.$emit("on-row-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onRowDoubleClicked:function(e,t,n){this.$emit("on-row-dblclick",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onRowClicked:function(e,t,n){this.selectable&&!this.selectOnCheckboxOnly&&this.$set(e,"vgtSelected",!e.vgtSelected),this.$emit("on-row-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onRowAuxClicked:function(e,t,n){this.$emit("on-row-aux-click",{row:e,pageIndex:t,selected:!!e.vgtSelected,event:n})},onCellClicked:function(e,t,n,a){this.$emit("on-cell-click",{row:e,column:t,rowIndex:n,event:a})},onMouseenter:function(e,t){this.$emit("on-row-mouseenter",{row:e,pageIndex:t})},onMouseleave:function(e,t){this.$emit("on-row-mouseleave",{row:e,pageIndex:t})},searchTableOnEnter:function(){"enter"===this.searchTrigger&&(this.handleSearch(),this.filteredRows=JSON.parse(JSON.stringify(this.originalRows)),this.forceSearch=!0,this.sortChanged=!0)},searchTableOnKeyUp:function(){"enter"!==this.searchTrigger&&this.handleSearch()},resetTable:function(){this.unselectAllInternal(!0),this.changePage(1)},collect:function(e,t){return"function"==typeof t?t(e):"string"==typeof t?function(e,t){for(var n=e,a=t.split("."),r=0;r<a.length;r++){if(null==n)return;n=n[a[r]]}return n}(e,t):void 0},collectFormatted:function(e,t){var n,a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(void 0===(n=a&&t.headerField?this.collect(e,t.headerField):this.collect(e,t.field)))return"";if(t.formatFn&&"function"==typeof t.formatFn)return t.formatFn(n,e);var r=t.typeDef;r||(r=this.dataTypes[t.type]||Y);var s=r.format(n,t);return!this.compactMode||""!=s&&null!=s?s:"-"},formattedRow:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n={},a=0;a<this.typedColumns.length;a++){var r=this.typedColumns[a];r.field&&(n[r.field]=this.collectFormatted(e,r,t))}return n},getClasses:function(e,t,n){var a=this.typedColumns[e],r=a.typeDef,s=a["".concat(t,"Class")],i=r.isRight;this.rtl&&(i=!0);var o={"vgt-right-align":i,"vgt-left-align":!i};return"function"==typeof s?o[s(n)]=!0:"string"==typeof s&&(o[s]=!0),o},filterRows:function(e){var t=this,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.columnFilters=e;var r=JSON.parse(JSON.stringify(this.originalRows)),s=!1;if(this.columnFilters&&Object.keys(this.columnFilters).length){var i=function(){if(("remote"!==t.mode||n)&&t.changePage(1),n&&t.$emit("on-column-filter",{columnFilters:t.columnFilters}),"remote"===t.mode)return n?t.$emit("update:isLoading",!0):t.filteredRows=r,{v:void 0};for(var e=function(e){return"function"==typeof e&&e.name?e.name:e},i=function(n){var i=t.typedColumns[n];t.columnFilters[e(i.field)]&&(s=!0,r.forEach((function(n){var r=n.children.filter((function(n){return i.filterOptions&&"function"==typeof i.filterOptions.filterFn?i.filterOptions.filterFn(t.collect(n,i.field),t.columnFilters[e(i.field)]):i.typeDef.filterPredicate(t.collect(n,i.field),t.columnFilters[e(i.field)],!1,i.filterOptions&&"object"===a(i.filterOptions.filterDropdownItems))}));n.children=r})))},o=0;o<t.typedColumns.length;o++)i(o)}();if("object"===a(i))return i.v}this.filteredRows=s?r.filter((function(e){return e.children&&e.children.length})):r},getCurrentIndex:function(e){for(var t=0,n=!1,a=0;a<this.paginated.length;a+=1){var r=this.paginated[a].children;if(r&&r.length)for(var s=0;s<r.length;s+=1){if(r[s].originalIndex===e){n=!0;break}t+=1}if(n)break}return(this.currentPage-1)*this.currentPerPage+t+1},getRowStyleClass:function(e){var t,n="";return this.hasRowClickListener&&(n+="clickable"),(t="function"==typeof this.rowStyleClass?this.rowStyleClass(e):this.rowStyleClass)&&(n+=" ".concat(t)),n},handleGrouped:function(e){var t=this;return e.forEach((function(e,n){e.vgt_header_id=n,t.groupOptions.maintainExpanded&&t.expandedRowKeys.has(e[t.groupOptions.rowKey])&&t.$set(e,"vgtIsExpanded",!0),e.children.forEach((function(e){e.vgt_id=n}))})),e},initializePagination:function(){var e=this,t=this.paginationOptions,n=t.enabled,a=t.perPage,r=t.position,i=t.perPageDropdown,o=t.perPageDropdownEnabled,d=t.dropdownAllowAll,u=t.firstLabel,l=t.lastLabel,_=t.nextLabel,c=t.prevLabel,m=t.rowsPerPageLabel,h=t.ofLabel,f=t.pageLabel,M=t.allLabel,p=t.setCurrentPage,y=t.mode,g=t.infoFn;if("boolean"==typeof n&&(this.paginate=n),"number"==typeof a&&(this.perPage=a),"top"===r?(this.paginateOnTop=!0,this.paginateOnBottom=!1):"both"===r&&(this.paginateOnTop=!0,this.paginateOnBottom=!0),Array.isArray(i)&&i.length&&(this.customRowsPerPageDropdown=i,!this.perPage)){var L=s(i,1);this.perPage=L[0]}"boolean"==typeof o&&(this.perPageDropdownEnabled=o),"boolean"==typeof d&&(this.paginateDropdownAllowAll=d),"string"==typeof y&&(this.paginationMode=y),"string"==typeof u&&(this.firstText=u),"string"==typeof l&&(this.lastText=l),"string"==typeof _&&(this.nextText=_),"string"==typeof c&&(this.prevText=c),"string"==typeof m&&(this.rowsPerPageText=m),"string"==typeof h&&(this.ofText=h),"string"==typeof f&&(this.pageText=f),"string"==typeof M&&(this.allText=M),"number"==typeof p&&setTimeout((function(){e.changePage(p)}),500),"function"==typeof g&&(this.paginationInfoFn=g)},initializeSearch:function(){var e=this.searchOptions,t=e.enabled,n=e.trigger,a=e.externalQuery,r=e.searchFn,s=e.placeholder,i=e.skipDiacritics;"boolean"==typeof t&&(this.searchEnabled=t),"enter"===n&&(this.searchTrigger=n),"string"==typeof a&&(this.externalSearchQuery=a),"function"==typeof r&&(this.searchFn=r),"string"==typeof s&&(this.searchPlaceholder=s),"boolean"==typeof i&&(this.searchSkipDiacritics=i)},initializeSort:function(){var e=this.sortOptions,t=e.enabled,n=e.initialSortBy,r=e.multipleColumns,s=JSON.parse(JSON.stringify(n||{}));if("boolean"==typeof t&&(this.sortable=t),"boolean"==typeof r&&(this.multipleColumnSort=r),"object"===a(s)){var i=this.fixedHeader?this.$refs["table-header-secondary"]:this.$refs["table-header-primary"];if(Array.isArray(s))i.setInitialSort(s);else Object.prototype.hasOwnProperty.call(s,"field")&&i.setInitialSort([s])}},initializeSelect:function(){var e=this.selectOptions,t=e.enabled,n=e.selectionInfoClass,a=e.selectionText,r=e.clearSelectionText,s=e.selectOnCheckboxOnly,i=e.selectAllByPage,o=e.disableSelectInfo,d=e.selectAllByGroup;"boolean"==typeof t&&(this.selectable=t),"boolean"==typeof s&&(this.selectOnCheckboxOnly=s),"boolean"==typeof i&&(this.selectAllByPage=i),"boolean"==typeof d&&(this.selectAllByGroup=d),"boolean"==typeof o&&(this.disableSelectInfo=o),"string"==typeof n&&(this.selectionInfoClass=n),"string"==typeof a&&(this.selectionText=a),"string"==typeof r&&(this.clearSelectionText=r)}},mounted:function(){this.perPage&&(this.currentPerPage=this.perPage),this.initializeSort()},components:{"vgt-pagination":b,"vgt-global-search":k,"vgt-header-row":S,"vgt-table-header":T}},void 0,!1,void 0,!1,void 0,void 0,void 0),Rt={install:function(e,t){e.component(Et.name,Et)}};"undefined"!=typeof window&&window.Vue&&window.Vue.use(Rt)}).call(this,n("yLpj"))},"9rRi":function(e,t,n){!function(e){"use strict";e.defineLocale("gd",{months:["Am Faoilleach","An Gearran","Am Màrt","An Giblean","An Cèitean","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"],monthsShort:["Faoi","Gear","Màrt","Gibl","Cèit","Ògmh","Iuch","Lùn","Sult","Dàmh","Samh","Dùbh"],monthsParseExact:!0,weekdays:["Didòmhnaich","Diluain","Dimàirt","Diciadain","Diardaoin","Dihaoine","Disathairne"],weekdaysShort:["Did","Dil","Dim","Dic","Dia","Dih","Dis"],weekdaysMin:["Dò","Lu","Mà","Ci","Ar","Ha","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",ss:"%d diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){return e+(1===e?"d":e%10==2?"na":"mh")},week:{dow:1,doy:4}})}(n("wd/R"))},"A+xa":function(e,t,n){!function(e){"use strict";e.defineLocale("cv",{months:"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав".split("_"),monthsShort:"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш".split("_"),weekdays:"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун".split("_"),weekdaysShort:"выр_тун_ытл_юн_кӗҫ_эрн_шӑм".split("_"),weekdaysMin:"вр_тн_ыт_юн_кҫ_эр_шм".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]",LLL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm",LLLL:"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm"},calendar:{sameDay:"[Паян] LT [сехетре]",nextDay:"[Ыран] LT [сехетре]",lastDay:"[Ӗнер] LT [сехетре]",nextWeek:"[Ҫитес] dddd LT [сехетре]",lastWeek:"[Иртнӗ] dddd LT [сехетре]",sameElse:"L"},relativeTime:{future:function(e){return e+(/сехет$/i.exec(e)?"рен":/ҫул$/i.exec(e)?"тан":"ран")},past:"%s каялла",s:"пӗр-ик ҫеккунт",ss:"%d ҫеккунт",m:"пӗр минут",mm:"%d минут",h:"пӗр сехет",hh:"%d сехет",d:"пӗр кун",dd:"%d кун",M:"пӗр уйӑх",MM:"%d уйӑх",y:"пӗр ҫул",yy:"%d ҫул"},dayOfMonthOrdinalParse:/\d{1,2}-мӗш/,ordinal:"%d-мӗш",week:{dow:1,doy:7}})}(n("wd/R"))},AQ68:function(e,t,n){!function(e){"use strict";e.defineLocale("uz-latn",{months:"Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr".split("_"),monthsShort:"Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek".split("_"),weekdays:"Yakshanba_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba".split("_"),weekdaysShort:"Yak_Dush_Sesh_Chor_Pay_Jum_Shan".split("_"),weekdaysMin:"Ya_Du_Se_Cho_Pa_Ju_Sha".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Bugun soat] LT [da]",nextDay:"[Ertaga] LT [da]",nextWeek:"dddd [kuni soat] LT [da]",lastDay:"[Kecha soat] LT [da]",lastWeek:"[O'tgan] dddd [kuni soat] LT [da]",sameElse:"L"},relativeTime:{future:"Yaqin %s ichida",past:"Bir necha %s oldin",s:"soniya",ss:"%d soniya",m:"bir daqiqa",mm:"%d daqiqa",h:"bir soat",hh:"%d soat",d:"bir kun",dd:"%d kun",M:"bir oy",MM:"%d oy",y:"bir yil",yy:"%d yil"},week:{dow:1,doy:7}})}(n("wd/R"))},AvvY:function(e,t,n){!function(e){"use strict";e.defineLocale("ml",{months:"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ".split("_"),monthsShort:"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.".split("_"),monthsParseExact:!0,weekdays:"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച".split("_"),weekdaysShort:"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി".split("_"),weekdaysMin:"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ".split("_"),longDateFormat:{LT:"A h:mm -നു",LTS:"A h:mm:ss -നു",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm -നു",LLLL:"dddd, D MMMM YYYY, A h:mm -നു"},calendar:{sameDay:"[ഇന്ന്] LT",nextDay:"[നാളെ] LT",nextWeek:"dddd, LT",lastDay:"[ഇന്നലെ] LT",lastWeek:"[കഴിഞ്ഞ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s കഴിഞ്ഞ്",past:"%s മുൻപ്",s:"അൽപ നിമിഷങ്ങൾ",ss:"%d സെക്കൻഡ്",m:"ഒരു മിനിറ്റ്",mm:"%d മിനിറ്റ്",h:"ഒരു മണിക്കൂർ",hh:"%d മണിക്കൂർ",d:"ഒരു ദിവസം",dd:"%d ദിവസം",M:"ഒരു മാസം",MM:"%d മാസം",y:"ഒരു വർഷം",yy:"%d വർഷം"},meridiemParse:/രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,meridiemHour:function(e,t){return 12===e&&(e=0),"രാത്രി"===t&&e>=4||"ഉച്ച കഴിഞ്ഞ്"===t||"വൈകുന്നേരം"===t?e+12:e},meridiem:function(e,t,n){return e<4?"രാത്രി":e<12?"രാവിലെ":e<17?"ഉച്ച കഴിഞ്ഞ്":e<20?"വൈകുന്നേരം":"രാത്രി"}})}(n("wd/R"))},B55N:function(e,t,n){!function(e){"use strict";e.defineLocale("ja",{eras:[{since:"2019-05-01",offset:1,name:"令和",narrow:"㋿",abbr:"R"},{since:"1989-01-08",until:"2019-04-30",offset:1,name:"平成",narrow:"㍻",abbr:"H"},{since:"1926-12-25",until:"1989-01-07",offset:1,name:"昭和",narrow:"㍼",abbr:"S"},{since:"1912-07-30",until:"1926-12-24",offset:1,name:"大正",narrow:"㍽",abbr:"T"},{since:"1873-01-01",until:"1912-07-29",offset:6,name:"明治",narrow:"㍾",abbr:"M"},{since:"0001-01-01",until:"1873-12-31",offset:1,name:"西暦",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"紀元前",narrow:"BC",abbr:"BC"}],eraYearOrdinalRegex:/(元|\d+)年/,eraYearOrdinalParse:function(e,t){return"元"===t[1]?1:parseInt(t[1]||e,10)},months:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日 dddd HH:mm",l:"YYYY/MM/DD",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日(ddd) HH:mm"},meridiemParse:/午前|午後/i,isPM:function(e){return"午後"===e},meridiem:function(e,t,n){return e<12?"午前":"午後"},calendar:{sameDay:"[今日] LT",nextDay:"[明日] LT",nextWeek:function(e){return e.week()!==this.week()?"[来週]dddd LT":"dddd LT"},lastDay:"[昨日] LT",lastWeek:function(e){return this.week()!==e.week()?"[先週]dddd LT":"dddd LT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}日/,ordinal:function(e,t){switch(t){case"y":return 1===e?"元年":e+"年";case"d":case"D":case"DDD":return e+"日";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"数秒",ss:"%d秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日",dd:"%d日",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}})}(n("wd/R"))},BVg3:function(e,t,n){!function(e){"use strict";function t(e){return e%100==11||e%10!=1}function n(e,n,a,r){var s=e+" ";switch(a){case"s":return n||r?"nokkrar sekúndur":"nokkrum sekúndum";case"ss":return t(e)?s+(n||r?"sekúndur":"sekúndum"):s+"sekúnda";case"m":return n?"mínúta":"mínútu";case"mm":return t(e)?s+(n||r?"mínútur":"mínútum"):n?s+"mínúta":s+"mínútu";case"hh":return t(e)?s+(n||r?"klukkustundir":"klukkustundum"):s+"klukkustund";case"d":return n?"dagur":r?"dag":"degi";case"dd":return t(e)?n?s+"dagar":s+(r?"daga":"dögum"):n?s+"dagur":s+(r?"dag":"degi");case"M":return n?"mánuður":r?"mánuð":"mánuði";case"MM":return t(e)?n?s+"mánuðir":s+(r?"mánuði":"mánuðum"):n?s+"mánuður":s+(r?"mánuð":"mánuði");case"y":return n||r?"ár":"ári";case"yy":return t(e)?s+(n||r?"ár":"árum"):s+(n||r?"ár":"ári")}}e.defineLocale("is",{months:"janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember".split("_"),monthsShort:"jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des".split("_"),weekdays:"sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur".split("_"),weekdaysShort:"sun_mán_þri_mið_fim_fös_lau".split("_"),weekdaysMin:"Su_Má_Þr_Mi_Fi_Fö_La".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd, D. MMMM YYYY [kl.] H:mm"},calendar:{sameDay:"[í dag kl.] LT",nextDay:"[á morgun kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[í gær kl.] LT",lastWeek:"[síðasta] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"eftir %s",past:"fyrir %s síðan",s:n,ss:n,m:n,mm:n,h:"klukkustund",hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},ByF4:function(e,t,n){!function(e){"use strict";e.defineLocale("fo",{months:"januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur".split("_"),weekdaysShort:"sun_mán_týs_mik_hós_frí_ley".split("_"),weekdaysMin:"su_má_tý_mi_hó_fr_le".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D. MMMM, YYYY HH:mm"},calendar:{sameDay:"[Í dag kl.] LT",nextDay:"[Í morgin kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Í gjár kl.] LT",lastWeek:"[síðstu] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"um %s",past:"%s síðani",s:"fá sekund",ss:"%d sekundir",m:"ein minuttur",mm:"%d minuttir",h:"ein tími",hh:"%d tímar",d:"ein dagur",dd:"%d dagar",M:"ein mánaður",MM:"%d mánaðir",y:"eitt ár",yy:"%d ár"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},CjzT:function(e,t,n){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),n="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),a=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es-do",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(n("wd/R"))},CoRJ:function(e,t,n){!function(e){"use strict";e.defineLocale("ar-ma",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اثنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}})}(n("wd/R"))},"D/JM":function(e,t,n){!function(e){"use strict";e.defineLocale("eu",{months:"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"),monthsShort:"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"),monthsParseExact:!0,weekdays:"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"),weekdaysShort:"ig._al._ar._az._og._ol._lr.".split("_"),weekdaysMin:"ig_al_ar_az_og_ol_lr".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY[ko] MMMM[ren] D[a]",LLL:"YYYY[ko] MMMM[ren] D[a] HH:mm",LLLL:"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm",l:"YYYY-M-D",ll:"YYYY[ko] MMM D[a]",lll:"YYYY[ko] MMM D[a] HH:mm",llll:"ddd, YYYY[ko] MMM D[a] HH:mm"},calendar:{sameDay:"[gaur] LT[etan]",nextDay:"[bihar] LT[etan]",nextWeek:"dddd LT[etan]",lastDay:"[atzo] LT[etan]",lastWeek:"[aurreko] dddd LT[etan]",sameElse:"L"},relativeTime:{future:"%s barru",past:"duela %s",s:"segundo batzuk",ss:"%d segundo",m:"minutu bat",mm:"%d minutu",h:"ordu bat",hh:"%d ordu",d:"egun bat",dd:"%d egun",M:"hilabete bat",MM:"%d hilabete",y:"urte bat",yy:"%d urte"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(n("wd/R"))},"DKr+":function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={s:["thoddea sekondamni","thodde sekond"],ss:[e+" sekondamni",e+" sekond"],m:["eka mintan","ek minut"],mm:[e+" mintamni",e+" mintam"],h:["eka voran","ek vor"],hh:[e+" voramni",e+" voram"],d:["eka disan","ek dis"],dd:[e+" disamni",e+" dis"],M:["eka mhoinean","ek mhoino"],MM:[e+" mhoineamni",e+" mhoine"],y:["eka vorsan","ek voros"],yy:[e+" vorsamni",e+" vorsam"]};return a?r[n][0]:r[n][1]}e.defineLocale("gom-latn",{months:{standalone:"Janer_Febrer_Mars_Abril_Mai_Jun_Julai_Agost_Setembr_Otubr_Novembr_Dezembr".split("_"),format:"Janerachea_Febrerachea_Marsachea_Abrilachea_Maiachea_Junachea_Julaiachea_Agostachea_Setembrachea_Otubrachea_Novembrachea_Dezembrachea".split("_"),isFormat:/MMMM(\s)+D[oD]?/},monthsShort:"Jan._Feb._Mars_Abr._Mai_Jun_Jul._Ago._Set._Otu._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Aitar_Somar_Mongllar_Budhvar_Birestar_Sukrar_Son'var".split("_"),weekdaysShort:"Ait._Som._Mon._Bud._Bre._Suk._Son.".split("_"),weekdaysMin:"Ai_Sm_Mo_Bu_Br_Su_Sn".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [vazta]",LTS:"A h:mm:ss [vazta]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [vazta]",LLLL:"dddd, MMMM Do, YYYY, A h:mm [vazta]",llll:"ddd, D MMM YYYY, A h:mm [vazta]"},calendar:{sameDay:"[Aiz] LT",nextDay:"[Faleam] LT",nextWeek:"[Fuddlo] dddd[,] LT",lastDay:"[Kal] LT",lastWeek:"[Fattlo] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s adim",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}(er)/,ordinal:function(e,t){switch(t){case"D":return e+"er";default:case"M":case"Q":case"DDD":case"d":case"w":case"W":return e}},week:{dow:0,doy:3},meridiemParse:/rati|sokallim|donparam|sanje/,meridiemHour:function(e,t){return 12===e&&(e=0),"rati"===t?e<4?e:e+12:"sokallim"===t?e:"donparam"===t?e>12?e:e+12:"sanje"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"rati":e<12?"sokallim":e<16?"donparam":e<20?"sanje":"rati"}})}(n("wd/R"))},Dkky:function(e,t,n){!function(e){"use strict";e.defineLocale("fr-ch",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsParseExact:!0,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|e)/,ordinal:function(e,t){switch(t){default:case"M":case"Q":case"D":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})}(n("wd/R"))},Dmvi:function(e,t,n){!function(e){"use strict";e.defineLocale("en-au",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:0,doy:4}})}(n("wd/R"))},DoHr:function(e,t,n){!function(e){"use strict";var t={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};e.defineLocale("tr",{months:"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split("_"),monthsShort:"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pzt_Sal_Çar_Per_Cum_Cmt".split("_"),weekdaysMin:"Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),meridiem:function(e,t,n){return e<12?n?"öö":"ÖÖ":n?"ös":"ÖS"},meridiemParse:/öö|ÖÖ|ös|ÖS/,isPM:function(e){return"ös"===e||"ÖS"===e},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[yarın saat] LT",nextWeek:"[gelecek] dddd [saat] LT",lastDay:"[dün] LT",lastWeek:"[geçen] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s önce",s:"birkaç saniye",ss:"%d saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",w:"bir hafta",ww:"%d hafta",M:"bir ay",MM:"%d ay",y:"bir yıl",yy:"%d yıl"},ordinal:function(e,n){switch(n){case"d":case"D":case"Do":case"DD":return e;default:if(0===e)return e+"'ıncı";var a=e%10;return e+(t[a]||t[e%100-a]||t[e>=100?100:null])}},week:{dow:1,doy:7}})}(n("wd/R"))},DxQv:function(e,t,n){!function(e){"use strict";e.defineLocale("da",{months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"søn_man_tir_ons_tor_fre_lør".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd [d.] D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"på dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[i] dddd[s kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"få sekunder",ss:"%d sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en måned",MM:"%d måneder",y:"et år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},Dzi0:function(e,t,n){!function(e){"use strict";e.defineLocale("tl-ph",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"LT [ngayong araw]",nextDay:"[Bukas ng] LT",nextWeek:"LT [sa susunod na] dddd",lastDay:"LT [kahapon]",lastWeek:"LT [noong nakaraang] dddd",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",ss:"%d segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}})}(n("wd/R"))},"E+lV":function(e,t,n){!function(e){"use strict";var t={words:{ss:["секунда","секунде","секунди"],m:["један минут","једног минута"],mm:["минут","минута","минута"],h:["један сат","једног сата"],hh:["сат","сата","сати"],d:["један дан","једног дана"],dd:["дан","дана","дана"],M:["један месец","једног месеца"],MM:["месец","месеца","месеци"],y:["једну годину","једне године"],yy:["годину","године","година"]},correctGrammaticalCase:function(e,t){return e%10>=1&&e%10<=4&&(e%100<10||e%100>=20)?e%10==1?t[0]:t[1]:t[2]},translate:function(e,n,a,r){var s,i=t.words[a];return 1===a.length?"y"===a&&n?"једна година":r||n?i[0]:i[1]:(s=t.correctGrammaticalCase(e,i),"yy"===a&&n&&"годину"===s?e+" година":e+" "+s)}};e.defineLocale("sr-cyrl",{months:"јануар_фебруар_март_април_мај_јун_јул_август_септембар_октобар_новембар_децембар".split("_"),monthsShort:"јан._феб._мар._апр._мај_јун_јул_авг._сеп._окт._нов._дец.".split("_"),monthsParseExact:!0,weekdays:"недеља_понедељак_уторак_среда_четвртак_петак_субота".split("_"),weekdaysShort:"нед._пон._уто._сре._чет._пет._суб.".split("_"),weekdaysMin:"не_по_ут_ср_че_пе_су".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D. M. YYYY.",LL:"D. MMMM YYYY.",LLL:"D. MMMM YYYY. H:mm",LLLL:"dddd, D. MMMM YYYY. H:mm"},calendar:{sameDay:"[данас у] LT",nextDay:"[сутра у] LT",nextWeek:function(){switch(this.day()){case 0:return"[у] [недељу] [у] LT";case 3:return"[у] [среду] [у] LT";case 6:return"[у] [суботу] [у] LT";case 1:case 2:case 4:case 5:return"[у] dddd [у] LT"}},lastDay:"[јуче у] LT",lastWeek:function(){return["[прошле] [недеље] [у] LT","[прошлог] [понедељка] [у] LT","[прошлог] [уторка] [у] LT","[прошле] [среде] [у] LT","[прошлог] [четвртка] [у] LT","[прошлог] [петка] [у] LT","[прошле] [суботе] [у] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"за %s",past:"пре %s",s:"неколико секунди",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:t.translate,dd:t.translate,M:t.translate,MM:t.translate,y:t.translate,yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(n("wd/R"))},EOgW:function(e,t,n){!function(e){"use strict";e.defineLocale("th",{months:"มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),monthsShort:"ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.".split("_"),monthsParseExact:!0,weekdays:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),weekdaysShort:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),weekdaysMin:"อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY เวลา H:mm",LLLL:"วันddddที่ D MMMM YYYY เวลา H:mm"},meridiemParse:/ก่อนเที่ยง|หลังเที่ยง/,isPM:function(e){return"หลังเที่ยง"===e},meridiem:function(e,t,n){return e<12?"ก่อนเที่ยง":"หลังเที่ยง"},calendar:{sameDay:"[วันนี้ เวลา] LT",nextDay:"[พรุ่งนี้ เวลา] LT",nextWeek:"dddd[หน้า เวลา] LT",lastDay:"[เมื่อวานนี้ เวลา] LT",lastWeek:"[วัน]dddd[ที่แล้ว เวลา] LT",sameElse:"L"},relativeTime:{future:"อีก %s",past:"%sที่แล้ว",s:"ไม่กี่วินาที",ss:"%d วินาที",m:"1 นาที",mm:"%d นาที",h:"1 ชั่วโมง",hh:"%d ชั่วโมง",d:"1 วัน",dd:"%d วัน",w:"1 สัปดาห์",ww:"%d สัปดาห์",M:"1 เดือน",MM:"%d เดือน",y:"1 ปี",yy:"%d ปี"}})}(n("wd/R"))},Ed67:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var a=n("XuX8"),r=n.n(a),s=n("tC49"),i=n("xjcK"),o=n("pyNs"),d=n("z3V6"),u=Object(d.d)({id:Object(d.c)(o.t),inline:Object(d.c)(o.g,!1),novalidate:Object(d.c)(o.g,!1),validated:Object(d.c)(o.g,!1)},i.v),l=r.a.extend({name:i.v,functional:!0,props:u,render:function(e,t){var n=t.props,a=t.data,r=t.children;return e("form",Object(s.a)(a,{class:{"form-inline":n.inline,"was-validated":n.validated},attrs:{id:n.id,novalidate:n.novalidate}}),r)}})},Fnuy:function(e,t,n){!function(e){"use strict";e.defineLocale("oc-lnc",{months:{standalone:"genièr_febrièr_març_abril_mai_junh_julhet_agost_setembre_octòbre_novembre_decembre".split("_"),format:"de genièr_de febrièr_de març_d'abril_de mai_de junh_de julhet_d'agost_de setembre_d'octòbre_de novembre_de decembre".split("_"),isFormat:/D[oD]?(\s)+MMMM/},monthsShort:"gen._febr._març_abr._mai_junh_julh._ago._set._oct._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"dimenge_diluns_dimars_dimècres_dijòus_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dm._dc._dj._dv._ds.".split("_"),weekdaysMin:"dg_dl_dm_dc_dj_dv_ds".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [de] YYYY",ll:"D MMM YYYY",LLL:"D MMMM [de] YYYY [a] H:mm",lll:"D MMM YYYY, H:mm",LLLL:"dddd D MMMM [de] YYYY [a] H:mm",llll:"ddd D MMM YYYY, H:mm"},calendar:{sameDay:"[uèi a] LT",nextDay:"[deman a] LT",nextWeek:"dddd [a] LT",lastDay:"[ièr a] LT",lastWeek:"dddd [passat a] LT",sameElse:"L"},relativeTime:{future:"d'aquí %s",past:"fa %s",s:"unas segondas",ss:"%d segondas",m:"una minuta",mm:"%d minutas",h:"una ora",hh:"%d oras",d:"un jorn",dd:"%d jorns",M:"un mes",MM:"%d meses",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(r|n|t|è|a)/,ordinal:function(e,t){var n=1===e?"r":2===e?"n":3===e?"r":4===e?"t":"è";return"w"!==t&&"W"!==t||(n="a"),e+n},week:{dow:1,doy:4}})}(n("wd/R"))},G0Uy:function(e,t,n){!function(e){"use strict";e.defineLocale("mt",{months:"Jannar_Frar_Marzu_April_Mejju_Ġunju_Lulju_Awwissu_Settembru_Ottubru_Novembru_Diċembru".split("_"),monthsShort:"Jan_Fra_Mar_Apr_Mej_Ġun_Lul_Aww_Set_Ott_Nov_Diċ".split("_"),weekdays:"Il-Ħadd_It-Tnejn_It-Tlieta_L-Erbgħa_Il-Ħamis_Il-Ġimgħa_Is-Sibt".split("_"),weekdaysShort:"Ħad_Tne_Tli_Erb_Ħam_Ġim_Sib".split("_"),weekdaysMin:"Ħa_Tn_Tl_Er_Ħa_Ġi_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Illum fil-]LT",nextDay:"[Għada fil-]LT",nextWeek:"dddd [fil-]LT",lastDay:"[Il-bieraħ fil-]LT",lastWeek:"dddd [li għadda] [fil-]LT",sameElse:"L"},relativeTime:{future:"f’ %s",past:"%s ilu",s:"ftit sekondi",ss:"%d sekondi",m:"minuta",mm:"%d minuti",h:"siegħa",hh:"%d siegħat",d:"ġurnata",dd:"%d ġranet",M:"xahar",MM:"%d xhur",y:"sena",yy:"%d sni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(n("wd/R"))},H8ED:function(e,t,n){!function(e){"use strict";function t(e,t,n){var a,r;return"m"===n?t?"хвіліна":"хвіліну":"h"===n?t?"гадзіна":"гадзіну":e+" "+(a=+e,r={ss:t?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:t?"хвіліна_хвіліны_хвілін":"хвіліну_хвіліны_хвілін",hh:t?"гадзіна_гадзіны_гадзін":"гадзіну_гадзіны_гадзін",dd:"дзень_дні_дзён",MM:"месяц_месяцы_месяцаў",yy:"год_гады_гадоў"}[n].split("_"),a%10==1&&a%100!=11?r[0]:a%10>=2&&a%10<=4&&(a%100<10||a%100>=20)?r[1]:r[2])}e.defineLocale("be",{months:{format:"студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня".split("_"),standalone:"студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань".split("_")},monthsShort:"студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж".split("_"),weekdays:{format:"нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу".split("_"),standalone:"нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота".split("_"),isFormat:/\[ ?[Ууў] ?(?:мінулую|наступную)? ?\] ?dddd/},weekdaysShort:"нд_пн_ат_ср_чц_пт_сб".split("_"),weekdaysMin:"нд_пн_ат_ср_чц_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., HH:mm",LLLL:"dddd, D MMMM YYYY г., HH:mm"},calendar:{sameDay:"[Сёння ў] LT",nextDay:"[Заўтра ў] LT",lastDay:"[Учора ў] LT",nextWeek:function(){return"[У] dddd [ў] LT"},lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return"[У мінулую] dddd [ў] LT";case 1:case 2:case 4:return"[У мінулы] dddd [ў] LT"}},sameElse:"L"},relativeTime:{future:"праз %s",past:"%s таму",s:"некалькі секунд",m:t,mm:t,h:t,hh:t,d:"дзень",dd:t,M:"месяц",MM:t,y:"год",yy:t},meridiemParse:/ночы|раніцы|дня|вечара/,isPM:function(e){return/^(дня|вечара)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночы":e<12?"раніцы":e<17?"дня":"вечара"},dayOfMonthOrdinalParse:/\d{1,2}-(і|ы|га)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e%10!=2&&e%10!=3||e%100==12||e%100==13?e+"-ы":e+"-і";case"D":return e+"-га";default:return e}},week:{dow:1,doy:7}})}(n("wd/R"))},HP3h:function(e,t,n){!function(e){"use strict";var t={1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",0:"0"},n=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},a={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},r=function(e){return function(t,r,s,i){var o=n(t),d=a[e][n(t)];return 2===o&&(d=d[r?0:1]),d.replace(/%d/i,t)}},s=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];e.defineLocale("ar-ly",{months:s,monthsShort:s,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:r("s"),ss:r("s"),m:r("m"),mm:r("m"),h:r("h"),hh:r("h"),d:r("d"),dd:r("d"),M:r("M"),MM:r("M"),y:r("y"),yy:r("y")},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}})}(n("wd/R"))},IBtZ:function(e,t,n){!function(e){"use strict";e.defineLocale("ka",{months:"იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი".split("_"),monthsShort:"იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ".split("_"),weekdays:{standalone:"კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი".split("_"),format:"კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს".split("_"),isFormat:/(წინა|შემდეგ)/},weekdaysShort:"კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ".split("_"),weekdaysMin:"კვ_ორ_სა_ოთ_ხუ_პა_შა".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[დღეს] LT[-ზე]",nextDay:"[ხვალ] LT[-ზე]",lastDay:"[გუშინ] LT[-ზე]",nextWeek:"[შემდეგ] dddd LT[-ზე]",lastWeek:"[წინა] dddd LT-ზე",sameElse:"L"},relativeTime:{future:function(e){return e.replace(/(წამ|წუთ|საათ|წელ|დღ|თვ)(ი|ე)/,(function(e,t,n){return"ი"===n?t+"ში":t+n+"ში"}))},past:function(e){return/(წამი|წუთი|საათი|დღე|თვე)/.test(e)?e.replace(/(ი|ე)$/,"ის წინ"):/წელი/.test(e)?e.replace(/წელი$/,"წლის წინ"):e},s:"რამდენიმე წამი",ss:"%d წამი",m:"წუთი",mm:"%d წუთი",h:"საათი",hh:"%d საათი",d:"დღე",dd:"%d დღე",M:"თვე",MM:"%d თვე",y:"წელი",yy:"%d წელი"},dayOfMonthOrdinalParse:/0|1-ლი|მე-\d{1,2}|\d{1,2}-ე/,ordinal:function(e){return 0===e?e:1===e?e+"-ლი":e<20||e<=100&&e%20==0||e%100==0?"მე-"+e:e+"-ე"},week:{dow:1,doy:7}})}(n("wd/R"))},"Ivi+":function(e,t,n){!function(e){"use strict";e.defineLocale("ko",{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 A h:mm",LLLL:"YYYY년 MMMM D일 dddd A h:mm",l:"YYYY.MM.DD.",ll:"YYYY년 MMMM D일",lll:"YYYY년 MMMM D일 A h:mm",llll:"YYYY년 MMMM D일 dddd A h:mm"},calendar:{sameDay:"오늘 LT",nextDay:"내일 LT",nextWeek:"dddd LT",lastDay:"어제 LT",lastWeek:"지난주 dddd LT",sameElse:"L"},relativeTime:{future:"%s 후",past:"%s 전",s:"몇 초",ss:"%d초",m:"1분",mm:"%d분",h:"한 시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한 달",MM:"%d달",y:"일 년",yy:"%d년"},dayOfMonthOrdinalParse:/\d{1,2}(일|월|주)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"일";case"M":return e+"월";case"w":case"W":return e+"주";default:return e}},meridiemParse:/오전|오후/,isPM:function(e){return"오후"===e},meridiem:function(e,t,n){return e<12?"오전":"오후"}})}(n("wd/R"))},"JCF/":function(e,t,n){!function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},n={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},a=["کانونی دووەم","شوبات","ئازار","نیسان","ئایار","حوزەیران","تەمموز","ئاب","ئەیلوول","تشرینی یەكەم","تشرینی دووەم","كانونی یەکەم"];e.defineLocale("ku",{months:a,monthsShort:a,weekdays:"یه‌كشه‌ممه‌_دووشه‌ممه‌_سێشه‌ممه‌_چوارشه‌ممه‌_پێنجشه‌ممه‌_هه‌ینی_شه‌ممه‌".split("_"),weekdaysShort:"یه‌كشه‌م_دووشه‌م_سێشه‌م_چوارشه‌م_پێنجشه‌م_هه‌ینی_شه‌ممه‌".split("_"),weekdaysMin:"ی_د_س_چ_پ_ه_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/ئێواره‌|به‌یانی/,isPM:function(e){return/ئێواره‌/.test(e)},meridiem:function(e,t,n){return e<12?"به‌یانی":"ئێواره‌"},calendar:{sameDay:"[ئه‌مرۆ كاتژمێر] LT",nextDay:"[به‌یانی كاتژمێر] LT",nextWeek:"dddd [كاتژمێر] LT",lastDay:"[دوێنێ كاتژمێر] LT",lastWeek:"dddd [كاتژمێر] LT",sameElse:"L"},relativeTime:{future:"له‌ %s",past:"%s",s:"چه‌ند چركه‌یه‌ك",ss:"چركه‌ %d",m:"یه‌ك خوله‌ك",mm:"%d خوله‌ك",h:"یه‌ك كاتژمێر",hh:"%d كاتژمێر",d:"یه‌ك ڕۆژ",dd:"%d ڕۆژ",M:"یه‌ك مانگ",MM:"%d مانگ",y:"یه‌ك ساڵ",yy:"%d ساڵ"},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}})}(n("wd/R"))},JVSJ:function(e,t,n){!function(e){"use strict";function t(e,t,n){var a=e+" ";switch(n){case"ss":return a+=1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi";case"m":return t?"jedna minuta":"jedne minute";case"mm":return a+=1===e?"minuta":2===e||3===e||4===e?"minute":"minuta";case"h":return t?"jedan sat":"jednog sata";case"hh":return a+=1===e?"sat":2===e||3===e||4===e?"sata":"sati";case"dd":return a+=1===e?"dan":"dana";case"MM":return a+=1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci";case"yy":return a+=1===e?"godina":2===e||3===e||4===e?"godine":"godina"}}e.defineLocale("bs",{months:"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:t,m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(n("wd/R"))},JtJI:function(e,t,n){"use strict";n.d(t,"a",(function(){return J}));var a,r=n("XuX8"),s=n.n(r),i=n("xjcK"),o=n("AFYn"),d=n("pyNs"),u=n("bUBZ"),l=n("kGy3"),_=n("ex6f"),c=n("qMhD"),m=n("OljW"),h=n("2C+6"),f=n("z3V6"),M=n("m/oX"),p=n("m3aq"),y=n("Iyau"),g=n("a3f1"),L=n("WPLV"),Y=n("+nMp"),w=n("aGvM"),b=n("jBgq"),k=n("qlm0");function v(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?v(Object(n),!0).forEach((function(t){T(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function T(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var S=Object(L.a)("value",{type:d.i,defaultValue:null,validator:function(e){return!(!Object(_.g)(e)&&Object(m.b)(e,0)<1)||(Object(w.a)('"v-model" value must be a number greater than "0"',i.eb),!1)}}),j=S.mixin,x=S.props,H=S.prop,P=S.event,O=function(e){var t=Object(m.b)(e)||1;return t<1?5:t},C=function(e,t){var n=Object(m.b)(e)||1;return n>t?t:n<1?1:n},E=function(e){if(e.keyCode===M.j)return Object(g.f)(e,{immediatePropagation:!0}),e.currentTarget.click(),!1},R=Object(f.d)(Object(h.m)(D(D({},x),{},{align:Object(f.c)(d.t,"left"),ariaLabel:Object(f.c)(d.t,"Pagination"),disabled:Object(f.c)(d.g,!1),ellipsisClass:Object(f.c)(d.e),ellipsisText:Object(f.c)(d.t,"…"),firstClass:Object(f.c)(d.e),firstNumber:Object(f.c)(d.g,!1),firstText:Object(f.c)(d.t,"«"),hideEllipsis:Object(f.c)(d.g,!1),hideGotoEndButtons:Object(f.c)(d.g,!1),labelFirstPage:Object(f.c)(d.t,"Go to first page"),labelLastPage:Object(f.c)(d.t,"Go to last page"),labelNextPage:Object(f.c)(d.t,"Go to next page"),labelPage:Object(f.c)(d.l,"Go to page"),labelPrevPage:Object(f.c)(d.t,"Go to previous page"),lastClass:Object(f.c)(d.e),lastNumber:Object(f.c)(d.g,!1),lastText:Object(f.c)(d.t,"»"),limit:Object(f.c)(d.o,5,(function(e){return!(Object(m.b)(e,0)<1)||(Object(w.a)('Prop "limit" must be a number greater than "0"',i.eb),!1)})),nextClass:Object(f.c)(d.e),nextText:Object(f.c)(d.t,"›"),pageClass:Object(f.c)(d.e),pills:Object(f.c)(d.g,!1),prevClass:Object(f.c)(d.e),prevText:Object(f.c)(d.t,"‹"),size:Object(f.c)(d.t)})),"pagination"),F=s.a.extend({mixins:[j,b.a],props:R,data:function(){var e=Object(m.b)(this[H],0);return{currentPage:e=e>0?e:-1,localNumberOfPages:1,localLimit:5}},computed:{btnSize:function(){var e=this.size;return e?"pagination-".concat(e):""},alignment:function(){var e=this.align;return"center"===e?"justify-content-center":"end"===e||"right"===e?"justify-content-end":"fill"===e?"text-center":""},styleClass:function(){return this.pills?"b-pagination-pills":""},computedCurrentPage:function(){return C(this.currentPage,this.localNumberOfPages)},paginationParams:function(){var e=this.localLimit,t=this.localNumberOfPages,n=this.computedCurrentPage,a=this.hideEllipsis,r=this.firstNumber,s=this.lastNumber,i=!1,o=!1,d=e,u=1;t<=e?d=t:n<e-1&&e>3?(a&&!s||(o=!0,d=e-(r?0:1)),d=Object(c.d)(d,e)):t-n+2<e&&e>3?(a&&!r||(i=!0,d=e-(s?0:1)),u=t-d+1):(e>3&&(d=e-(a?0:2),i=!(a&&!r),o=!(a&&!s)),u=n-Object(c.b)(d/2)),u<1?(u=1,i=!1):u>t-d&&(u=t-d+1,o=!1),i&&r&&u<4&&(d+=2,u=1,i=!1);var l=u+d-1;return o&&s&&l>t-3&&(d+=l===t-2?2:3,o=!1),e<=3&&(r&&1===u?d=Object(c.d)(d+1,t,e+1):s&&t===u+d-1&&(u=Object(c.c)(u-1,1),d=Object(c.d)(t-u+1,t,e+1))),{showFirstDots:i,showLastDots:o,numberOfLinks:d=Object(c.d)(d,t-u+1),startNumber:u}},pageList:function(){var e=this.paginationParams,t=e.numberOfLinks,n=e.startNumber,a=this.computedCurrentPage,r=function(e,t){return Object(y.c)(t,(function(t,n){return{number:e+n,classes:null}}))}(n,t);if(r.length>3){var s=a-n,i="bv-d-xs-down-none";if(0===s)for(var o=3;o<r.length;o++)r[o].classes=i;else if(s===r.length-1)for(var d=0;d<r.length-3;d++)r[d].classes=i;else{for(var u=0;u<s-1;u++)r[u].classes=i;for(var l=r.length-1;l>s+1;l--)r[l].classes=i}}return r}},watch:(a={},T(a,H,(function(e,t){e!==t&&(this.currentPage=C(e,this.localNumberOfPages))})),T(a,"currentPage",(function(e,t){e!==t&&this.$emit(P,e>0?e:null)})),T(a,"limit",(function(e,t){e!==t&&(this.localLimit=O(e))})),a),created:function(){var e=this;this.localLimit=O(this.limit),this.$nextTick((function(){e.currentPage=e.currentPage>e.localNumberOfPages?e.localNumberOfPages:e.currentPage}))},methods:{handleKeyNav:function(e){var t=e.keyCode,n=e.shiftKey;this.isNav||(t===M.f||t===M.k?(Object(g.f)(e,{propagation:!1}),n?this.focusFirst():this.focusPrev()):t!==M.i&&t!==M.a||(Object(g.f)(e,{propagation:!1}),n?this.focusLast():this.focusNext()))},getButtons:function(){return Object(l.D)("button.page-link, a.page-link",this.$el).filter((function(e){return Object(l.u)(e)}))},focusCurrent:function(){var e=this;this.$nextTick((function(){var t=e.getButtons().find((function(t){return Object(m.b)(Object(l.h)(t,"aria-posinset"),0)===e.computedCurrentPage}));Object(l.d)(t)||e.focusFirst()}))},focusFirst:function(){var e=this;this.$nextTick((function(){var t=e.getButtons().find((function(e){return!Object(l.r)(e)}));Object(l.d)(t)}))},focusLast:function(){var e=this;this.$nextTick((function(){var t=e.getButtons().reverse().find((function(e){return!Object(l.r)(e)}));Object(l.d)(t)}))},focusPrev:function(){var e=this;this.$nextTick((function(){var t=e.getButtons(),n=t.indexOf(Object(l.g)());n>0&&!Object(l.r)(t[n-1])&&Object(l.d)(t[n-1])}))},focusNext:function(){var e=this;this.$nextTick((function(){var t=e.getButtons(),n=t.indexOf(Object(l.g)());n<t.length-1&&!Object(l.r)(t[n+1])&&Object(l.d)(t[n+1])}))}},render:function(e){var t=this,n=this.disabled,a=this.labelPage,r=this.ariaLabel,s=this.isNav,i=this.localNumberOfPages,o=this.computedCurrentPage,d=this.pageList.map((function(e){return e.number})),u=this.paginationParams,l=u.showFirstDots,c=u.showLastDots,m="fill"===this.align,h=[],M=function(e){return e===o},y=this.currentPage<1,g=function(a,r,o,d,u,l,_){var c=n||M(l)||y||a<1||a>i,h=a<1?1:a>i?i:a,f={disabled:c,page:h,index:h-1},p=t.normalizeSlot(o,f)||Object(Y.g)(d)||e(),g=e(c?"span":s?k.a:"button",{staticClass:"page-link",class:{"flex-grow-1":!s&&!c&&m},props:c||!s?{}:t.linkProps(a),attrs:{role:s?null:"menuitem",type:s||c?null:"button",tabindex:c||s?null:"-1","aria-label":r,"aria-controls":t.ariaControls||null,"aria-disabled":c?"true":null},on:c?{}:{"!click":function(e){t.onClick(e,a)},keydown:E}},[p]);return e("li",{key:_,staticClass:"page-item",class:[{disabled:c,"flex-fill":m,"d-flex":m&&!s&&!c},u],attrs:{role:s?null:"presentation","aria-hidden":c?"true":null}},[g])},L=function(n){return e("li",{staticClass:"page-item",class:["disabled","bv-d-xs-down-none",m?"flex-fill":"",t.ellipsisClass],attrs:{role:"separator"},key:"ellipsis-".concat(n?"last":"first")},[e("span",{staticClass:"page-link"},[t.normalizeSlot(p.k)||Object(Y.g)(t.ellipsisText)||e()])])},w=function(r,o){var d=r.number,u=M(d)&&!y,l=n?null:u||y&&0===o?"0":"-1",c={role:s?null:"menuitemradio",type:s||n?null:"button","aria-disabled":n?"true":null,"aria-controls":t.ariaControls||null,"aria-label":Object(f.b)(a)?a(d):"".concat(Object(_.f)(a)?a():a," ").concat(d),"aria-checked":s?null:u?"true":"false","aria-current":s&&u?"page":null,"aria-posinset":s?null:d,"aria-setsize":s?null:i,tabindex:s?null:l},h=Object(Y.g)(t.makePage(d)),g={page:d,index:d-1,content:h,active:u,disabled:n},L=e(n?"span":s?k.a:"button",{props:n||!s?{}:t.linkProps(d),staticClass:"page-link",class:{"flex-grow-1":!s&&!n&&m},attrs:c,on:n?{}:{"!click":function(e){t.onClick(e,d)},keydown:E}},[t.normalizeSlot(p.F,g)||h]);return e("li",{staticClass:"page-item",class:[{disabled:n,active:u,"flex-fill":m,"d-flex":m&&!s&&!n},r.classes,t.pageClass],attrs:{role:s?null:"presentation"},key:"page-".concat(d)},[L])},b=e();this.firstNumber||this.hideGotoEndButtons||(b=g(1,this.labelFirstPage,p.p,this.firstText,this.firstClass,1,"pagination-goto-first")),h.push(b),h.push(g(o-1,this.labelPrevPage,p.I,this.prevText,this.prevClass,1,"pagination-goto-prev")),h.push(this.firstNumber&&1!==d[0]?w({number:1},0):e()),h.push(l?L(!1):e()),this.pageList.forEach((function(e,n){var a=l&&t.firstNumber&&1!==d[0]?1:0;h.push(w(e,n+a))})),h.push(c?L(!0):e()),h.push(this.lastNumber&&d[d.length-1]!==i?w({number:i},-1):e()),h.push(g(o+1,this.labelNextPage,p.E,this.nextText,this.nextClass,i,"pagination-goto-next"));var v=e();this.lastNumber||this.hideGotoEndButtons||(v=g(i,this.labelLastPage,p.w,this.lastText,this.lastClass,i,"pagination-goto-last")),h.push(v);var D=e("ul",{staticClass:"pagination",class:["b-pagination",this.btnSize,this.alignment,this.styleClass],attrs:{role:s?null:"menubar","aria-disabled":n?"true":"false","aria-label":s?null:r||null},on:s?{}:{keydown:this.handleKeyNav},ref:"ul"},h);return s?e("nav",{attrs:{"aria-disabled":n?"true":null,"aria-hidden":n?"true":"false","aria-label":s&&r||null}},[D]):D}});function W(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function A(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?W(Object(n),!0).forEach((function(t){z(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):W(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var N=function(e){return Object(c.c)(Object(m.b)(e)||20,1)},I=function(e){return Object(c.c)(Object(m.b)(e)||0,0)},U=Object(f.d)(Object(h.m)(A(A({},R),{},{ariaControls:Object(f.c)(d.t),perPage:Object(f.c)(d.o,20),totalRows:Object(f.c)(d.o,0)})),i.eb),J=s.a.extend({name:i.eb,mixins:[F],props:U,computed:{numberOfPages:function(){var e=Object(c.a)(I(this.totalRows)/N(this.perPage));return e<1?1:e},pageSizeNumberOfPages:function(){return{perPage:N(this.perPage),totalRows:I(this.totalRows),numberOfPages:this.numberOfPages}}},watch:{pageSizeNumberOfPages:function(e,t){Object(_.p)(t)||(e.perPage!==t.perPage&&e.totalRows===t.totalRows||e.numberOfPages!==t.numberOfPages&&this.currentPage>e.numberOfPages)&&(this.currentPage=1),this.localNumberOfPages=e.numberOfPages}},created:function(){var e=this;this.localNumberOfPages=this.numberOfPages;var t=Object(m.b)(this[H],0);t>0?this.currentPage=t:this.$nextTick((function(){e.currentPage=0}))},methods:{onClick:function(e,t){var n=this;if(t!==this.currentPage){var a=e.target,r=new u.a(o.C,{cancelable:!0,vueTarget:this,target:a});this.$emit(r.type,r,t),r.defaultPrevented||(this.currentPage=t,this.$emit(o.d,this.currentPage),this.$nextTick((function(){Object(l.u)(a)&&n.$el.contains(a)?Object(l.d)(a):n.focusCurrent()})))}},makePage:function(e){return e},linkProps:function(){return{}}}})},JvlW:function(e,t,n){!function(e){"use strict";var t={ss:"sekundė_sekundžių_sekundes",m:"minutė_minutės_minutę",mm:"minutės_minučių_minutes",h:"valanda_valandos_valandą",hh:"valandos_valandų_valandas",d:"diena_dienos_dieną",dd:"dienos_dienų_dienas",M:"mėnuo_mėnesio_mėnesį",MM:"mėnesiai_mėnesių_mėnesius",y:"metai_metų_metus",yy:"metai_metų_metus"};function n(e,t,n,a){return t?r(n)[0]:a?r(n)[1]:r(n)[2]}function a(e){return e%10==0||e>10&&e<20}function r(e){return t[e].split("_")}function s(e,t,s,i){var o=e+" ";return 1===e?o+n(0,t,s[0],i):t?o+(a(e)?r(s)[1]:r(s)[0]):i?o+r(s)[1]:o+(a(e)?r(s)[1]:r(s)[2])}e.defineLocale("lt",{months:{format:"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio".split("_"),standalone:"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis".split("_"),isFormat:/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?|MMMM?(\[[^\[\]]*\]|\s)+D[oD]?/},monthsShort:"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd".split("_"),weekdays:{format:"sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį".split("_"),standalone:"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis".split("_"),isFormat:/dddd HH:mm/},weekdaysShort:"Sek_Pir_Ant_Tre_Ket_Pen_Šeš".split("_"),weekdaysMin:"S_P_A_T_K_Pn_Š".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY [m.] MMMM D [d.]",LLL:"YYYY [m.] MMMM D [d.], HH:mm [val.]",LLLL:"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]",l:"YYYY-MM-DD",ll:"YYYY [m.] MMMM D [d.]",lll:"YYYY [m.] MMMM D [d.], HH:mm [val.]",llll:"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]"},calendar:{sameDay:"[Šiandien] LT",nextDay:"[Rytoj] LT",nextWeek:"dddd LT",lastDay:"[Vakar] LT",lastWeek:"[Praėjusį] dddd LT",sameElse:"L"},relativeTime:{future:"po %s",past:"prieš %s",s:function(e,t,n,a){return t?"kelios sekundės":a?"kelių sekundžių":"kelias sekundes"},ss:s,m:n,mm:s,h:n,hh:s,d:n,dd:s,M:n,MM:s,y:n,yy:s},dayOfMonthOrdinalParse:/\d{1,2}-oji/,ordinal:function(e){return e+"-oji"},week:{dow:1,doy:4}})}(n("wd/R"))},"K/tc":function(e,t,n){!function(e){"use strict";e.defineLocale("af",{months:"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mrt_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des".split("_"),weekdays:"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag".split("_"),weekdaysShort:"Son_Maa_Din_Woe_Don_Vry_Sat".split("_"),weekdaysMin:"So_Ma_Di_Wo_Do_Vr_Sa".split("_"),meridiemParse:/vm|nm/i,isPM:function(e){return/^nm$/i.test(e)},meridiem:function(e,t,n){return e<12?n?"vm":"VM":n?"nm":"NM"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Vandag om] LT",nextDay:"[Môre om] LT",nextWeek:"dddd [om] LT",lastDay:"[Gister om] LT",lastWeek:"[Laas] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oor %s",past:"%s gelede",s:"'n paar sekondes",ss:"%d sekondes",m:"'n minuut",mm:"%d minute",h:"'n uur",hh:"%d ure",d:"'n dag",dd:"%d dae",M:"'n maand",MM:"%d maande",y:"'n jaar",yy:"%d jaar"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(n("wd/R"))},KSF8:function(e,t,n){!function(e){"use strict";e.defineLocale("vi",{months:"tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12".split("_"),monthsShort:"Thg 01_Thg 02_Thg 03_Thg 04_Thg 05_Thg 06_Thg 07_Thg 08_Thg 09_Thg 10_Thg 11_Thg 12".split("_"),monthsParseExact:!0,weekdays:"chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy".split("_"),weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysParseExact:!0,meridiemParse:/sa|ch/i,isPM:function(e){return/^ch$/i.test(e)},meridiem:function(e,t,n){return e<12?n?"sa":"SA":n?"ch":"CH"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [năm] YYYY",LLL:"D MMMM [năm] YYYY HH:mm",LLLL:"dddd, D MMMM [năm] YYYY HH:mm",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[Hôm nay lúc] LT",nextDay:"[Ngày mai lúc] LT",nextWeek:"dddd [tuần tới lúc] LT",lastDay:"[Hôm qua lúc] LT",lastWeek:"dddd [tuần trước lúc] LT",sameElse:"L"},relativeTime:{future:"%s tới",past:"%s trước",s:"vài giây",ss:"%d giây",m:"một phút",mm:"%d phút",h:"một giờ",hh:"%d giờ",d:"một ngày",dd:"%d ngày",w:"một tuần",ww:"%d tuần",M:"một tháng",MM:"%d tháng",y:"một năm",yy:"%d năm"},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:function(e){return e},week:{dow:1,doy:4}})}(n("wd/R"))},KTz0:function(e,t,n){!function(e){"use strict";var t={words:{ss:["sekund","sekunda","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mjesec","mjeseca","mjeseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(e,t){return 1===e?t[0]:e>=2&&e<=4?t[1]:t[2]},translate:function(e,n,a){var r=t.words[a];return 1===a.length?n?r[0]:r[1]:e+" "+t.correctGrammaticalCase(e,r)}};e.defineLocale("me",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sjutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedjelje] [u] LT","[prošlog] [ponedjeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srijede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"nekoliko sekundi",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:"dan",dd:t.translate,M:"mjesec",MM:t.translate,y:"godinu",yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(n("wd/R"))},Loxo:function(e,t,n){!function(e){"use strict";e.defineLocale("uz",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба".split("_"),weekdaysShort:"Якш_Душ_Сеш_Чор_Пай_Жум_Шан".split("_"),weekdaysMin:"Як_Ду_Се_Чо_Па_Жу_Ша".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Бугун соат] LT [да]",nextDay:"[Эртага] LT [да]",nextWeek:"dddd [куни соат] LT [да]",lastDay:"[Кеча соат] LT [да]",lastWeek:"[Утган] dddd [куни соат] LT [да]",sameElse:"L"},relativeTime:{future:"Якин %s ичида",past:"Бир неча %s олдин",s:"фурсат",ss:"%d фурсат",m:"бир дакика",mm:"%d дакика",h:"бир соат",hh:"%d соат",d:"бир кун",dd:"%d кун",M:"бир ой",MM:"%d ой",y:"бир йил",yy:"%d йил"},week:{dow:1,doy:7}})}(n("wd/R"))},OIYi:function(e,t,n){!function(e){"use strict";e.defineLocale("en-ca",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"YYYY-MM-DD",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}})}(n("wd/R"))},Oaa7:function(e,t,n){!function(e){"use strict";e.defineLocale("en-gb",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(n("wd/R"))},Ob0Z:function(e,t,n){!function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},n={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};function a(e,t,n,a){var r="";if(t)switch(n){case"s":r="काही सेकंद";break;case"ss":r="%d सेकंद";break;case"m":r="एक मिनिट";break;case"mm":r="%d मिनिटे";break;case"h":r="एक तास";break;case"hh":r="%d तास";break;case"d":r="एक दिवस";break;case"dd":r="%d दिवस";break;case"M":r="एक महिना";break;case"MM":r="%d महिने";break;case"y":r="एक वर्ष";break;case"yy":r="%d वर्षे"}else switch(n){case"s":r="काही सेकंदां";break;case"ss":r="%d सेकंदां";break;case"m":r="एका मिनिटा";break;case"mm":r="%d मिनिटां";break;case"h":r="एका तासा";break;case"hh":r="%d तासां";break;case"d":r="एका दिवसा";break;case"dd":r="%d दिवसां";break;case"M":r="एका महिन्या";break;case"MM":r="%d महिन्यां";break;case"y":r="एका वर्षा";break;case"yy":r="%d वर्षां"}return r.replace(/%d/i,e)}e.defineLocale("mr",{months:"जानेवारी_फेब्रुवारी_मार्च_एप्रिल_मे_जून_जुलै_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),monthsShort:"जाने._फेब्रु._मार्च._एप्रि._मे._जून._जुलै._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:!0,weekdays:"रविवार_सोमवार_मंगळवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगळ_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm वाजता",LTS:"A h:mm:ss वाजता",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm वाजता",LLLL:"dddd, D MMMM YYYY, A h:mm वाजता"},calendar:{sameDay:"[आज] LT",nextDay:"[उद्या] LT",nextWeek:"dddd, LT",lastDay:"[काल] LT",lastWeek:"[मागील] dddd, LT",sameElse:"L"},relativeTime:{future:"%sमध्ये",past:"%sपूर्वी",s:a,ss:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/पहाटे|सकाळी|दुपारी|सायंकाळी|रात्री/,meridiemHour:function(e,t){return 12===e&&(e=0),"पहाटे"===t||"सकाळी"===t?e:"दुपारी"===t||"सायंकाळी"===t||"रात्री"===t?e>=12?e:e+12:void 0},meridiem:function(e,t,n){return e>=0&&e<6?"पहाटे":e<12?"सकाळी":e<17?"दुपारी":e<20?"सायंकाळी":"रात्री"},week:{dow:0,doy:6}})}(n("wd/R"))},Obrr:function(e,t,n){"use strict";n.d(t,"a",(function(){return x}));var a=n("xjcK"),r=n("6GPe"),s=n("AFYn"),i=n("m/oX"),o=n("mS7b"),d=n("Iyau"),u=n("kGy3"),l=n("a3f1"),_=n("ex6f"),c=n("PCFI"),m=n("2C+6"),h="__BV_toggle",f="".concat(h,"_HANDLER__"),M="".concat(h,"_CLICK__"),p="".concat(h,"_STATE__"),y="".concat(h,"_TARGETS__"),g=Object(l.d)(a.q,"toggle"),L=Object(l.e)(a.q,"state"),Y=Object(l.e)(a.q,"sync-state"),w=Object(l.d)(a.q,"request-state"),b=[i.c,i.j],k=function(e){return!Object(d.a)(["button","a"],e.tagName.toLowerCase())},v=function(e){var t=e[M];t&&(Object(l.a)(e,"click",t,s.T),Object(l.a)(e,"keydown",t,s.T)),e[M]=null},D=function(e,t){e[f]&&t.context&&t.context.$root.$off([L,Y],e[f]),e[f]=null},T=function(e,t){t?(Object(u.y)(e,"collapsed"),Object(u.b)(e,"not-collapsed"),Object(u.E)(e,"aria-expanded","true")):(Object(u.y)(e,"not-collapsed"),Object(u.b)(e,"collapsed"),Object(u.E)(e,"aria-expanded","false"))},S=function(e,t){e[t]=null,delete e[t]},j=function(e,t,n){if(r.f&&n.context){k(e)&&(Object(u.o)(e,"role")||Object(u.E)(e,"role","button"),Object(u.o)(e,"tabindex")||Object(u.E)(e,"tabindex","0")),T(e,e[p]);var a=function(e,t){var n=e.modifiers,a=e.arg,r=e.value,s=Object(m.h)(n||{});if(r=Object(_.n)(r)?r.split(o.s):r,Object(u.t)(t.tagName,"a")){var i=Object(u.h)(t,"href")||"";o.i.test(i)&&s.push(i.replace(o.h,""))}return Object(d.b)(a,r).forEach((function(e){return Object(_.n)(e)&&s.push(e)})),s.filter((function(e,t,n){return e&&n.indexOf(e)===t}))}(t,e);a.length>0?(Object(u.E)(e,"aria-controls",a.join(" ")),Object(u.F)(e,"overflow-anchor","none")):(Object(u.x)(e,"aria-controls"),Object(u.A)(e,"overflow-anchor")),Object(u.B)((function(){!function(e,t){if(v(e),t.context){var n=function(n){"keydown"===n.type&&!Object(d.a)(b,n.keyCode)||Object(u.r)(e)||(e[y]||[]).forEach((function(e){t.context.$root.$emit(g,e)}))};e[M]=n,Object(l.b)(e,"click",n,s.T),k(e)&&Object(l.b)(e,"keydown",n,s.T)}}(e,n)})),Object(c.a)(a,e[y])||(e[y]=a,a.forEach((function(e){n.context.$root.$emit(w,e)})))}},x={bind:function(e,t,n){e[p]=!1,e[y]=[],function(e,t){if(D(e,t),t.context){var n=function(t,n){Object(d.a)(e[y]||[],t)&&(e[p]=n,T(e,n))};e[f]=n,t.context.$root.$on([L,Y],n)}}(e,n),j(e,t,n)},componentUpdated:j,updated:j,unbind:function(e,t,n){v(e),D(e,n),S(e,f),S(e,M),S(e,p),S(e,y),Object(u.y)(e,"collapsed"),Object(u.y)(e,"not-collapsed"),Object(u.x)(e,"aria-expanded"),Object(u.x)(e,"aria-controls"),Object(u.x)(e,"role"),Object(u.A)(e,"overflow-anchor")}}},OjkT:function(e,t,n){!function(e){"use strict";var t={1:"१",2:"२",3:"३",4:"४",5:"५",6:"६",7:"७",8:"८",9:"९",0:"०"},n={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};e.defineLocale("ne",{months:"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मई_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर".split("_"),monthsShort:"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.".split("_"),monthsParseExact:!0,weekdays:"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार".split("_"),weekdaysShort:"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.".split("_"),weekdaysMin:"आ._सो._मं._बु._बि._शु._श.".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"Aको h:mm बजे",LTS:"Aको h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, Aको h:mm बजे",LLLL:"dddd, D MMMM YYYY, Aको h:mm बजे"},preparse:function(e){return e.replace(/[१२३४५६७८९०]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/राति|बिहान|दिउँसो|साँझ/,meridiemHour:function(e,t){return 12===e&&(e=0),"राति"===t?e<4?e:e+12:"बिहान"===t?e:"दिउँसो"===t?e>=10?e:e+12:"साँझ"===t?e+12:void 0},meridiem:function(e,t,n){return e<3?"राति":e<12?"बिहान":e<16?"दिउँसो":e<20?"साँझ":"राति"},calendar:{sameDay:"[आज] LT",nextDay:"[भोलि] LT",nextWeek:"[आउँदो] dddd[,] LT",lastDay:"[हिजो] LT",lastWeek:"[गएको] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%sमा",past:"%s अगाडि",s:"केही क्षण",ss:"%d सेकेण्ड",m:"एक मिनेट",mm:"%d मिनेट",h:"एक घण्टा",hh:"%d घण्टा",d:"एक दिन",dd:"%d दिन",M:"एक महिना",MM:"%d महिना",y:"एक बर्ष",yy:"%d बर्ष"},week:{dow:0,doy:6}})}(n("wd/R"))},OmwH:function(e,t,n){!function(e){"use strict";e.defineLocale("zh-mo",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"D/M/YYYY",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){var a=100*e+t;return a<600?"凌晨":a<900?"早上":a<1130?"上午":a<1230?"中午":a<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})}(n("wd/R"))},Oxv6:function(e,t,n){!function(e){"use strict";var t={0:"-ум",1:"-ум",2:"-юм",3:"-юм",4:"-ум",5:"-ум",6:"-ум",7:"-ум",8:"-ум",9:"-ум",10:"-ум",12:"-ум",13:"-ум",20:"-ум",30:"-юм",40:"-ум",50:"-ум",60:"-ум",70:"-ум",80:"-ум",90:"-ум",100:"-ум"};e.defineLocale("tg",{months:{format:"январи_феврали_марти_апрели_майи_июни_июли_августи_сентябри_октябри_ноябри_декабри".split("_"),standalone:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_")},monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"якшанбе_душанбе_сешанбе_чоршанбе_панҷшанбе_ҷумъа_шанбе".split("_"),weekdaysShort:"яшб_дшб_сшб_чшб_пшб_ҷум_шнб".split("_"),weekdaysMin:"яш_дш_сш_чш_пш_ҷм_шб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Имрӯз соати] LT",nextDay:"[Фардо соати] LT",lastDay:"[Дирӯз соати] LT",nextWeek:"dddd[и] [ҳафтаи оянда соати] LT",lastWeek:"dddd[и] [ҳафтаи гузашта соати] LT",sameElse:"L"},relativeTime:{future:"баъди %s",past:"%s пеш",s:"якчанд сония",m:"як дақиқа",mm:"%d дақиқа",h:"як соат",hh:"%d соат",d:"як рӯз",dd:"%d рӯз",M:"як моҳ",MM:"%d моҳ",y:"як сол",yy:"%d сол"},meridiemParse:/шаб|субҳ|рӯз|бегоҳ/,meridiemHour:function(e,t){return 12===e&&(e=0),"шаб"===t?e<4?e:e+12:"субҳ"===t?e:"рӯз"===t?e>=11?e:e+12:"бегоҳ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"шаб":e<11?"субҳ":e<16?"рӯз":e<19?"бегоҳ":"шаб"},dayOfMonthOrdinalParse:/\d{1,2}-(ум|юм)/,ordinal:function(e){return e+(t[e]||t[e%10]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(n("wd/R"))},PA2r:function(e,t,n){!function(e){"use strict";var t={format:"leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_"),standalone:"ledna_února_března_dubna_května_června_července_srpna_září_října_listopadu_prosince".split("_")},n="led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_"),a=[/^led/i,/^úno/i,/^bře/i,/^dub/i,/^kvě/i,/^(čvn|červen$|června)/i,/^(čvc|červenec|července)/i,/^srp/i,/^zář/i,/^říj/i,/^lis/i,/^pro/i],r=/^(leden|únor|březen|duben|květen|červenec|července|červen|června|srpen|září|říjen|listopad|prosinec|led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i;function s(e){return e>1&&e<5&&1!=~~(e/10)}function i(e,t,n,a){var r=e+" ";switch(n){case"s":return t||a?"pár sekund":"pár sekundami";case"ss":return t||a?r+(s(e)?"sekundy":"sekund"):r+"sekundami";case"m":return t?"minuta":a?"minutu":"minutou";case"mm":return t||a?r+(s(e)?"minuty":"minut"):r+"minutami";case"h":return t?"hodina":a?"hodinu":"hodinou";case"hh":return t||a?r+(s(e)?"hodiny":"hodin"):r+"hodinami";case"d":return t||a?"den":"dnem";case"dd":return t||a?r+(s(e)?"dny":"dní"):r+"dny";case"M":return t||a?"měsíc":"měsícem";case"MM":return t||a?r+(s(e)?"měsíce":"měsíců"):r+"měsíci";case"y":return t||a?"rok":"rokem";case"yy":return t||a?r+(s(e)?"roky":"let"):r+"lety"}}e.defineLocale("cs",{months:t,monthsShort:n,monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(leden|ledna|února|únor|březen|března|duben|dubna|květen|května|červenec|července|červen|června|srpen|srpna|září|říjen|října|listopadu|listopad|prosinec|prosince)/i,monthsShortStrictRegex:/^(led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"),weekdaysShort:"ne_po_út_st_čt_pá_so".split("_"),weekdaysMin:"ne_po_út_st_čt_pá_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm",l:"D. M. YYYY"},calendar:{sameDay:"[dnes v] LT",nextDay:"[zítra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v neděli v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve středu v] LT";case 4:return"[ve čtvrtek v] LT";case 5:return"[v pátek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[včera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou neděli v] LT";case 1:case 2:return"[minulé] dddd [v] LT";case 3:return"[minulou středu v] LT";case 4:case 5:return"[minulý] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"před %s",s:i,ss:i,m:i,mm:i,h:i,hh:i,d:i,dd:i,M:i,MM:i,y:i,yy:i},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},PeUW:function(e,t,n){!function(e){"use strict";var t={1:"௧",2:"௨",3:"௩",4:"௪",5:"௫",6:"௬",7:"௭",8:"௮",9:"௯",0:"௦"},n={"௧":"1","௨":"2","௩":"3","௪":"4","௫":"5","௬":"6","௭":"7","௮":"8","௯":"9","௦":"0"};e.defineLocale("ta",{months:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),monthsShort:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),weekdays:"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை".split("_"),weekdaysShort:"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி".split("_"),weekdaysMin:"ஞா_தி_செ_பு_வி_வெ_ச".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, HH:mm",LLLL:"dddd, D MMMM YYYY, HH:mm"},calendar:{sameDay:"[இன்று] LT",nextDay:"[நாளை] LT",nextWeek:"dddd, LT",lastDay:"[நேற்று] LT",lastWeek:"[கடந்த வாரம்] dddd, LT",sameElse:"L"},relativeTime:{future:"%s இல்",past:"%s முன்",s:"ஒரு சில விநாடிகள்",ss:"%d விநாடிகள்",m:"ஒரு நிமிடம்",mm:"%d நிமிடங்கள்",h:"ஒரு மணி நேரம்",hh:"%d மணி நேரம்",d:"ஒரு நாள்",dd:"%d நாட்கள்",M:"ஒரு மாதம்",MM:"%d மாதங்கள்",y:"ஒரு வருடம்",yy:"%d ஆண்டுகள்"},dayOfMonthOrdinalParse:/\d{1,2}வது/,ordinal:function(e){return e+"வது"},preparse:function(e){return e.replace(/[௧௨௩௪௫௬௭௮௯௦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,meridiem:function(e,t,n){return e<2?" யாமம்":e<6?" வைகறை":e<10?" காலை":e<14?" நண்பகல்":e<18?" எற்பாடு":e<22?" மாலை":" யாமம்"},meridiemHour:function(e,t){return 12===e&&(e=0),"யாமம்"===t?e<2?e:e+12:"வைகறை"===t||"காலை"===t||"நண்பகல்"===t&&e>=10?e:e+12},week:{dow:0,doy:6}})}(n("wd/R"))},PpIw:function(e,t,n){!function(e){"use strict";var t={1:"೧",2:"೨",3:"೩",4:"೪",5:"೫",6:"೬",7:"೭",8:"೮",9:"೯",0:"೦"},n={"೧":"1","೨":"2","೩":"3","೪":"4","೫":"5","೬":"6","೭":"7","೮":"8","೯":"9","೦":"0"};e.defineLocale("kn",{months:"ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್".split("_"),monthsShort:"ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ".split("_"),monthsParseExact:!0,weekdays:"ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ".split("_"),weekdaysShort:"ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ".split("_"),weekdaysMin:"ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[ಇಂದು] LT",nextDay:"[ನಾಳೆ] LT",nextWeek:"dddd, LT",lastDay:"[ನಿನ್ನೆ] LT",lastWeek:"[ಕೊನೆಯ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ನಂತರ",past:"%s ಹಿಂದೆ",s:"ಕೆಲವು ಕ್ಷಣಗಳು",ss:"%d ಸೆಕೆಂಡುಗಳು",m:"ಒಂದು ನಿಮಿಷ",mm:"%d ನಿಮಿಷ",h:"ಒಂದು ಗಂಟೆ",hh:"%d ಗಂಟೆ",d:"ಒಂದು ದಿನ",dd:"%d ದಿನ",M:"ಒಂದು ತಿಂಗಳು",MM:"%d ತಿಂಗಳು",y:"ಒಂದು ವರ್ಷ",yy:"%d ವರ್ಷ"},preparse:function(e){return e.replace(/[೧೨೩೪೫೬೭೮೯೦]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/ರಾತ್ರಿ|ಬೆಳಿಗ್ಗೆ|ಮಧ್ಯಾಹ್ನ|ಸಂಜೆ/,meridiemHour:function(e,t){return 12===e&&(e=0),"ರಾತ್ರಿ"===t?e<4?e:e+12:"ಬೆಳಿಗ್ಗೆ"===t?e:"ಮಧ್ಯಾಹ್ನ"===t?e>=10?e:e+12:"ಸಂಜೆ"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"ರಾತ್ರಿ":e<10?"ಬೆಳಿಗ್ಗೆ":e<17?"ಮಧ್ಯಾಹ್ನ":e<20?"ಸಂಜೆ":"ರಾತ್ರಿ"},dayOfMonthOrdinalParse:/\d{1,2}(ನೇ)/,ordinal:function(e){return e+"ನೇ"},week:{dow:0,doy:6}})}(n("wd/R"))},Qj4J:function(e,t,n){!function(e){"use strict";e.defineLocale("ar-kw",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:12}})}(n("wd/R"))},RAwQ:function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={m:["eng Minutt","enger Minutt"],h:["eng Stonn","enger Stonn"],d:["een Dag","engem Dag"],M:["ee Mount","engem Mount"],y:["ee Joer","engem Joer"]};return t?r[n][0]:r[n][1]}function n(e){if(e=parseInt(e,10),isNaN(e))return!1;if(e<0)return!0;if(e<10)return 4<=e&&e<=7;if(e<100){var t=e%10;return n(0===t?e/10:t)}if(e<1e4){for(;e>=10;)e/=10;return n(e)}return n(e/=1e3)}e.defineLocale("lb",{months:"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg".split("_"),weekdaysShort:"So._Mé._Dë._Më._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mé_Dë_Më_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm [Auer]",LTS:"H:mm:ss [Auer]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm [Auer]",LLLL:"dddd, D. MMMM YYYY H:mm [Auer]"},calendar:{sameDay:"[Haut um] LT",sameElse:"L",nextDay:"[Muer um] LT",nextWeek:"dddd [um] LT",lastDay:"[Gëschter um] LT",lastWeek:function(){switch(this.day()){case 2:case 4:return"[Leschten] dddd [um] LT";default:return"[Leschte] dddd [um] LT"}}},relativeTime:{future:function(e){return n(e.substr(0,e.indexOf(" ")))?"a "+e:"an "+e},past:function(e){return n(e.substr(0,e.indexOf(" ")))?"viru "+e:"virun "+e},s:"e puer Sekonnen",ss:"%d Sekonnen",m:t,mm:"%d Minutten",h:t,hh:"%d Stonnen",d:t,dd:"%d Deeg",M:t,MM:"%d Méint",y:t,yy:"%d Joer"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},RnhZ:function(e,t,n){var a={"./af":"K/tc","./af.js":"K/tc","./ar":"jnO4","./ar-dz":"o1bE","./ar-dz.js":"o1bE","./ar-kw":"Qj4J","./ar-kw.js":"Qj4J","./ar-ly":"HP3h","./ar-ly.js":"HP3h","./ar-ma":"CoRJ","./ar-ma.js":"CoRJ","./ar-sa":"gjCT","./ar-sa.js":"gjCT","./ar-tn":"bYM6","./ar-tn.js":"bYM6","./ar.js":"jnO4","./az":"SFxW","./az.js":"SFxW","./be":"H8ED","./be.js":"H8ED","./bg":"hKrs","./bg.js":"hKrs","./bm":"p/rL","./bm.js":"p/rL","./bn":"kEOa","./bn-bd":"loYQ","./bn-bd.js":"loYQ","./bn.js":"kEOa","./bo":"0mo+","./bo.js":"0mo+","./br":"aIdf","./br.js":"aIdf","./bs":"JVSJ","./bs.js":"JVSJ","./ca":"1xZ4","./ca.js":"1xZ4","./cs":"PA2r","./cs.js":"PA2r","./cv":"A+xa","./cv.js":"A+xa","./cy":"l5ep","./cy.js":"l5ep","./da":"DxQv","./da.js":"DxQv","./de":"tGlX","./de-at":"s+uk","./de-at.js":"s+uk","./de-ch":"u3GI","./de-ch.js":"u3GI","./de.js":"tGlX","./dv":"WYrj","./dv.js":"WYrj","./el":"jUeY","./el.js":"jUeY","./en-au":"Dmvi","./en-au.js":"Dmvi","./en-ca":"OIYi","./en-ca.js":"OIYi","./en-gb":"Oaa7","./en-gb.js":"Oaa7","./en-ie":"4dOw","./en-ie.js":"4dOw","./en-il":"czMo","./en-il.js":"czMo","./en-in":"7C5Q","./en-in.js":"7C5Q","./en-nz":"b1Dy","./en-nz.js":"b1Dy","./en-sg":"t+mt","./en-sg.js":"t+mt","./eo":"Zduo","./eo.js":"Zduo","./es":"iYuL","./es-do":"CjzT","./es-do.js":"CjzT","./es-mx":"tbfe","./es-mx.js":"tbfe","./es-us":"Vclq","./es-us.js":"Vclq","./es.js":"iYuL","./et":"7BjC","./et.js":"7BjC","./eu":"D/JM","./eu.js":"D/JM","./fa":"jfSC","./fa.js":"jfSC","./fi":"gekB","./fi.js":"gekB","./fil":"1ppg","./fil.js":"1ppg","./fo":"ByF4","./fo.js":"ByF4","./fr":"nyYc","./fr-ca":"2fjn","./fr-ca.js":"2fjn","./fr-ch":"Dkky","./fr-ch.js":"Dkky","./fr.js":"nyYc","./fy":"cRix","./fy.js":"cRix","./ga":"USCx","./ga.js":"USCx","./gd":"9rRi","./gd.js":"9rRi","./gl":"iEDd","./gl.js":"iEDd","./gom-deva":"qvJo","./gom-deva.js":"qvJo","./gom-latn":"DKr+","./gom-latn.js":"DKr+","./gu":"4MV3","./gu.js":"4MV3","./he":"x6pH","./he.js":"x6pH","./hi":"3E1r","./hi.js":"3E1r","./hr":"S6ln","./hr.js":"S6ln","./hu":"WxRl","./hu.js":"WxRl","./hy-am":"1rYy","./hy-am.js":"1rYy","./id":"UDhR","./id.js":"UDhR","./is":"BVg3","./is.js":"BVg3","./it":"bpih","./it-ch":"bxKX","./it-ch.js":"bxKX","./it.js":"bpih","./ja":"B55N","./ja.js":"B55N","./jv":"tUCv","./jv.js":"tUCv","./ka":"IBtZ","./ka.js":"IBtZ","./kk":"bXm7","./kk.js":"bXm7","./km":"6B0Y","./km.js":"6B0Y","./kn":"PpIw","./kn.js":"PpIw","./ko":"Ivi+","./ko.js":"Ivi+","./ku":"JCF/","./ku.js":"JCF/","./ky":"lgnt","./ky.js":"lgnt","./lb":"RAwQ","./lb.js":"RAwQ","./lo":"sp3z","./lo.js":"sp3z","./lt":"JvlW","./lt.js":"JvlW","./lv":"uXwI","./lv.js":"uXwI","./me":"KTz0","./me.js":"KTz0","./mi":"aIsn","./mi.js":"aIsn","./mk":"aQkU","./mk.js":"aQkU","./ml":"AvvY","./ml.js":"AvvY","./mn":"lYtQ","./mn.js":"lYtQ","./mr":"Ob0Z","./mr.js":"Ob0Z","./ms":"6+QB","./ms-my":"ZAMP","./ms-my.js":"ZAMP","./ms.js":"6+QB","./mt":"G0Uy","./mt.js":"G0Uy","./my":"honF","./my.js":"honF","./nb":"bOMt","./nb.js":"bOMt","./ne":"OjkT","./ne.js":"OjkT","./nl":"+s0g","./nl-be":"2ykv","./nl-be.js":"2ykv","./nl.js":"+s0g","./nn":"uEye","./nn.js":"uEye","./oc-lnc":"Fnuy","./oc-lnc.js":"Fnuy","./pa-in":"8/+R","./pa-in.js":"8/+R","./pl":"jVdC","./pl.js":"jVdC","./pt":"8mBD","./pt-br":"0tRk","./pt-br.js":"0tRk","./pt.js":"8mBD","./ro":"lyxo","./ro.js":"lyxo","./ru":"lXzo","./ru.js":"lXzo","./sd":"Z4QM","./sd.js":"Z4QM","./se":"//9w","./se.js":"//9w","./si":"7aV9","./si.js":"7aV9","./sk":"e+ae","./sk.js":"e+ae","./sl":"gVVK","./sl.js":"gVVK","./sq":"yPMs","./sq.js":"yPMs","./sr":"zx6S","./sr-cyrl":"E+lV","./sr-cyrl.js":"E+lV","./sr.js":"zx6S","./ss":"Ur1D","./ss.js":"Ur1D","./sv":"X709","./sv.js":"X709","./sw":"dNwA","./sw.js":"dNwA","./ta":"PeUW","./ta.js":"PeUW","./te":"XLvN","./te.js":"XLvN","./tet":"V2x9","./tet.js":"V2x9","./tg":"Oxv6","./tg.js":"Oxv6","./th":"EOgW","./th.js":"EOgW","./tk":"Wv91","./tk.js":"Wv91","./tl-ph":"Dzi0","./tl-ph.js":"Dzi0","./tlh":"z3Vd","./tlh.js":"z3Vd","./tr":"DoHr","./tr.js":"DoHr","./tzl":"z1FC","./tzl.js":"z1FC","./tzm":"wQk9","./tzm-latn":"tT3J","./tzm-latn.js":"tT3J","./tzm.js":"wQk9","./ug-cn":"YRex","./ug-cn.js":"YRex","./uk":"raLr","./uk.js":"raLr","./ur":"UpQW","./ur.js":"UpQW","./uz":"Loxo","./uz-latn":"AQ68","./uz-latn.js":"AQ68","./uz.js":"Loxo","./vi":"KSF8","./vi.js":"KSF8","./x-pseudo":"/X5v","./x-pseudo.js":"/X5v","./yo":"fzPg","./yo.js":"fzPg","./zh-cn":"XDpg","./zh-cn.js":"XDpg","./zh-hk":"SatO","./zh-hk.js":"SatO","./zh-mo":"OmwH","./zh-mo.js":"OmwH","./zh-tw":"kOpN","./zh-tw.js":"kOpN"};function r(e){var t=s(e);return n(t)}function s(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=s,e.exports=r,r.id="RnhZ"},S6ln:function(e,t,n){!function(e){"use strict";function t(e,t,n){var a=e+" ";switch(n){case"ss":return a+=1===e?"sekunda":2===e||3===e||4===e?"sekunde":"sekundi";case"m":return t?"jedna minuta":"jedne minute";case"mm":return a+=1===e?"minuta":2===e||3===e||4===e?"minute":"minuta";case"h":return t?"jedan sat":"jednog sata";case"hh":return a+=1===e?"sat":2===e||3===e||4===e?"sata":"sati";case"dd":return a+=1===e?"dan":"dana";case"MM":return a+=1===e?"mjesec":2===e||3===e||4===e?"mjeseca":"mjeseci";case"yy":return a+=1===e?"godina":2===e||3===e||4===e?"godine":"godina"}}e.defineLocale("hr",{months:{format:"siječnja_veljače_ožujka_travnja_svibnja_lipnja_srpnja_kolovoza_rujna_listopada_studenoga_prosinca".split("_"),standalone:"siječanj_veljača_ožujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split("_")},monthsShort:"sij._velj._ožu._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split("_"),monthsParseExact:!0,weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"Do MMMM YYYY",LLL:"Do MMMM YYYY H:mm",LLLL:"dddd, Do MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:return"[prošlu] [nedjelju] [u] LT";case 3:return"[prošlu] [srijedu] [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",ss:t,m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(n("wd/R"))},SFxW:function(e,t,n){!function(e){"use strict";var t={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-üncü",4:"-üncü",100:"-üncü",6:"-ncı",9:"-uncu",10:"-uncu",30:"-uncu",60:"-ıncı",90:"-ıncı"};e.defineLocale("az",{months:"yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr".split("_"),monthsShort:"yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek".split("_"),weekdays:"Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə".split("_"),weekdaysShort:"Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən".split("_"),weekdaysMin:"Bz_BE_ÇA_Çə_CA_Cü_Şə".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[sabah saat] LT",nextWeek:"[gələn həftə] dddd [saat] LT",lastDay:"[dünən] LT",lastWeek:"[keçən həftə] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s əvvəl",s:"bir neçə saniyə",ss:"%d saniyə",m:"bir dəqiqə",mm:"%d dəqiqə",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir il",yy:"%d il"},meridiemParse:/gecə|səhər|gündüz|axşam/,isPM:function(e){return/^(gündüz|axşam)$/.test(e)},meridiem:function(e,t,n){return e<4?"gecə":e<12?"səhər":e<17?"gündüz":"axşam"},dayOfMonthOrdinalParse:/\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,ordinal:function(e){if(0===e)return e+"-ıncı";var n=e%10;return e+(t[n]||t[e%100-n]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(n("wd/R"))},SatO:function(e,t,n){!function(e){"use strict";e.defineLocale("zh-hk",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){var a=100*e+t;return a<600?"凌晨":a<900?"早上":a<1200?"上午":1200===a?"中午":a<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})}(n("wd/R"))},UDhR:function(e,t,n){!function(e){"use strict";e.defineLocale("id",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|siang|sore|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"siang"===t?e>=11?e:e+12:"sore"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"siang":e<19?"sore":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",ss:"%d detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:0,doy:6}})}(n("wd/R"))},USCx:function(e,t,n){!function(e){"use strict";e.defineLocale("ga",{months:["Eanáir","Feabhra","Márta","Aibreán","Bealtaine","Meitheamh","Iúil","Lúnasa","Meán Fómhair","Deireadh Fómhair","Samhain","Nollaig"],monthsShort:["Ean","Feabh","Márt","Aib","Beal","Meith","Iúil","Lún","M.F.","D.F.","Samh","Noll"],monthsParseExact:!0,weekdays:["Dé Domhnaigh","Dé Luain","Dé Máirt","Dé Céadaoin","Déardaoin","Dé hAoine","Dé Sathairn"],weekdaysShort:["Domh","Luan","Máirt","Céad","Déar","Aoine","Sath"],weekdaysMin:["Do","Lu","Má","Cé","Dé","A","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Inniu ag] LT",nextDay:"[Amárach ag] LT",nextWeek:"dddd [ag] LT",lastDay:"[Inné ag] LT",lastWeek:"dddd [seo caite] [ag] LT",sameElse:"L"},relativeTime:{future:"i %s",past:"%s ó shin",s:"cúpla soicind",ss:"%d soicind",m:"nóiméad",mm:"%d nóiméad",h:"uair an chloig",hh:"%d uair an chloig",d:"lá",dd:"%d lá",M:"mí",MM:"%d míonna",y:"bliain",yy:"%d bliain"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(e){return e+(1===e?"d":e%10==2?"na":"mh")},week:{dow:1,doy:4}})}(n("wd/R"))},UpQW:function(e,t,n){!function(e){"use strict";var t=["جنوری","فروری","مارچ","اپریل","مئی","جون","جولائی","اگست","ستمبر","اکتوبر","نومبر","دسمبر"],n=["اتوار","پیر","منگل","بدھ","جمعرات","جمعہ","ہفتہ"];e.defineLocale("ur",{months:t,monthsShort:t,weekdays:n,weekdaysShort:n,weekdaysMin:n,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(e){return"شام"===e},meridiem:function(e,t,n){return e<12?"صبح":"شام"},calendar:{sameDay:"[آج بوقت] LT",nextDay:"[کل بوقت] LT",nextWeek:"dddd [بوقت] LT",lastDay:"[گذشتہ روز بوقت] LT",lastWeek:"[گذشتہ] dddd [بوقت] LT",sameElse:"L"},relativeTime:{future:"%s بعد",past:"%s قبل",s:"چند سیکنڈ",ss:"%d سیکنڈ",m:"ایک منٹ",mm:"%d منٹ",h:"ایک گھنٹہ",hh:"%d گھنٹے",d:"ایک دن",dd:"%d دن",M:"ایک ماہ",MM:"%d ماہ",y:"ایک سال",yy:"%d سال"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}})}(n("wd/R"))},Ur1D:function(e,t,n){!function(e){"use strict";e.defineLocale("ss",{months:"Bhimbidvwane_Indlovana_Indlov'lenkhulu_Mabasa_Inkhwekhweti_Inhlaba_Kholwane_Ingci_Inyoni_Imphala_Lweti_Ingongoni".split("_"),monthsShort:"Bhi_Ina_Inu_Mab_Ink_Inh_Kho_Igc_Iny_Imp_Lwe_Igo".split("_"),weekdays:"Lisontfo_Umsombuluko_Lesibili_Lesitsatfu_Lesine_Lesihlanu_Umgcibelo".split("_"),weekdaysShort:"Lis_Umb_Lsb_Les_Lsi_Lsh_Umg".split("_"),weekdaysMin:"Li_Us_Lb_Lt_Ls_Lh_Ug".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Namuhla nga] LT",nextDay:"[Kusasa nga] LT",nextWeek:"dddd [nga] LT",lastDay:"[Itolo nga] LT",lastWeek:"dddd [leliphelile] [nga] LT",sameElse:"L"},relativeTime:{future:"nga %s",past:"wenteka nga %s",s:"emizuzwana lomcane",ss:"%d mzuzwana",m:"umzuzu",mm:"%d emizuzu",h:"lihora",hh:"%d emahora",d:"lilanga",dd:"%d emalanga",M:"inyanga",MM:"%d tinyanga",y:"umnyaka",yy:"%d iminyaka"},meridiemParse:/ekuseni|emini|entsambama|ebusuku/,meridiem:function(e,t,n){return e<11?"ekuseni":e<15?"emini":e<19?"entsambama":"ebusuku"},meridiemHour:function(e,t){return 12===e&&(e=0),"ekuseni"===t?e:"emini"===t?e>=11?e:e+12:"entsambama"===t||"ebusuku"===t?0===e?0:e+12:void 0},dayOfMonthOrdinalParse:/\d{1,2}/,ordinal:"%d",week:{dow:1,doy:4}})}(n("wd/R"))},V2x9:function(e,t,n){!function(e){"use strict";e.defineLocale("tet",{months:"Janeiru_Fevereiru_Marsu_Abril_Maiu_Juñu_Jullu_Agustu_Setembru_Outubru_Novembru_Dezembru".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingu_Segunda_Tersa_Kuarta_Kinta_Sesta_Sabadu".split("_"),weekdaysShort:"Dom_Seg_Ters_Kua_Kint_Sest_Sab".split("_"),weekdaysMin:"Do_Seg_Te_Ku_Ki_Ses_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Ohin iha] LT",nextDay:"[Aban iha] LT",nextWeek:"dddd [iha] LT",lastDay:"[Horiseik iha] LT",lastWeek:"dddd [semana kotuk] [iha] LT",sameElse:"L"},relativeTime:{future:"iha %s",past:"%s liuba",s:"segundu balun",ss:"segundu %d",m:"minutu ida",mm:"minutu %d",h:"oras ida",hh:"oras %d",d:"loron ida",dd:"loron %d",M:"fulan ida",MM:"fulan %d",y:"tinan ida",yy:"tinan %d"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(n("wd/R"))},Vclq:function(e,t,n){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),n="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),a=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es-us",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"MM/DD/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY h:mm A",LLLL:"dddd, D [de] MMMM [de] YYYY h:mm A"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:0,doy:6}})}(n("wd/R"))},WVKn:function(e,t,n){"use strict";n.r(t);var a=n("HaE+"),r=(n("2B1R"),n("o0o1")),s=n.n(r),i=n("wd/R"),o=n.n(i),d=n("6Ytq"),u=n("giZP"),l=n("R5cT"),_=n("JtJI"),c=n("g2Gq"),m=n("3Zo4"),h=n("nqqA"),f=n("GUe+"),M=n("uqS5"),p=n("Obrr"),y=n("Ed67"),g=n("9hfn"),L=n("vDqi"),Y=n.n(L),w={components:{BBadge:d.a,BFormGroup:u.a,BFormInput:l.a,VueGoodTable:g.a,moment:o.a,BPagination:_.a,BFormSelect:c.a,BDropdown:m.a,BDropdownItem:h.a,BButton:f.a,BSidebar:M.a,VBToggle:p.a,BForm:y.a},directives:{"b-toggle":p.a},data:function(){return{rowSelection:{length:0},pageLength:15,dir:!1,columns:[{label:"#",field:"id",hidden:!0},{label:"الاسم",field:"name",sortable:!0},{label:"البريد الالكتروني",field:"email",sortable:!0},{label:"الدولة",field:"country",sortable:!0},{label:"رقم الهاتف",field:"phone",sortable:!1},{label:"تاريخ التسجيل",field:"date",sortable:!0},{label:"الاعدادات",field:"action",sortable:!1}],rows:[],searchTerm:""}},mounted:function(){var e=this;return Object(a.a)(s.a.mark((function t(){var n;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Y.a.post("/api/admin/users",{},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}});case 3:n=t.sent,e.rows=n.data,t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0),alert("حدث خطأ ما");case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},methods:{selectionChanged:function(e){this.rowSelection=e.selectedRows},deleteSelection:function(){var e,t,n=this.rowSelection.length;window.confirm("هل انتا متاكد من حذف ("+n+") من المستخدمين !");if(confirm){var a=(e=this.rowSelection,t="id",e.map((function(e){return e[t]})));Y.a.post("/api/admin/delete-users",{ids:a},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم حذف المستخدمين"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))}},blockAdmin:function(e){window.confirm("هل انت متأكد ؟")&&Y.a.post("/api/admin/block-user",{id:e},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم حظر المستخدم"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))},unblockAdmin:function(e){window.confirm("هل انت متأكد ؟")&&Y.a.post("/api/admin/unblock-user",{id:e},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم رفع حظر المستخدم"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))},deleteAdmin:function(e){window.confirm("هل انت متأكد ؟")&&Y.a.post("/api/admin/delete-user",{id:e},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(e){alert("تم حذف المستخدم"),location.reload()})).catch((function(e){alert("حدث خطأ ما")}))}}},b=n("KHd+"),k=Object(b.a)(w,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"custom-search d-flex justify-content-start"},[n("b-form-group",[n("div",{staticClass:"d-flex align-items-center"},[n("b-form-input",{staticClass:"d-inline-block",attrs:{placeholder:"بحث",type:"text"},model:{value:e.searchTerm,callback:function(t){e.searchTerm=t},expression:"searchTerm"}})],1)])],1),e._v(" "),n("vue-good-table",{attrs:{"select-options":{enabled:!0,selectionText:"صفوف محدده",clearSelectionText:"ازاله التحديد"},columns:e.columns,rows:e.rows,rtl:e.dir,"search-options":{enabled:!0,externalQuery:e.searchTerm},"pagination-options":{enabled:!0,perPage:e.pageLength}},on:{"on-selected-rows-change":e.selectionChanged},scopedSlots:e._u([{key:"table-row",fn:function(t){return["fullName"===t.column.field?n("span",{staticClass:"text-nowrap"},[n("span",{staticClass:"text-nowrap"},[e._v(e._s(t.row.fullName))])]):"action"===t.column.field?n("span",[n("b-dropdown",{attrs:{variant:"link","toggle-class":"text-decoration-none","no-caret":""},scopedSlots:e._u([{key:"button-content",fn:function(){return[n("feather-icon",{staticClass:"text-body",attrs:{icon:"MoreVerticalIcon",size:"16"}})]},proxy:!0}],null,!0)},[e._v(" "),0===t.row.ban?n("b-dropdown-item",{on:{click:function(n){return e.blockAdmin(t.row.id)}}},[n("feather-icon",{staticClass:"mr-50",attrs:{icon:"StopCircleIcon"}}),e._v(" "),n("span",[e._v("حظر")])],1):n("b-dropdown-item",{on:{click:function(n){return e.unblockAdmin(t.row.id)}}},[n("feather-icon",{staticClass:"mr-50",attrs:{icon:"StopCircleIcon"}}),e._v(" "),n("span",[e._v("رفع الحظر")])],1),e._v(" "),n("b-dropdown-item",{on:{click:function(n){return e.deleteAdmin(t.row.id)}}},[n("feather-icon",{staticClass:"mr-50",attrs:{icon:"TrashIcon"}}),e._v(" "),n("span",[e._v("حذف")])],1)],1)],1):n("span",[e._v("\n                "+e._s(t.formattedRow[t.column.field])+"\n            ")])]}},{key:"pagination-bottom",fn:function(t){return[n("div",{staticClass:"d-flex justify-content-between flex-wrap"},[n("div",{staticClass:"d-flex align-items-center mb-0 mt-1"},[n("span",{staticClass:"text-nowrap"},[e._v(" اظهار 1 الي ")]),e._v(" "),n("b-form-select",{staticClass:"mx-1",attrs:{options:["15","30","50","100"]},on:{input:function(e){return t.perPageChanged({currentPerPage:e})}},model:{value:e.pageLength,callback:function(t){e.pageLength=t},expression:"pageLength"}}),e._v(" "),n("span",{staticClass:"text-nowrap"},[e._v("\n                        من "+e._s(t.total)+" صف\n                    ")])],1),e._v(" "),n("div",[n("b-pagination",{staticClass:"mt-1 mb-0",attrs:{value:1,"total-rows":t.total,"per-page":e.pageLength,"first-number":"","last-number":"",align:"right"},on:{input:function(e){return t.pageChanged({currentPage:e})}},scopedSlots:e._u([{key:"prev-text",fn:function(){return[n("feather-icon",{attrs:{icon:"ChevronLeftIcon",size:"18"}})]},proxy:!0},{key:"next-text",fn:function(){return[n("feather-icon",{attrs:{icon:"ChevronRightIcon",size:"18"}})]},proxy:!0}],null,!0)})],1)])]}}])},[n("div",{attrs:{slot:"selected-row-actions"},slot:"selected-row-actions"},[n("div",{staticClass:"d-flex align-items-center"},[n("b-button",{staticClass:"p-auto",attrs:{pill:"",variant:"danger"},on:{click:function(t){return e.deleteSelection()}}},[n("feather-icon",{staticClass:"mr-50",attrs:{icon:"TrashIcon"}}),e._v(" "),n("span",{staticClass:"align-middle"})],1)],1)]),e._v(" "),n("div",{attrs:{slot:"emptystate"},slot:"emptystate"},[e._v("لا توجد بيانات")])])],1)}),[],!1,null,null,null);t.default=k.exports},WYrj:function(e,t,n){!function(e){"use strict";var t=["ޖެނުއަރީ","ފެބްރުއަރީ","މާރިޗު","އޭޕްރީލު","މޭ","ޖޫން","ޖުލައި","އޯގަސްޓު","ސެޕްޓެމްބަރު","އޮކްޓޯބަރު","ނޮވެމްބަރު","ޑިސެމްބަރު"],n=["އާދިއްތަ","ހޯމަ","އަންގާރަ","ބުދަ","ބުރާސްފަތި","ހުކުރު","ހޮނިހިރު"];e.defineLocale("dv",{months:t,monthsShort:t,weekdays:n,weekdaysShort:n,weekdaysMin:"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/M/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/މކ|މފ/,isPM:function(e){return"މފ"===e},meridiem:function(e,t,n){return e<12?"މކ":"މފ"},calendar:{sameDay:"[މިއަދު] LT",nextDay:"[މާދަމާ] LT",nextWeek:"dddd LT",lastDay:"[އިއްޔެ] LT",lastWeek:"[ފާއިތުވި] dddd LT",sameElse:"L"},relativeTime:{future:"ތެރޭގައި %s",past:"ކުރިން %s",s:"ސިކުންތުކޮޅެއް",ss:"d% ސިކުންތު",m:"މިނިޓެއް",mm:"މިނިޓު %d",h:"ގަޑިއިރެއް",hh:"ގަޑިއިރު %d",d:"ދުވަހެއް",dd:"ދުވަސް %d",M:"މަހެއް",MM:"މަސް %d",y:"އަހަރެއް",yy:"އަހަރު %d"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:7,doy:12}})}(n("wd/R"))},Wv91:function(e,t,n){!function(e){"use strict";var t={1:"'inji",5:"'inji",8:"'inji",70:"'inji",80:"'inji",2:"'nji",7:"'nji",20:"'nji",50:"'nji",3:"'ünji",4:"'ünji",100:"'ünji",6:"'njy",9:"'unjy",10:"'unjy",30:"'unjy",60:"'ynjy",90:"'ynjy"};e.defineLocale("tk",{months:"Ýanwar_Fewral_Mart_Aprel_Maý_Iýun_Iýul_Awgust_Sentýabr_Oktýabr_Noýabr_Dekabr".split("_"),monthsShort:"Ýan_Few_Mar_Apr_Maý_Iýn_Iýl_Awg_Sen_Okt_Noý_Dek".split("_"),weekdays:"Ýekşenbe_Duşenbe_Sişenbe_Çarşenbe_Penşenbe_Anna_Şenbe".split("_"),weekdaysShort:"Ýek_Duş_Siş_Çar_Pen_Ann_Şen".split("_"),weekdaysMin:"Ýk_Dş_Sş_Çr_Pn_An_Şn".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün sagat] LT",nextDay:"[ertir sagat] LT",nextWeek:"[indiki] dddd [sagat] LT",lastDay:"[düýn] LT",lastWeek:"[geçen] dddd [sagat] LT",sameElse:"L"},relativeTime:{future:"%s soň",past:"%s öň",s:"birnäçe sekunt",m:"bir minut",mm:"%d minut",h:"bir sagat",hh:"%d sagat",d:"bir gün",dd:"%d gün",M:"bir aý",MM:"%d aý",y:"bir ýyl",yy:"%d ýyl"},ordinal:function(e,n){switch(n){case"d":case"D":case"Do":case"DD":return e;default:if(0===e)return e+"'unjy";var a=e%10;return e+(t[a]||t[e%100-a]||t[e>=100?100:null])}},week:{dow:1,doy:7}})}(n("wd/R"))},WxRl:function(e,t,n){!function(e){"use strict";var t="vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton".split(" ");function n(e,t,n,a){var r=e;switch(n){case"s":return a||t?"néhány másodperc":"néhány másodperce";case"ss":return r+(a||t)?" másodperc":" másodperce";case"m":return"egy"+(a||t?" perc":" perce");case"mm":return r+(a||t?" perc":" perce");case"h":return"egy"+(a||t?" óra":" órája");case"hh":return r+(a||t?" óra":" órája");case"d":return"egy"+(a||t?" nap":" napja");case"dd":return r+(a||t?" nap":" napja");case"M":return"egy"+(a||t?" hónap":" hónapja");case"MM":return r+(a||t?" hónap":" hónapja");case"y":return"egy"+(a||t?" év":" éve");case"yy":return r+(a||t?" év":" éve")}return""}function a(e){return(e?"":"[múlt] ")+"["+t[this.day()]+"] LT[-kor]"}e.defineLocale("hu",{months:"január_február_március_április_május_június_július_augusztus_szeptember_október_november_december".split("_"),monthsShort:"jan._feb._márc._ápr._máj._jún._júl._aug._szept._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat".split("_"),weekdaysShort:"vas_hét_kedd_sze_csüt_pén_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"},meridiemParse:/de|du/i,isPM:function(e){return"u"===e.charAt(1).toLowerCase()},meridiem:function(e,t,n){return e<12?!0===n?"de":"DE":!0===n?"du":"DU"},calendar:{sameDay:"[ma] LT[-kor]",nextDay:"[holnap] LT[-kor]",nextWeek:function(){return a.call(this,!0)},lastDay:"[tegnap] LT[-kor]",lastWeek:function(){return a.call(this,!1)},sameElse:"L"},relativeTime:{future:"%s múlva",past:"%s",s:n,ss:n,m:n,mm:n,h:n,hh:n,d:n,dd:n,M:n,MM:n,y:n,yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},X709:function(e,t,n){!function(e){"use strict";e.defineLocale("sv",{months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag".split("_"),weekdaysShort:"sön_mån_tis_ons_tor_fre_lör".split("_"),weekdaysMin:"sö_må_ti_on_to_fr_lö".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [kl.] HH:mm",LLLL:"dddd D MMMM YYYY [kl.] HH:mm",lll:"D MMM YYYY HH:mm",llll:"ddd D MMM YYYY HH:mm"},calendar:{sameDay:"[Idag] LT",nextDay:"[Imorgon] LT",lastDay:"[Igår] LT",nextWeek:"[På] dddd LT",lastWeek:"[I] dddd[s] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"för %s sedan",s:"några sekunder",ss:"%d sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en månad",MM:"%d månader",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}(\:e|\:a)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?":e":1===t||2===t?":a":":e")},week:{dow:1,doy:4}})}(n("wd/R"))},XDpg:function(e,t,n){!function(e){"use strict";e.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"下午"===t||"晚上"===t?e+12:e>=11?e:e+12},meridiem:function(e,t,n){var a=100*e+t;return a<600?"凌晨":a<900?"早上":a<1130?"上午":a<1230?"中午":a<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(e){return e.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(e){return this.week()!==e.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"周";default:return e}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}})}(n("wd/R"))},XLvN:function(e,t,n){!function(e){"use strict";e.defineLocale("te",{months:"జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జులై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్".split("_"),monthsShort:"జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జులై_ఆగ._సెప్._అక్టో._నవ._డిసె.".split("_"),monthsParseExact:!0,weekdays:"ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం".split("_"),weekdaysShort:"ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని".split("_"),weekdaysMin:"ఆ_సో_మం_బు_గు_శు_శ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[నేడు] LT",nextDay:"[రేపు] LT",nextWeek:"dddd, LT",lastDay:"[నిన్న] LT",lastWeek:"[గత] dddd, LT",sameElse:"L"},relativeTime:{future:"%s లో",past:"%s క్రితం",s:"కొన్ని క్షణాలు",ss:"%d సెకన్లు",m:"ఒక నిమిషం",mm:"%d నిమిషాలు",h:"ఒక గంట",hh:"%d గంటలు",d:"ఒక రోజు",dd:"%d రోజులు",M:"ఒక నెల",MM:"%d నెలలు",y:"ఒక సంవత్సరం",yy:"%d సంవత్సరాలు"},dayOfMonthOrdinalParse:/\d{1,2}వ/,ordinal:"%dవ",meridiemParse:/రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,meridiemHour:function(e,t){return 12===e&&(e=0),"రాత్రి"===t?e<4?e:e+12:"ఉదయం"===t?e:"మధ్యాహ్నం"===t?e>=10?e:e+12:"సాయంత్రం"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"రాత్రి":e<10?"ఉదయం":e<17?"మధ్యాహ్నం":e<20?"సాయంత్రం":"రాత్రి"},week:{dow:0,doy:6}})}(n("wd/R"))},YRex:function(e,t,n){!function(e){"use strict";e.defineLocale("ug-cn",{months:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),monthsShort:"يانۋار_فېۋرال_مارت_ئاپرېل_ماي_ئىيۇن_ئىيۇل_ئاۋغۇست_سېنتەبىر_ئۆكتەبىر_نويابىر_دېكابىر".split("_"),weekdays:"يەكشەنبە_دۈشەنبە_سەيشەنبە_چارشەنبە_پەيشەنبە_جۈمە_شەنبە".split("_"),weekdaysShort:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),weekdaysMin:"يە_دۈ_سە_چا_پە_جۈ_شە".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY-يىلىM-ئاينىڭD-كۈنى",LLL:"YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm",LLLL:"dddd، YYYY-يىلىM-ئاينىڭD-كۈنى، HH:mm"},meridiemParse:/يېرىم كېچە|سەھەر|چۈشتىن بۇرۇن|چۈش|چۈشتىن كېيىن|كەچ/,meridiemHour:function(e,t){return 12===e&&(e=0),"يېرىم كېچە"===t||"سەھەر"===t||"چۈشتىن بۇرۇن"===t?e:"چۈشتىن كېيىن"===t||"كەچ"===t?e+12:e>=11?e:e+12},meridiem:function(e,t,n){var a=100*e+t;return a<600?"يېرىم كېچە":a<900?"سەھەر":a<1130?"چۈشتىن بۇرۇن":a<1230?"چۈش":a<1800?"چۈشتىن كېيىن":"كەچ"},calendar:{sameDay:"[بۈگۈن سائەت] LT",nextDay:"[ئەتە سائەت] LT",nextWeek:"[كېلەركى] dddd [سائەت] LT",lastDay:"[تۆنۈگۈن] LT",lastWeek:"[ئالدىنقى] dddd [سائەت] LT",sameElse:"L"},relativeTime:{future:"%s كېيىن",past:"%s بۇرۇن",s:"نەچچە سېكونت",ss:"%d سېكونت",m:"بىر مىنۇت",mm:"%d مىنۇت",h:"بىر سائەت",hh:"%d سائەت",d:"بىر كۈن",dd:"%d كۈن",M:"بىر ئاي",MM:"%d ئاي",y:"بىر يىل",yy:"%d يىل"},dayOfMonthOrdinalParse:/\d{1,2}(-كۈنى|-ئاي|-ھەپتە)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"-كۈنى";case"w":case"W":return e+"-ھەپتە";default:return e}},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:7}})}(n("wd/R"))},YuTi:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},Z4QM:function(e,t,n){!function(e){"use strict";var t=["جنوري","فيبروري","مارچ","اپريل","مئي","جون","جولاءِ","آگسٽ","سيپٽمبر","آڪٽوبر","نومبر","ڊسمبر"],n=["آچر","سومر","اڱارو","اربع","خميس","جمع","ڇنڇر"];e.defineLocale("sd",{months:t,monthsShort:t,weekdays:n,weekdaysShort:n,weekdaysMin:n,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd، D MMMM YYYY HH:mm"},meridiemParse:/صبح|شام/,isPM:function(e){return"شام"===e},meridiem:function(e,t,n){return e<12?"صبح":"شام"},calendar:{sameDay:"[اڄ] LT",nextDay:"[سڀاڻي] LT",nextWeek:"dddd [اڳين هفتي تي] LT",lastDay:"[ڪالهه] LT",lastWeek:"[گزريل هفتي] dddd [تي] LT",sameElse:"L"},relativeTime:{future:"%s پوء",past:"%s اڳ",s:"چند سيڪنڊ",ss:"%d سيڪنڊ",m:"هڪ منٽ",mm:"%d منٽ",h:"هڪ ڪلاڪ",hh:"%d ڪلاڪ",d:"هڪ ڏينهن",dd:"%d ڏينهن",M:"هڪ مهينو",MM:"%d مهينا",y:"هڪ سال",yy:"%d سال"},preparse:function(e){return e.replace(/،/g,",")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:1,doy:4}})}(n("wd/R"))},ZAMP:function(e,t,n){!function(e){"use strict";e.defineLocale("ms-my",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(e,t){return 12===e&&(e=0),"pagi"===t?e:"tengahari"===t?e>=11?e:e+12:"petang"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"pagi":e<15?"tengahari":e<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",ss:"%d saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}})}(n("wd/R"))},Zduo:function(e,t,n){!function(e){"use strict";e.defineLocale("eo",{months:"januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro".split("_"),monthsShort:"jan_feb_mart_apr_maj_jun_jul_aŭg_sept_okt_nov_dec".split("_"),weekdays:"dimanĉo_lundo_mardo_merkredo_ĵaŭdo_vendredo_sabato".split("_"),weekdaysShort:"dim_lun_mard_merk_ĵaŭ_ven_sab".split("_"),weekdaysMin:"di_lu_ma_me_ĵa_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"[la] D[-an de] MMMM, YYYY",LLL:"[la] D[-an de] MMMM, YYYY HH:mm",LLLL:"dddd[n], [la] D[-an de] MMMM, YYYY HH:mm",llll:"ddd, [la] D[-an de] MMM, YYYY HH:mm"},meridiemParse:/[ap]\.t\.m/i,isPM:function(e){return"p"===e.charAt(0).toLowerCase()},meridiem:function(e,t,n){return e>11?n?"p.t.m.":"P.T.M.":n?"a.t.m.":"A.T.M."},calendar:{sameDay:"[Hodiaŭ je] LT",nextDay:"[Morgaŭ je] LT",nextWeek:"dddd[n je] LT",lastDay:"[Hieraŭ je] LT",lastWeek:"[pasintan] dddd[n je] LT",sameElse:"L"},relativeTime:{future:"post %s",past:"antaŭ %s",s:"kelkaj sekundoj",ss:"%d sekundoj",m:"unu minuto",mm:"%d minutoj",h:"unu horo",hh:"%d horoj",d:"unu tago",dd:"%d tagoj",M:"unu monato",MM:"%d monatoj",y:"unu jaro",yy:"%d jaroj"},dayOfMonthOrdinalParse:/\d{1,2}a/,ordinal:"%da",week:{dow:1,doy:7}})}(n("wd/R"))},aIdf:function(e,t,n){!function(e){"use strict";function t(e,t,n){return e+" "+function(e,t){return 2===t?function(e){var t={m:"v",b:"v",d:"z"};return void 0===t[e.charAt(0)]?e:t[e.charAt(0)]+e.substring(1)}(e):e}({mm:"munutenn",MM:"miz",dd:"devezh"}[n],e)}var n=[/^gen/i,/^c[ʼ\']hwe/i,/^meu/i,/^ebr/i,/^mae/i,/^(mez|eve)/i,/^gou/i,/^eos/i,/^gwe/i,/^her/i,/^du/i,/^ker/i],a=/^(genver|c[ʼ\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu|gen|c[ʼ\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,r=[/^Su/i,/^Lu/i,/^Me([^r]|$)/i,/^Mer/i,/^Ya/i,/^Gw/i,/^Sa/i];e.defineLocale("br",{months:"Genver_Cʼhwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu".split("_"),monthsShort:"Gen_Cʼhwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker".split("_"),weekdays:"Sul_Lun_Meurzh_Mercʼher_Yaou_Gwener_Sadorn".split("_"),weekdaysShort:"Sul_Lun_Meu_Mer_Yao_Gwe_Sad".split("_"),weekdaysMin:"Su_Lu_Me_Mer_Ya_Gw_Sa".split("_"),weekdaysParse:r,fullWeekdaysParse:[/^sul/i,/^lun/i,/^meurzh/i,/^merc[ʼ\']her/i,/^yaou/i,/^gwener/i,/^sadorn/i],shortWeekdaysParse:[/^Sul/i,/^Lun/i,/^Meu/i,/^Mer/i,/^Yao/i,/^Gwe/i,/^Sad/i],minWeekdaysParse:r,monthsRegex:a,monthsShortRegex:a,monthsStrictRegex:/^(genver|c[ʼ\']hwevrer|meurzh|ebrel|mae|mezheven|gouere|eost|gwengolo|here|du|kerzu)/i,monthsShortStrictRegex:/^(gen|c[ʼ\']hwe|meu|ebr|mae|eve|gou|eos|gwe|her|du|ker)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [a viz] MMMM YYYY",LLL:"D [a viz] MMMM YYYY HH:mm",LLLL:"dddd, D [a viz] MMMM YYYY HH:mm"},calendar:{sameDay:"[Hiziv da] LT",nextDay:"[Warcʼhoazh da] LT",nextWeek:"dddd [da] LT",lastDay:"[Decʼh da] LT",lastWeek:"dddd [paset da] LT",sameElse:"L"},relativeTime:{future:"a-benn %s",past:"%s ʼzo",s:"un nebeud segondennoù",ss:"%d eilenn",m:"ur vunutenn",mm:t,h:"un eur",hh:"%d eur",d:"un devezh",dd:t,M:"ur miz",MM:t,y:"ur bloaz",yy:function(e){switch(function e(t){return t>9?e(t%10):t}(e)){case 1:case 3:case 4:case 5:case 9:return e+" bloaz";default:return e+" vloaz"}}},dayOfMonthOrdinalParse:/\d{1,2}(añ|vet)/,ordinal:function(e){return e+(1===e?"añ":"vet")},week:{dow:1,doy:4},meridiemParse:/a.m.|g.m./,isPM:function(e){return"g.m."===e},meridiem:function(e,t,n){return e<12?"a.m.":"g.m."}})}(n("wd/R"))},aIsn:function(e,t,n){!function(e){"use strict";e.defineLocale("mi",{months:"Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_Pipiri_Hōngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea".split("_"),monthsShort:"Kohi_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki".split("_"),monthsRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,3}/i,monthsShortStrictRegex:/(?:['a-z\u0101\u014D\u016B]+\-?){1,2}/i,weekdays:"Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei".split("_"),weekdaysShort:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),weekdaysMin:"Ta_Ma_Tū_We_Tāi_Pa_Hā".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [i] HH:mm",LLLL:"dddd, D MMMM YYYY [i] HH:mm"},calendar:{sameDay:"[i teie mahana, i] LT",nextDay:"[apopo i] LT",nextWeek:"dddd [i] LT",lastDay:"[inanahi i] LT",lastWeek:"dddd [whakamutunga i] LT",sameElse:"L"},relativeTime:{future:"i roto i %s",past:"%s i mua",s:"te hēkona ruarua",ss:"%d hēkona",m:"he meneti",mm:"%d meneti",h:"te haora",hh:"%d haora",d:"he ra",dd:"%d ra",M:"he marama",MM:"%d marama",y:"he tau",yy:"%d tau"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(n("wd/R"))},aQkU:function(e,t,n){!function(e){"use strict";e.defineLocale("mk",{months:"јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември".split("_"),monthsShort:"јан_фев_мар_апр_мај_јун_јул_авг_сеп_окт_ное_дек".split("_"),weekdays:"недела_понеделник_вторник_среда_четврток_петок_сабота".split("_"),weekdaysShort:"нед_пон_вто_сре_чет_пет_саб".split("_"),weekdaysMin:"нe_пo_вт_ср_че_пе_сa".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Денес во] LT",nextDay:"[Утре во] LT",nextWeek:"[Во] dddd [во] LT",lastDay:"[Вчера во] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Изминатата] dddd [во] LT";case 1:case 2:case 4:case 5:return"[Изминатиот] dddd [во] LT"}},sameElse:"L"},relativeTime:{future:"за %s",past:"пред %s",s:"неколку секунди",ss:"%d секунди",m:"една минута",mm:"%d минути",h:"еден час",hh:"%d часа",d:"еден ден",dd:"%d дена",M:"еден месец",MM:"%d месеци",y:"една година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(e){var t=e%10,n=e%100;return 0===e?e+"-ев":0===n?e+"-ен":n>10&&n<20?e+"-ти":1===t?e+"-ви":2===t?e+"-ри":7===t||8===t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}})}(n("wd/R"))},b1Dy:function(e,t,n){!function(e){"use strict";e.defineLocale("en-nz",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(n("wd/R"))},bOMt:function(e,t,n){!function(e){"use strict";e.defineLocale("nb",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:!0,weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"sø._ma._ti._on._to._fr._lø.".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] HH:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[forrige] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"noen sekunder",ss:"%d sekunder",m:"ett minutt",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dager",w:"en uke",ww:"%d uker",M:"en måned",MM:"%d måneder",y:"ett år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},bXm7:function(e,t,n){!function(e){"use strict";var t={0:"-ші",1:"-ші",2:"-ші",3:"-ші",4:"-ші",5:"-ші",6:"-шы",7:"-ші",8:"-ші",9:"-шы",10:"-шы",20:"-шы",30:"-шы",40:"-шы",50:"-ші",60:"-шы",70:"-ші",80:"-ші",90:"-шы",100:"-ші"};e.defineLocale("kk",{months:"қаңтар_ақпан_наурыз_сәуір_мамыр_маусым_шілде_тамыз_қыркүйек_қазан_қараша_желтоқсан".split("_"),monthsShort:"қаң_ақп_нау_сәу_мам_мау_шіл_там_қыр_қаз_қар_жел".split("_"),weekdays:"жексенбі_дүйсенбі_сейсенбі_сәрсенбі_бейсенбі_жұма_сенбі".split("_"),weekdaysShort:"жек_дүй_сей_сәр_бей_жұм_сен".split("_"),weekdaysMin:"жк_дй_сй_ср_бй_жм_сн".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгін сағат] LT",nextDay:"[Ертең сағат] LT",nextWeek:"dddd [сағат] LT",lastDay:"[Кеше сағат] LT",lastWeek:"[Өткен аптаның] dddd [сағат] LT",sameElse:"L"},relativeTime:{future:"%s ішінде",past:"%s бұрын",s:"бірнеше секунд",ss:"%d секунд",m:"бір минут",mm:"%d минут",h:"бір сағат",hh:"%d сағат",d:"бір күн",dd:"%d күн",M:"бір ай",MM:"%d ай",y:"бір жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(ші|шы)/,ordinal:function(e){return e+(t[e]||t[e%10]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(n("wd/R"))},bYM6:function(e,t,n){!function(e){"use strict";e.defineLocale("ar-tn",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}})}(n("wd/R"))},bpih:function(e,t,n){!function(e){"use strict";e.defineLocale("it",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:function(){return"[Oggi a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextDay:function(){return"[Domani a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},nextWeek:function(){return"dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastDay:function(){return"[Ieri a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"},lastWeek:function(){switch(this.day()){case 0:return"[La scorsa] dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT";default:return"[Lo scorso] dddd [a"+(this.hours()>1?"lle ":0===this.hours()?" ":"ll'")+"]LT"}},sameElse:"L"},relativeTime:{future:"tra %s",past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",w:"una settimana",ww:"%d settimane",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(n("wd/R"))},bxKX:function(e,t,n){!function(e){"use strict";e.defineLocale("it-ch",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"domenica_lunedì_martedì_mercoledì_giovedì_venerdì_sabato".split("_"),weekdaysShort:"dom_lun_mar_mer_gio_ven_sab".split("_"),weekdaysMin:"do_lu_ma_me_gi_ve_sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){switch(this.day()){case 0:return"[la scorsa] dddd [alle] LT";default:return"[lo scorso] dddd [alle] LT"}},sameElse:"L"},relativeTime:{future:function(e){return(/^[0-9].+$/.test(e)?"tra":"in")+" "+e},past:"%s fa",s:"alcuni secondi",ss:"%d secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(n("wd/R"))},cRix:function(e,t,n){!function(e){"use strict";var t="jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.".split("_"),n="jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_");e.defineLocale("fy",{months:"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsParseExact:!0,weekdays:"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon".split("_"),weekdaysShort:"si._mo._ti._wo._to._fr._so.".split("_"),weekdaysMin:"Si_Mo_Ti_Wo_To_Fr_So".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[hjoed om] LT",nextDay:"[moarn om] LT",nextWeek:"dddd [om] LT",lastDay:"[juster om] LT",lastWeek:"[ôfrûne] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oer %s",past:"%s lyn",s:"in pear sekonden",ss:"%d sekonden",m:"ien minút",mm:"%d minuten",h:"ien oere",hh:"%d oeren",d:"ien dei",dd:"%d dagen",M:"ien moanne",MM:"%d moannen",y:"ien jier",yy:"%d jierren"},dayOfMonthOrdinalParse:/\d{1,2}(ste|de)/,ordinal:function(e){return e+(1===e||8===e||e>=20?"ste":"de")},week:{dow:1,doy:4}})}(n("wd/R"))},czMo:function(e,t,n){!function(e){"use strict";e.defineLocale("en-il",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}})}(n("wd/R"))},dNwA:function(e,t,n){!function(e){"use strict";e.defineLocale("sw",{months:"Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des".split("_"),weekdays:"Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi".split("_"),weekdaysShort:"Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos".split("_"),weekdaysMin:"J2_J3_J4_J5_Al_Ij_J1".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"hh:mm A",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[leo saa] LT",nextDay:"[kesho saa] LT",nextWeek:"[wiki ijayo] dddd [saat] LT",lastDay:"[jana] LT",lastWeek:"[wiki iliyopita] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s baadaye",past:"tokea %s",s:"hivi punde",ss:"sekunde %d",m:"dakika moja",mm:"dakika %d",h:"saa limoja",hh:"masaa %d",d:"siku moja",dd:"siku %d",M:"mwezi mmoja",MM:"miezi %d",y:"mwaka mmoja",yy:"miaka %d"},week:{dow:1,doy:7}})}(n("wd/R"))},"e+ae":function(e,t,n){!function(e){"use strict";var t="január_február_marec_apríl_máj_jún_júl_august_september_október_november_december".split("_"),n="jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec".split("_");function a(e){return e>1&&e<5}function r(e,t,n,r){var s=e+" ";switch(n){case"s":return t||r?"pár sekúnd":"pár sekundami";case"ss":return t||r?s+(a(e)?"sekundy":"sekúnd"):s+"sekundami";case"m":return t?"minúta":r?"minútu":"minútou";case"mm":return t||r?s+(a(e)?"minúty":"minút"):s+"minútami";case"h":return t?"hodina":r?"hodinu":"hodinou";case"hh":return t||r?s+(a(e)?"hodiny":"hodín"):s+"hodinami";case"d":return t||r?"deň":"dňom";case"dd":return t||r?s+(a(e)?"dni":"dní"):s+"dňami";case"M":return t||r?"mesiac":"mesiacom";case"MM":return t||r?s+(a(e)?"mesiace":"mesiacov"):s+"mesiacmi";case"y":return t||r?"rok":"rokom";case"yy":return t||r?s+(a(e)?"roky":"rokov"):s+"rokmi"}}e.defineLocale("sk",{months:t,monthsShort:n,weekdays:"nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota".split("_"),weekdaysShort:"ne_po_ut_st_št_pi_so".split("_"),weekdaysMin:"ne_po_ut_st_št_pi_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm"},calendar:{sameDay:"[dnes o] LT",nextDay:"[zajtra o] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedeľu o] LT";case 1:case 2:return"[v] dddd [o] LT";case 3:return"[v stredu o] LT";case 4:return"[vo štvrtok o] LT";case 5:return"[v piatok o] LT";case 6:return"[v sobotu o] LT"}},lastDay:"[včera o] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulú nedeľu o] LT";case 1:case 2:return"[minulý] dddd [o] LT";case 3:return"[minulú stredu o] LT";case 4:case 5:return"[minulý] dddd [o] LT";case 6:return"[minulú sobotu o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pred %s",s:r,ss:r,m:r,mm:r,h:r,hh:r,d:r,dd:r,M:r,MM:r,y:r,yy:r},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},fzPg:function(e,t,n){!function(e){"use strict";e.defineLocale("yo",{months:"Sẹ́rẹ́_Èrèlè_Ẹrẹ̀nà_Ìgbé_Èbibi_Òkùdu_Agẹmo_Ògún_Owewe_Ọ̀wàrà_Bélú_Ọ̀pẹ̀̀".split("_"),monthsShort:"Sẹ́r_Èrl_Ẹrn_Ìgb_Èbi_Òkù_Agẹ_Ògú_Owe_Ọ̀wà_Bél_Ọ̀pẹ̀̀".split("_"),weekdays:"Àìkú_Ajé_Ìsẹ́gun_Ọjọ́rú_Ọjọ́bọ_Ẹtì_Àbámẹ́ta".split("_"),weekdaysShort:"Àìk_Ajé_Ìsẹ́_Ọjr_Ọjb_Ẹtì_Àbá".split("_"),weekdaysMin:"Àì_Aj_Ìs_Ọr_Ọb_Ẹt_Àb".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Ònì ni] LT",nextDay:"[Ọ̀la ni] LT",nextWeek:"dddd [Ọsẹ̀ tón'bọ] [ni] LT",lastDay:"[Àna ni] LT",lastWeek:"dddd [Ọsẹ̀ tólọ́] [ni] LT",sameElse:"L"},relativeTime:{future:"ní %s",past:"%s kọjá",s:"ìsẹjú aayá die",ss:"aayá %d",m:"ìsẹjú kan",mm:"ìsẹjú %d",h:"wákati kan",hh:"wákati %d",d:"ọjọ́ kan",dd:"ọjọ́ %d",M:"osù kan",MM:"osù %d",y:"ọdún kan",yy:"ọdún %d"},dayOfMonthOrdinalParse:/ọjọ́\s\d{1,2}/,ordinal:"ọjọ́ %d",week:{dow:1,doy:4}})}(n("wd/R"))},gVVK:function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r=e+" ";switch(n){case"s":return t||a?"nekaj sekund":"nekaj sekundami";case"ss":return r+=1===e?t?"sekundo":"sekundi":2===e?t||a?"sekundi":"sekundah":e<5?t||a?"sekunde":"sekundah":"sekund";case"m":return t?"ena minuta":"eno minuto";case"mm":return r+=1===e?t?"minuta":"minuto":2===e?t||a?"minuti":"minutama":e<5?t||a?"minute":"minutami":t||a?"minut":"minutami";case"h":return t?"ena ura":"eno uro";case"hh":return r+=1===e?t?"ura":"uro":2===e?t||a?"uri":"urama":e<5?t||a?"ure":"urami":t||a?"ur":"urami";case"d":return t||a?"en dan":"enim dnem";case"dd":return r+=1===e?t||a?"dan":"dnem":2===e?t||a?"dni":"dnevoma":t||a?"dni":"dnevi";case"M":return t||a?"en mesec":"enim mesecem";case"MM":return r+=1===e?t||a?"mesec":"mesecem":2===e?t||a?"meseca":"mesecema":e<5?t||a?"mesece":"meseci":t||a?"mesecev":"meseci";case"y":return t||a?"eno leto":"enim letom";case"yy":return r+=1===e?t||a?"leto":"letom":2===e?t||a?"leti":"letoma":e<5?t||a?"leta":"leti":t||a?"let":"leti"}}e.defineLocale("sl",{months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),weekdaysShort:"ned._pon._tor._sre._čet._pet._sob.".split("_"),weekdaysMin:"ne_po_to_sr_če_pe_so".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danes ob] LT",nextDay:"[jutri ob] LT",nextWeek:function(){switch(this.day()){case 0:return"[v] [nedeljo] [ob] LT";case 3:return"[v] [sredo] [ob] LT";case 6:return"[v] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[v] dddd [ob] LT"}},lastDay:"[včeraj ob] LT",lastWeek:function(){switch(this.day()){case 0:return"[prejšnjo] [nedeljo] [ob] LT";case 3:return"[prejšnjo] [sredo] [ob] LT";case 6:return"[prejšnjo] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[prejšnji] dddd [ob] LT"}},sameElse:"L"},relativeTime:{future:"čez %s",past:"pred %s",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(n("wd/R"))},gekB:function(e,t,n){!function(e){"use strict";var t="nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän".split(" "),n=["nolla","yhden","kahden","kolmen","neljän","viiden","kuuden",t[7],t[8],t[9]];function a(e,a,r,s){var i="";switch(r){case"s":return s?"muutaman sekunnin":"muutama sekunti";case"ss":i=s?"sekunnin":"sekuntia";break;case"m":return s?"minuutin":"minuutti";case"mm":i=s?"minuutin":"minuuttia";break;case"h":return s?"tunnin":"tunti";case"hh":i=s?"tunnin":"tuntia";break;case"d":return s?"päivän":"päivä";case"dd":i=s?"päivän":"päivää";break;case"M":return s?"kuukauden":"kuukausi";case"MM":i=s?"kuukauden":"kuukautta";break;case"y":return s?"vuoden":"vuosi";case"yy":i=s?"vuoden":"vuotta"}return i=function(e,a){return e<10?a?n[e]:t[e]:e}(e,s)+" "+i}e.defineLocale("fi",{months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu".split("_"),weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"Do MMMM[ta] YYYY",LLL:"Do MMMM[ta] YYYY, [klo] HH.mm",LLLL:"dddd, Do MMMM[ta] YYYY, [klo] HH.mm",l:"D.M.YYYY",ll:"Do MMM YYYY",lll:"Do MMM YYYY, [klo] HH.mm",llll:"ddd, Do MMM YYYY, [klo] HH.mm"},calendar:{sameDay:"[tänään] [klo] LT",nextDay:"[huomenna] [klo] LT",nextWeek:"dddd [klo] LT",lastDay:"[eilen] [klo] LT",lastWeek:"[viime] dddd[na] [klo] LT",sameElse:"L"},relativeTime:{future:"%s päästä",past:"%s sitten",s:a,ss:a,m:a,mm:a,h:a,hh:a,d:a,dd:a,M:a,MM:a,y:a,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},gjCT:function(e,t,n){!function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},n={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"};e.defineLocale("ar-sa",{months:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",ss:"%d ثانية",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:0,doy:6}})}(n("wd/R"))},hKrs:function(e,t,n){!function(e){"use strict";e.defineLocale("bg",{months:"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември".split("_"),monthsShort:"яну_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек".split("_"),weekdays:"неделя_понеделник_вторник_сряда_четвъртък_петък_събота".split("_"),weekdaysShort:"нед_пон_вто_сря_чет_пет_съб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Днес в] LT",nextDay:"[Утре в] LT",nextWeek:"dddd [в] LT",lastDay:"[Вчера в] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Миналата] dddd [в] LT";case 1:case 2:case 4:case 5:return"[Миналия] dddd [в] LT"}},sameElse:"L"},relativeTime:{future:"след %s",past:"преди %s",s:"няколко секунди",ss:"%d секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дена",w:"седмица",ww:"%d седмици",M:"месец",MM:"%d месеца",y:"година",yy:"%d години"},dayOfMonthOrdinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(e){var t=e%10,n=e%100;return 0===e?e+"-ев":0===n?e+"-ен":n>10&&n<20?e+"-ти":1===t?e+"-ви":2===t?e+"-ри":7===t||8===t?e+"-ми":e+"-ти"},week:{dow:1,doy:7}})}(n("wd/R"))},honF:function(e,t,n){!function(e){"use strict";var t={1:"၁",2:"၂",3:"၃",4:"၄",5:"၅",6:"၆",7:"၇",8:"၈",9:"၉",0:"၀"},n={"၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","၀":"0"};e.defineLocale("my",{months:"ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ".split("_"),monthsShort:"ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ".split("_"),weekdays:"တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ".split("_"),weekdaysShort:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),weekdaysMin:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ယနေ.] LT [မှာ]",nextDay:"[မနက်ဖြန်] LT [မှာ]",nextWeek:"dddd LT [မှာ]",lastDay:"[မနေ.က] LT [မှာ]",lastWeek:"[ပြီးခဲ့သော] dddd LT [မှာ]",sameElse:"L"},relativeTime:{future:"လာမည့် %s မှာ",past:"လွန်ခဲ့သော %s က",s:"စက္ကန်.အနည်းငယ်",ss:"%d စက္ကန့်",m:"တစ်မိနစ်",mm:"%d မိနစ်",h:"တစ်နာရီ",hh:"%d နာရီ",d:"တစ်ရက်",dd:"%d ရက်",M:"တစ်လ",MM:"%d လ",y:"တစ်နှစ်",yy:"%d နှစ်"},preparse:function(e){return e.replace(/[၁၂၃၄၅၆၇၈၉၀]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},week:{dow:1,doy:4}})}(n("wd/R"))},iEDd:function(e,t,n){!function(e){"use strict";e.defineLocale("gl",{months:"xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro".split("_"),monthsShort:"xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"domingo_luns_martes_mércores_xoves_venres_sábado".split("_"),weekdaysShort:"dom._lun._mar._mér._xov._ven._sáb.".split("_"),weekdaysMin:"do_lu_ma_mé_xo_ve_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoxe "+(1!==this.hours()?"ás":"á")+"] LT"},nextDay:function(){return"[mañá "+(1!==this.hours()?"ás":"á")+"] LT"},nextWeek:function(){return"dddd ["+(1!==this.hours()?"ás":"a")+"] LT"},lastDay:function(){return"[onte "+(1!==this.hours()?"á":"a")+"] LT"},lastWeek:function(){return"[o] dddd [pasado "+(1!==this.hours()?"ás":"a")+"] LT"},sameElse:"L"},relativeTime:{future:function(e){return 0===e.indexOf("un")?"n"+e:"en "+e},past:"hai %s",s:"uns segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"unha hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un ano",yy:"%d anos"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}})}(n("wd/R"))},iYuL:function(e,t,n){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),n="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),a=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4},invalidDate:"Fecha inválida"})}(n("wd/R"))},jUeY:function(e,t,n){!function(e){"use strict";e.defineLocale("el",{monthsNominativeEl:"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος".split("_"),monthsGenitiveEl:"Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου".split("_"),months:function(e,t){return e?"string"==typeof t&&/D/.test(t.substring(0,t.indexOf("MMMM")))?this._monthsGenitiveEl[e.month()]:this._monthsNominativeEl[e.month()]:this._monthsNominativeEl},monthsShort:"Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ".split("_"),weekdays:"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο".split("_"),weekdaysShort:"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ".split("_"),weekdaysMin:"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα".split("_"),meridiem:function(e,t,n){return e>11?n?"μμ":"ΜΜ":n?"πμ":"ΠΜ"},isPM:function(e){return"μ"===(e+"").toLowerCase()[0]},meridiemParse:/[ΠΜ]\.?Μ?\.?/i,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendarEl:{sameDay:"[Σήμερα {}] LT",nextDay:"[Αύριο {}] LT",nextWeek:"dddd [{}] LT",lastDay:"[Χθες {}] LT",lastWeek:function(){switch(this.day()){case 6:return"[το προηγούμενο] dddd [{}] LT";default:return"[την προηγούμενη] dddd [{}] LT"}},sameElse:"L"},calendar:function(e,t){var n,a=this._calendarEl[e],r=t&&t.hours();return n=a,("undefined"!=typeof Function&&n instanceof Function||"[object Function]"===Object.prototype.toString.call(n))&&(a=a.apply(t)),a.replace("{}",r%12==1?"στη":"στις")},relativeTime:{future:"σε %s",past:"%s πριν",s:"λίγα δευτερόλεπτα",ss:"%d δευτερόλεπτα",m:"ένα λεπτό",mm:"%d λεπτά",h:"μία ώρα",hh:"%d ώρες",d:"μία μέρα",dd:"%d μέρες",M:"ένας μήνας",MM:"%d μήνες",y:"ένας χρόνος",yy:"%d χρόνια"},dayOfMonthOrdinalParse:/\d{1,2}η/,ordinal:"%dη",week:{dow:1,doy:4}})}(n("wd/R"))},jVdC:function(e,t,n){!function(e){"use strict";var t="styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień".split("_"),n="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia".split("_"),a=[/^sty/i,/^lut/i,/^mar/i,/^kwi/i,/^maj/i,/^cze/i,/^lip/i,/^sie/i,/^wrz/i,/^paź/i,/^lis/i,/^gru/i];function r(e){return e%10<5&&e%10>1&&~~(e/10)%10!=1}function s(e,t,n){var a=e+" ";switch(n){case"ss":return a+(r(e)?"sekundy":"sekund");case"m":return t?"minuta":"minutę";case"mm":return a+(r(e)?"minuty":"minut");case"h":return t?"godzina":"godzinę";case"hh":return a+(r(e)?"godziny":"godzin");case"ww":return a+(r(e)?"tygodnie":"tygodni");case"MM":return a+(r(e)?"miesiące":"miesięcy");case"yy":return a+(r(e)?"lata":"lat")}}e.defineLocale("pl",{months:function(e,a){return e?/D MMMM/.test(a)?n[e.month()]:t[e.month()]:t},monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru".split("_"),monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota".split("_"),weekdaysShort:"ndz_pon_wt_śr_czw_pt_sob".split("_"),weekdaysMin:"Nd_Pn_Wt_Śr_Cz_Pt_So".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Dziś o] LT",nextDay:"[Jutro o] LT",nextWeek:function(){switch(this.day()){case 0:return"[W niedzielę o] LT";case 2:return"[We wtorek o] LT";case 3:return"[W środę o] LT";case 6:return"[W sobotę o] LT";default:return"[W] dddd [o] LT"}},lastDay:"[Wczoraj o] LT",lastWeek:function(){switch(this.day()){case 0:return"[W zeszłą niedzielę o] LT";case 3:return"[W zeszłą środę o] LT";case 6:return"[W zeszłą sobotę o] LT";default:return"[W zeszły] dddd [o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",ss:s,m:s,mm:s,h:s,hh:s,d:"1 dzień",dd:"%d dni",w:"tydzień",ww:s,M:"miesiąc",MM:s,y:"rok",yy:s},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},jfSC:function(e,t,n){!function(e){"use strict";var t={1:"۱",2:"۲",3:"۳",4:"۴",5:"۵",6:"۶",7:"۷",8:"۸",9:"۹",0:"۰"},n={"۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","۰":"0"};e.defineLocale("fa",{months:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),monthsShort:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),weekdays:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysShort:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysMin:"ی_د_س_چ_پ_ج_ش".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/قبل از ظهر|بعد از ظهر/,isPM:function(e){return/بعد از ظهر/.test(e)},meridiem:function(e,t,n){return e<12?"قبل از ظهر":"بعد از ظهر"},calendar:{sameDay:"[امروز ساعت] LT",nextDay:"[فردا ساعت] LT",nextWeek:"dddd [ساعت] LT",lastDay:"[دیروز ساعت] LT",lastWeek:"dddd [پیش] [ساعت] LT",sameElse:"L"},relativeTime:{future:"در %s",past:"%s پیش",s:"چند ثانیه",ss:"%d ثانیه",m:"یک دقیقه",mm:"%d دقیقه",h:"یک ساعت",hh:"%d ساعت",d:"یک روز",dd:"%d روز",M:"یک ماه",MM:"%d ماه",y:"یک سال",yy:"%d سال"},preparse:function(e){return e.replace(/[۰-۹]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},dayOfMonthOrdinalParse:/\d{1,2}م/,ordinal:"%dم",week:{dow:6,doy:12}})}(n("wd/R"))},jnO4:function(e,t,n){!function(e){"use strict";var t={1:"١",2:"٢",3:"٣",4:"٤",5:"٥",6:"٦",7:"٧",8:"٨",9:"٩",0:"٠"},n={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},a=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},r={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},s=function(e){return function(t,n,s,i){var o=a(t),d=r[e][a(t)];return 2===o&&(d=d[n?0:1]),d.replace(/%d/i,t)}},i=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];e.defineLocale("ar",{months:i,monthsShort:i,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:s("s"),ss:s("s"),m:s("m"),mm:s("m"),h:s("h"),hh:s("h"),d:s("d"),dd:s("d"),M:s("M"),MM:s("M"),y:s("y"),yy:s("y")},preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return n[e]})).replace(/،/g,",")},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]})).replace(/,/g,"،")},week:{dow:6,doy:12}})}(n("wd/R"))},kEOa:function(e,t,n){!function(e){"use strict";var t={1:"১",2:"২",3:"৩",4:"৪",5:"৫",6:"৬",7:"৭",8:"৮",9:"৯",0:"০"},n={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"};e.defineLocale("bn",{months:"জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(e){return e.replace(/[১২৩৪৫৬৭৮৯০]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/রাত|সকাল|দুপুর|বিকাল|রাত/,meridiemHour:function(e,t){return 12===e&&(e=0),"রাত"===t&&e>=4||"দুপুর"===t&&e<5||"বিকাল"===t?e+12:e},meridiem:function(e,t,n){return e<4?"রাত":e<10?"সকাল":e<17?"দুপুর":e<20?"বিকাল":"রাত"},week:{dow:0,doy:6}})}(n("wd/R"))},kOpN:function(e,t,n){!function(e){"use strict";e.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日 HH:mm",LLLL:"YYYY年M月D日dddd HH:mm",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(e,t){return 12===e&&(e=0),"凌晨"===t||"早上"===t||"上午"===t?e:"中午"===t?e>=11?e:e+12:"下午"===t||"晚上"===t?e+12:void 0},meridiem:function(e,t,n){var a=100*e+t;return a<600?"凌晨":a<900?"早上":a<1130?"上午":a<1230?"中午":a<1800?"下午":"晚上"},calendar:{sameDay:"[今天] LT",nextDay:"[明天] LT",nextWeek:"[下]dddd LT",lastDay:"[昨天] LT",lastWeek:"[上]dddd LT",sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|週)/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+"日";case"M":return e+"月";case"w":case"W":return e+"週";default:return e}},relativeTime:{future:"%s後",past:"%s前",s:"幾秒",ss:"%d 秒",m:"1 分鐘",mm:"%d 分鐘",h:"1 小時",hh:"%d 小時",d:"1 天",dd:"%d 天",M:"1 個月",MM:"%d 個月",y:"1 年",yy:"%d 年"}})}(n("wd/R"))},l5ep:function(e,t,n){!function(e){"use strict";e.defineLocale("cy",{months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Heddiw am] LT",nextDay:"[Yfory am] LT",nextWeek:"dddd [am] LT",lastDay:"[Ddoe am] LT",lastWeek:"dddd [diwethaf am] LT",sameElse:"L"},relativeTime:{future:"mewn %s",past:"%s yn ôl",s:"ychydig eiliadau",ss:"%d eiliad",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"},dayOfMonthOrdinalParse:/\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,ordinal:function(e){var t="";return e>20?t=40===e||50===e||60===e||80===e||100===e?"fed":"ain":e>0&&(t=["","af","il","ydd","ydd","ed","ed","ed","fed","fed","fed","eg","fed","eg","eg","fed","eg","eg","fed","eg","fed"][e]),e+t},week:{dow:1,doy:4}})}(n("wd/R"))},lXzo:function(e,t,n){!function(e){"use strict";function t(e,t,n){var a,r;return"m"===n?t?"минута":"минуту":e+" "+(a=+e,r={ss:t?"секунда_секунды_секунд":"секунду_секунды_секунд",mm:t?"минута_минуты_минут":"минуту_минуты_минут",hh:"час_часа_часов",dd:"день_дня_дней",ww:"неделя_недели_недель",MM:"месяц_месяца_месяцев",yy:"год_года_лет"}[n].split("_"),a%10==1&&a%100!=11?r[0]:a%10>=2&&a%10<=4&&(a%100<10||a%100>=20)?r[1]:r[2])}var n=[/^янв/i,/^фев/i,/^мар/i,/^апр/i,/^ма[йя]/i,/^июн/i,/^июл/i,/^авг/i,/^сен/i,/^окт/i,/^ноя/i,/^дек/i];e.defineLocale("ru",{months:{format:"января_февраля_марта_апреля_мая_июня_июля_августа_сентября_октября_ноября_декабря".split("_"),standalone:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_")},monthsShort:{format:"янв._февр._мар._апр._мая_июня_июля_авг._сент._окт._нояб._дек.".split("_"),standalone:"янв._февр._март_апр._май_июнь_июль_авг._сент._окт._нояб._дек.".split("_")},weekdays:{standalone:"воскресенье_понедельник_вторник_среда_четверг_пятница_суббота".split("_"),format:"воскресенье_понедельник_вторник_среду_четверг_пятницу_субботу".split("_"),isFormat:/\[ ?[Вв] ?(?:прошлую|следующую|эту)? ?] ?dddd/},weekdaysShort:"вс_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"вс_пн_вт_ср_чт_пт_сб".split("_"),monthsParse:n,longMonthsParse:n,shortMonthsParse:n,monthsRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsShortRegex:/^(январ[ья]|янв\.?|феврал[ья]|февр?\.?|марта?|мар\.?|апрел[ья]|апр\.?|ма[йя]|июн[ья]|июн\.?|июл[ья]|июл\.?|августа?|авг\.?|сентябр[ья]|сент?\.?|октябр[ья]|окт\.?|ноябр[ья]|нояб?\.?|декабр[ья]|дек\.?)/i,monthsStrictRegex:/^(январ[яь]|феврал[яь]|марта?|апрел[яь]|ма[яй]|июн[яь]|июл[яь]|августа?|сентябр[яь]|октябр[яь]|ноябр[яь]|декабр[яь])/i,monthsShortStrictRegex:/^(янв\.|февр?\.|мар[т.]|апр\.|ма[яй]|июн[ья.]|июл[ья.]|авг\.|сент?\.|окт\.|нояб?\.|дек\.)/i,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., H:mm",LLLL:"dddd, D MMMM YYYY г., H:mm"},calendar:{sameDay:"[Сегодня, в] LT",nextDay:"[Завтра, в] LT",lastDay:"[Вчера, в] LT",nextWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В следующее] dddd, [в] LT";case 1:case 2:case 4:return"[В следующий] dddd, [в] LT";case 3:case 5:case 6:return"[В следующую] dddd, [в] LT"}},lastWeek:function(e){if(e.week()===this.week())return 2===this.day()?"[Во] dddd, [в] LT":"[В] dddd, [в] LT";switch(this.day()){case 0:return"[В прошлое] dddd, [в] LT";case 1:case 2:case 4:return"[В прошлый] dddd, [в] LT";case 3:case 5:case 6:return"[В прошлую] dddd, [в] LT"}},sameElse:"L"},relativeTime:{future:"через %s",past:"%s назад",s:"несколько секунд",ss:t,m:t,mm:t,h:"час",hh:t,d:"день",dd:t,w:"неделя",ww:t,M:"месяц",MM:t,y:"год",yy:t},meridiemParse:/ночи|утра|дня|вечера/i,isPM:function(e){return/^(дня|вечера)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночи":e<12?"утра":e<17?"дня":"вечера"},dayOfMonthOrdinalParse:/\d{1,2}-(й|го|я)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":return e+"-й";case"D":return e+"-го";case"w":case"W":return e+"-я";default:return e}},week:{dow:1,doy:4}})}(n("wd/R"))},lYtQ:function(e,t,n){!function(e){"use strict";function t(e,t,n,a){switch(n){case"s":return t?"хэдхэн секунд":"хэдхэн секундын";case"ss":return e+(t?" секунд":" секундын");case"m":case"mm":return e+(t?" минут":" минутын");case"h":case"hh":return e+(t?" цаг":" цагийн");case"d":case"dd":return e+(t?" өдөр":" өдрийн");case"M":case"MM":return e+(t?" сар":" сарын");case"y":case"yy":return e+(t?" жил":" жилийн");default:return e}}e.defineLocale("mn",{months:"Нэгдүгээр сар_Хоёрдугаар сар_Гуравдугаар сар_Дөрөвдүгээр сар_Тавдугаар сар_Зургадугаар сар_Долдугаар сар_Наймдугаар сар_Есдүгээр сар_Аравдугаар сар_Арван нэгдүгээр сар_Арван хоёрдугаар сар".split("_"),monthsShort:"1 сар_2 сар_3 сар_4 сар_5 сар_6 сар_7 сар_8 сар_9 сар_10 сар_11 сар_12 сар".split("_"),monthsParseExact:!0,weekdays:"Ням_Даваа_Мягмар_Лхагва_Пүрэв_Баасан_Бямба".split("_"),weekdaysShort:"Ням_Дав_Мяг_Лха_Пүр_Баа_Бям".split("_"),weekdaysMin:"Ня_Да_Мя_Лх_Пү_Ба_Бя".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY оны MMMMын D",LLL:"YYYY оны MMMMын D HH:mm",LLLL:"dddd, YYYY оны MMMMын D HH:mm"},meridiemParse:/ҮӨ|ҮХ/i,isPM:function(e){return"ҮХ"===e},meridiem:function(e,t,n){return e<12?"ҮӨ":"ҮХ"},calendar:{sameDay:"[Өнөөдөр] LT",nextDay:"[Маргааш] LT",nextWeek:"[Ирэх] dddd LT",lastDay:"[Өчигдөр] LT",lastWeek:"[Өнгөрсөн] dddd LT",sameElse:"L"},relativeTime:{future:"%s дараа",past:"%s өмнө",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2} өдөр/,ordinal:function(e,t){switch(t){case"d":case"D":case"DDD":return e+" өдөр";default:return e}}})}(n("wd/R"))},lgnt:function(e,t,n){!function(e){"use strict";var t={0:"-чү",1:"-чи",2:"-чи",3:"-чү",4:"-чү",5:"-чи",6:"-чы",7:"-чи",8:"-чи",9:"-чу",10:"-чу",20:"-чы",30:"-чу",40:"-чы",50:"-чү",60:"-чы",70:"-чи",80:"-чи",90:"-чу",100:"-чү"};e.defineLocale("ky",{months:"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь".split("_"),monthsShort:"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек".split("_"),weekdays:"Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби".split("_"),weekdaysShort:"Жек_Дүй_Шей_Шар_Бей_Жум_Ише".split("_"),weekdaysMin:"Жк_Дй_Шй_Шр_Бй_Жм_Иш".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгүн саат] LT",nextDay:"[Эртең саат] LT",nextWeek:"dddd [саат] LT",lastDay:"[Кечээ саат] LT",lastWeek:"[Өткөн аптанын] dddd [күнү] [саат] LT",sameElse:"L"},relativeTime:{future:"%s ичинде",past:"%s мурун",s:"бирнече секунд",ss:"%d секунд",m:"бир мүнөт",mm:"%d мүнөт",h:"бир саат",hh:"%d саат",d:"бир күн",dd:"%d күн",M:"бир ай",MM:"%d ай",y:"бир жыл",yy:"%d жыл"},dayOfMonthOrdinalParse:/\d{1,2}-(чи|чы|чү|чу)/,ordinal:function(e){return e+(t[e]||t[e%10]||t[e>=100?100:null])},week:{dow:1,doy:7}})}(n("wd/R"))},loYQ:function(e,t,n){!function(e){"use strict";var t={1:"১",2:"২",3:"৩",4:"৪",5:"৫",6:"৬",7:"৭",8:"৮",9:"৯",0:"০"},n={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"};e.defineLocale("bn-bd",{months:"জানুয়ারি_ফেব্রুয়ারি_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব্রু_মার্চ_এপ্রিল_মে_জুন_জুলাই_আগস্ট_সেপ্ট_অক্টো_নভে_ডিসে".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পতিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পতি_শুক্র_শনি".split("_"),weekdaysMin:"রবি_সোম_মঙ্গল_বুধ_বৃহ_শুক্র_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",ss:"%d সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(e){return e.replace(/[১২৩৪৫৬৭৮৯০]/g,(function(e){return n[e]}))},postformat:function(e){return e.replace(/\d/g,(function(e){return t[e]}))},meridiemParse:/রাত|ভোর|সকাল|দুপুর|বিকাল|সন্ধ্যা|রাত/,meridiemHour:function(e,t){return 12===e&&(e=0),"রাত"===t?e<4?e:e+12:"ভোর"===t||"সকাল"===t?e:"দুপুর"===t?e>=3?e:e+12:"বিকাল"===t||"সন্ধ্যা"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"রাত":e<6?"ভোর":e<12?"সকাল":e<15?"দুপুর":e<18?"বিকাল":e<20?"সন্ধ্যা":"রাত"},week:{dow:0,doy:6}})}(n("wd/R"))},lyxo:function(e,t,n){!function(e){"use strict";function t(e,t,n){var a=" ";return(e%100>=20||e>=100&&e%100==0)&&(a=" de "),e+a+{ss:"secunde",mm:"minute",hh:"ore",dd:"zile",ww:"săptămâni",MM:"luni",yy:"ani"}[n]}e.defineLocale("ro",{months:"ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie".split("_"),monthsShort:"ian._feb._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"duminică_luni_marți_miercuri_joi_vineri_sâmbătă".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_Sâm".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_Sâ".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[azi la] LT",nextDay:"[mâine la] LT",nextWeek:"dddd [la] LT",lastDay:"[ieri la] LT",lastWeek:"[fosta] dddd [la] LT",sameElse:"L"},relativeTime:{future:"peste %s",past:"%s în urmă",s:"câteva secunde",ss:t,m:"un minut",mm:t,h:"o oră",hh:t,d:"o zi",dd:t,w:"o săptămână",ww:t,M:"o lună",MM:t,y:"un an",yy:t},week:{dow:1,doy:7}})}(n("wd/R"))},nyYc:function(e,t,n){!function(e){"use strict";var t=/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?|janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,n=[/^janv/i,/^févr/i,/^mars/i,/^avr/i,/^mai/i,/^juin/i,/^juil/i,/^août/i,/^sept/i,/^oct/i,/^nov/i,/^déc/i];e.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),monthsRegex:t,monthsShortRegex:t,monthsStrictRegex:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i,monthsShortStrictRegex:/(janv\.?|févr\.?|mars|avr\.?|mai|juin|juil\.?|août|sept\.?|oct\.?|nov\.?|déc\.?)/i,monthsParse:n,longMonthsParse:n,shortMonthsParse:n,weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd’hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",ss:"%d secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",w:"une semaine",ww:"%d semaines",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},dayOfMonthOrdinalParse:/\d{1,2}(er|)/,ordinal:function(e,t){switch(t){case"D":return e+(1===e?"er":"");default:case"M":case"Q":case"DDD":case"d":return e+(1===e?"er":"e");case"w":case"W":return e+(1===e?"re":"e")}},week:{dow:1,doy:4}})}(n("wd/R"))},o1bE:function(e,t,n){!function(e){"use strict";var t=function(e){return 0===e?0:1===e?1:2===e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5},n={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},a=function(e){return function(a,r,s,i){var o=t(a),d=n[e][t(a)];return 2===o&&(d=d[r?0:1]),d.replace(/%d/i,a)}},r=["جانفي","فيفري","مارس","أفريل","ماي","جوان","جويلية","أوت","سبتمبر","أكتوبر","نوفمبر","ديسمبر"];e.defineLocale("ar-dz",{months:r,monthsShort:r,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(e){return"م"===e},meridiem:function(e,t,n){return e<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:a("s"),ss:a("s"),m:a("m"),mm:a("m"),h:a("h"),hh:a("h"),d:a("d"),dd:a("d"),M:a("M"),MM:a("M"),y:a("y"),yy:a("y")},postformat:function(e){return e.replace(/,/g,"،")},week:{dow:0,doy:4}})}(n("wd/R"))},"p/rL":function(e,t,n){!function(e){"use strict";e.defineLocale("bm",{months:"Zanwuyekalo_Fewuruyekalo_Marisikalo_Awirilikalo_Mɛkalo_Zuwɛnkalo_Zuluyekalo_Utikalo_Sɛtanburukalo_ɔkutɔburukalo_Nowanburukalo_Desanburukalo".split("_"),monthsShort:"Zan_Few_Mar_Awi_Mɛ_Zuw_Zul_Uti_Sɛt_ɔku_Now_Des".split("_"),weekdays:"Kari_Ntɛnɛn_Tarata_Araba_Alamisa_Juma_Sibiri".split("_"),weekdaysShort:"Kar_Ntɛ_Tar_Ara_Ala_Jum_Sib".split("_"),weekdaysMin:"Ka_Nt_Ta_Ar_Al_Ju_Si".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"MMMM [tile] D [san] YYYY",LLL:"MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm",LLLL:"dddd MMMM [tile] D [san] YYYY [lɛrɛ] HH:mm"},calendar:{sameDay:"[Bi lɛrɛ] LT",nextDay:"[Sini lɛrɛ] LT",nextWeek:"dddd [don lɛrɛ] LT",lastDay:"[Kunu lɛrɛ] LT",lastWeek:"dddd [tɛmɛnen lɛrɛ] LT",sameElse:"L"},relativeTime:{future:"%s kɔnɔ",past:"a bɛ %s bɔ",s:"sanga dama dama",ss:"sekondi %d",m:"miniti kelen",mm:"miniti %d",h:"lɛrɛ kelen",hh:"lɛrɛ %d",d:"tile kelen",dd:"tile %d",M:"kalo kelen",MM:"kalo %d",y:"san kelen",yy:"san %d"},week:{dow:1,doy:4}})}(n("wd/R"))},qvJo:function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={s:["थोडया सॅकंडांनी","थोडे सॅकंड"],ss:[e+" सॅकंडांनी",e+" सॅकंड"],m:["एका मिणटान","एक मिनूट"],mm:[e+" मिणटांनी",e+" मिणटां"],h:["एका वरान","एक वर"],hh:[e+" वरांनी",e+" वरां"],d:["एका दिसान","एक दीस"],dd:[e+" दिसांनी",e+" दीस"],M:["एका म्हयन्यान","एक म्हयनो"],MM:[e+" म्हयन्यानी",e+" म्हयने"],y:["एका वर्सान","एक वर्स"],yy:[e+" वर्सांनी",e+" वर्सां"]};return a?r[n][0]:r[n][1]}e.defineLocale("gom-deva",{months:{standalone:"जानेवारी_फेब्रुवारी_मार्च_एप्रील_मे_जून_जुलय_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),format:"जानेवारीच्या_फेब्रुवारीच्या_मार्चाच्या_एप्रीलाच्या_मेयाच्या_जूनाच्या_जुलयाच्या_ऑगस्टाच्या_सप्टेंबराच्या_ऑक्टोबराच्या_नोव्हेंबराच्या_डिसेंबराच्या".split("_"),isFormat:/MMMM(\s)+D[oD]?/},monthsShort:"जाने._फेब्रु._मार्च_एप्री._मे_जून_जुल._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),monthsParseExact:!0,weekdays:"आयतार_सोमार_मंगळार_बुधवार_बिरेस्तार_सुक्रार_शेनवार".split("_"),weekdaysShort:"आयत._सोम._मंगळ._बुध._ब्रेस्त._सुक्र._शेन.".split("_"),weekdaysMin:"आ_सो_मं_बु_ब्रे_सु_शे".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"A h:mm [वाजतां]",LTS:"A h:mm:ss [वाजतां]",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY A h:mm [वाजतां]",LLLL:"dddd, MMMM Do, YYYY, A h:mm [वाजतां]",llll:"ddd, D MMM YYYY, A h:mm [वाजतां]"},calendar:{sameDay:"[आयज] LT",nextDay:"[फाल्यां] LT",nextWeek:"[फुडलो] dddd[,] LT",lastDay:"[काल] LT",lastWeek:"[फाटलो] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%s",past:"%s आदीं",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}(वेर)/,ordinal:function(e,t){switch(t){case"D":return e+"वेर";default:case"M":case"Q":case"DDD":case"d":case"w":case"W":return e}},week:{dow:0,doy:3},meridiemParse:/राती|सकाळीं|दनपारां|सांजे/,meridiemHour:function(e,t){return 12===e&&(e=0),"राती"===t?e<4?e:e+12:"सकाळीं"===t?e:"दनपारां"===t?e>12?e:e+12:"सांजे"===t?e+12:void 0},meridiem:function(e,t,n){return e<4?"राती":e<12?"सकाळीं":e<16?"दनपारां":e<20?"सांजे":"राती"}})}(n("wd/R"))},raLr:function(e,t,n){!function(e){"use strict";function t(e,t,n){var a,r;return"m"===n?t?"хвилина":"хвилину":"h"===n?t?"година":"годину":e+" "+(a=+e,r={ss:t?"секунда_секунди_секунд":"секунду_секунди_секунд",mm:t?"хвилина_хвилини_хвилин":"хвилину_хвилини_хвилин",hh:t?"година_години_годин":"годину_години_годин",dd:"день_дні_днів",MM:"місяць_місяці_місяців",yy:"рік_роки_років"}[n].split("_"),a%10==1&&a%100!=11?r[0]:a%10>=2&&a%10<=4&&(a%100<10||a%100>=20)?r[1]:r[2])}function n(e){return function(){return e+"о"+(11===this.hours()?"б":"")+"] LT"}}e.defineLocale("uk",{months:{format:"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня".split("_"),standalone:"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень".split("_")},monthsShort:"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд".split("_"),weekdays:function(e,t){var n={nominative:"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота".split("_"),accusative:"неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу".split("_"),genitive:"неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи".split("_")};return!0===e?n.nominative.slice(1,7).concat(n.nominative.slice(0,1)):e?n[/(\[[ВвУу]\]) ?dddd/.test(t)?"accusative":/\[?(?:минулої|наступної)? ?\] ?dddd/.test(t)?"genitive":"nominative"][e.day()]:n.nominative},weekdaysShort:"нд_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY р.",LLL:"D MMMM YYYY р., HH:mm",LLLL:"dddd, D MMMM YYYY р., HH:mm"},calendar:{sameDay:n("[Сьогодні "),nextDay:n("[Завтра "),lastDay:n("[Вчора "),nextWeek:n("[У] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return n("[Минулої] dddd [").call(this);case 1:case 2:case 4:return n("[Минулого] dddd [").call(this)}},sameElse:"L"},relativeTime:{future:"за %s",past:"%s тому",s:"декілька секунд",ss:t,m:t,mm:t,h:"годину",hh:t,d:"день",dd:t,M:"місяць",MM:t,y:"рік",yy:t},meridiemParse:/ночі|ранку|дня|вечора/,isPM:function(e){return/^(дня|вечора)$/.test(e)},meridiem:function(e,t,n){return e<4?"ночі":e<12?"ранку":e<17?"дня":"вечора"},dayOfMonthOrdinalParse:/\d{1,2}-(й|го)/,ordinal:function(e,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return e+"-й";case"D":return e+"-го";default:return e}},week:{dow:1,doy:7}})}(n("wd/R"))},"s+uk":function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[n][0]:r[n][1]}e.defineLocale("de-at",{months:"Jänner_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jän._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},sp3z:function(e,t,n){!function(e){"use strict";e.defineLocale("lo",{months:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),monthsShort:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),weekdays:"ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysShort:"ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysMin:"ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"ວັນdddd D MMMM YYYY HH:mm"},meridiemParse:/ຕອນເຊົ້າ|ຕອນແລງ/,isPM:function(e){return"ຕອນແລງ"===e},meridiem:function(e,t,n){return e<12?"ຕອນເຊົ້າ":"ຕອນແລງ"},calendar:{sameDay:"[ມື້ນີ້ເວລາ] LT",nextDay:"[ມື້ອື່ນເວລາ] LT",nextWeek:"[ວັນ]dddd[ໜ້າເວລາ] LT",lastDay:"[ມື້ວານນີ້ເວລາ] LT",lastWeek:"[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT",sameElse:"L"},relativeTime:{future:"ອີກ %s",past:"%sຜ່ານມາ",s:"ບໍ່ເທົ່າໃດວິນາທີ",ss:"%d ວິນາທີ",m:"1 ນາທີ",mm:"%d ນາທີ",h:"1 ຊົ່ວໂມງ",hh:"%d ຊົ່ວໂມງ",d:"1 ມື້",dd:"%d ມື້",M:"1 ເດືອນ",MM:"%d ເດືອນ",y:"1 ປີ",yy:"%d ປີ"},dayOfMonthOrdinalParse:/(ທີ່)\d{1,2}/,ordinal:function(e){return"ທີ່"+e}})}(n("wd/R"))},"t+mt":function(e,t,n){!function(e){"use strict";e.defineLocale("en-sg",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},dayOfMonthOrdinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(e){var t=e%10;return e+(1==~~(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")},week:{dow:1,doy:4}})}(n("wd/R"))},tGlX:function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[n][0]:r[n][1]}e.defineLocale("de",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},tT3J:function(e,t,n){!function(e){"use strict";e.defineLocale("tzm-latn",{months:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),monthsShort:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),weekdays:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysShort:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysMin:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[asdkh g] LT",nextDay:"[aska g] LT",nextWeek:"dddd [g] LT",lastDay:"[assant g] LT",lastWeek:"dddd [g] LT",sameElse:"L"},relativeTime:{future:"dadkh s yan %s",past:"yan %s",s:"imik",ss:"%d imik",m:"minuḍ",mm:"%d minuḍ",h:"saɛa",hh:"%d tassaɛin",d:"ass",dd:"%d ossan",M:"ayowr",MM:"%d iyyirn",y:"asgas",yy:"%d isgasn"},week:{dow:6,doy:12}})}(n("wd/R"))},tUCv:function(e,t,n){!function(e){"use strict";e.defineLocale("jv",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des".split("_"),weekdays:"Minggu_Senen_Seloso_Rebu_Kemis_Jemuwah_Septu".split("_"),weekdaysShort:"Min_Sen_Sel_Reb_Kem_Jem_Sep".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sp".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/enjing|siyang|sonten|ndalu/,meridiemHour:function(e,t){return 12===e&&(e=0),"enjing"===t?e:"siyang"===t?e>=11?e:e+12:"sonten"===t||"ndalu"===t?e+12:void 0},meridiem:function(e,t,n){return e<11?"enjing":e<15?"siyang":e<19?"sonten":"ndalu"},calendar:{sameDay:"[Dinten puniko pukul] LT",nextDay:"[Mbenjang pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kala wingi pukul] LT",lastWeek:"dddd [kepengker pukul] LT",sameElse:"L"},relativeTime:{future:"wonten ing %s",past:"%s ingkang kepengker",s:"sawetawis detik",ss:"%d detik",m:"setunggal menit",mm:"%d menit",h:"setunggal jam",hh:"%d jam",d:"sedinten",dd:"%d dinten",M:"sewulan",MM:"%d wulan",y:"setaun",yy:"%d taun"},week:{dow:1,doy:7}})}(n("wd/R"))},tbfe:function(e,t,n){!function(e){"use strict";var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),n="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),a=[/^ene/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i],r=/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre|ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i;e.defineLocale("es-mx",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(e,a){return e?/-MMM-/.test(a)?n[e.month()]:t[e.month()]:t},monthsRegex:r,monthsShortRegex:r,monthsStrictRegex:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i,monthsShortStrictRegex:/^(ene\.?|feb\.?|mar\.?|abr\.?|may\.?|jun\.?|jul\.?|ago\.?|sep\.?|oct\.?|nov\.?|dic\.?)/i,monthsParse:a,longMonthsParse:a,shortMonthsParse:a,weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(1!==this.hours()?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(1!==this.hours()?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(1!==this.hours()?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(1!==this.hours()?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(1!==this.hours()?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",ss:"%d segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",w:"una semana",ww:"%d semanas",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},dayOfMonthOrdinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:0,doy:4},invalidDate:"Fecha inválida"})}(n("wd/R"))},u3GI:function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[e+" Tage",e+" Tagen"],w:["eine Woche","einer Woche"],M:["ein Monat","einem Monat"],MM:[e+" Monate",e+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[e+" Jahre",e+" Jahren"]};return t?r[n][0]:r[n][1]}e.defineLocale("de-ch",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sep._Okt._Nov._Dez.".split("_"),monthsParseExact:!0,weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",ss:"%d Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,w:t,ww:"%d Wochen",M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},uEye:function(e,t,n){!function(e){"use strict";e.defineLocale("nn",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_apr._mai_juni_juli_aug._sep._okt._nov._des.".split("_"),monthsParseExact:!0,weekdays:"sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag".split("_"),weekdaysShort:"su._må._ty._on._to._fr._lau.".split("_"),weekdaysMin:"su_må_ty_on_to_fr_la".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[I dag klokka] LT",nextDay:"[I morgon klokka] LT",nextWeek:"dddd [klokka] LT",lastDay:"[I går klokka] LT",lastWeek:"[Føregåande] dddd [klokka] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s sidan",s:"nokre sekund",ss:"%d sekund",m:"eit minutt",mm:"%d minutt",h:"ein time",hh:"%d timar",d:"ein dag",dd:"%d dagar",w:"ei veke",ww:"%d veker",M:"ein månad",MM:"%d månader",y:"eit år",yy:"%d år"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},uXwI:function(e,t,n){!function(e){"use strict";var t={ss:"sekundes_sekundēm_sekunde_sekundes".split("_"),m:"minūtes_minūtēm_minūte_minūtes".split("_"),mm:"minūtes_minūtēm_minūte_minūtes".split("_"),h:"stundas_stundām_stunda_stundas".split("_"),hh:"stundas_stundām_stunda_stundas".split("_"),d:"dienas_dienām_diena_dienas".split("_"),dd:"dienas_dienām_diena_dienas".split("_"),M:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),MM:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),y:"gada_gadiem_gads_gadi".split("_"),yy:"gada_gadiem_gads_gadi".split("_")};function n(e,t,n){return n?t%10==1&&t%100!=11?e[2]:e[3]:t%10==1&&t%100!=11?e[0]:e[1]}function a(e,a,r){return e+" "+n(t[r],e,a)}function r(e,a,r){return n(t[r],e,a)}e.defineLocale("lv",{months:"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris".split("_"),monthsShort:"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec".split("_"),weekdays:"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena".split("_"),weekdaysShort:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysMin:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY.",LL:"YYYY. [gada] D. MMMM",LLL:"YYYY. [gada] D. MMMM, HH:mm",LLLL:"YYYY. [gada] D. MMMM, dddd, HH:mm"},calendar:{sameDay:"[Šodien pulksten] LT",nextDay:"[Rīt pulksten] LT",nextWeek:"dddd [pulksten] LT",lastDay:"[Vakar pulksten] LT",lastWeek:"[Pagājušā] dddd [pulksten] LT",sameElse:"L"},relativeTime:{future:"pēc %s",past:"pirms %s",s:function(e,t){return t?"dažas sekundes":"dažām sekundēm"},ss:a,m:r,mm:a,h:r,hh:a,d:r,dd:a,M:r,MM:a,y:r,yy:a},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},uqS5:function(e,t,n){"use strict";n.d(t,"a",(function(){return I}));var a,r=n("XuX8"),s=n.n(r),i=n("xjcK"),o=n("6GPe"),d=n("AFYn"),u=n("m/oX"),l=n("pyNs"),_=n("m3aq"),c=n("kGy3"),m=n("a3f1"),h=n("WPLV"),f=n("2C+6"),M=n("z3V6"),p=n("STsD"),y=n("kO/s"),g=n("YC3Q"),L=n("jBgq"),Y=n("c4aD"),w=n("8p45"),b=n("zio1");function k(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?k(Object(n),!0).forEach((function(t){D(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var T=Object(m.d)(i.q,"request-state"),S=Object(m.d)(i.q,"toggle"),j=Object(m.e)(i.q,"state"),x=Object(m.e)(i.q,"sync-state"),H=Object(h.a)("visible",{type:l.g,defaultValue:!1,event:d.d}),P=H.mixin,O=H.props,C=H.prop,E=H.event,R=Object(M.d)(Object(f.m)(v(v(v({},y.b),O),{},{ariaLabel:Object(M.c)(l.t),ariaLabelledby:Object(M.c)(l.t),backdrop:Object(M.c)(l.g,!1),backdropVariant:Object(M.c)(l.t,"dark"),bgVariant:Object(M.c)(l.t,"light"),bodyClass:Object(M.c)(l.e),closeLabel:Object(M.c)(l.t),footerClass:Object(M.c)(l.e),headerClass:Object(M.c)(l.e),lazy:Object(M.c)(l.g,!1),noCloseOnBackdrop:Object(M.c)(l.g,!1),noCloseOnEsc:Object(M.c)(l.g,!1),noCloseOnRouteChange:Object(M.c)(l.g,!1),noEnforceFocus:Object(M.c)(l.g,!1),noHeader:Object(M.c)(l.g,!1),noHeaderClose:Object(M.c)(l.g,!1),noSlide:Object(M.c)(l.g,!1),right:Object(M.c)(l.g,!1),shadow:Object(M.c)(l.j,!1),sidebarClass:Object(M.c)(l.e),tag:Object(M.c)(l.t,"div"),textVariant:Object(M.c)(l.t,"dark"),title:Object(M.c)(l.t),width:Object(M.c)(l.t),zIndex:Object(M.c)(l.o)})),i.hb),F=function(e,t){if(t.noHeader)return e();var n=t.normalizeSlot(_.r,t.slotScope);if(!n){var a=function(e,t){var n=t.normalizeSlot(_.R,t.slotScope)||t.title;return n?e("strong",{attrs:{id:t.safeId("__title__")}},[n]):e("span")}(e,t),r=function(e,t){if(t.noHeaderClose)return e();var n=t.closeLabel,a=t.textVariant,r=t.hide;return e(w.a,{props:{ariaLabel:n,textVariant:a},on:{click:r},ref:"close-button"},[t.normalizeSlot(_.s)||e(Y.e)])}(e,t);n=t.right?[r,a]:[a,r]}return e("header",{staticClass:"".concat("b-sidebar","-header"),class:t.headerClass,key:"header"},n)},W=function(e,t){return e("div",{staticClass:"".concat("b-sidebar","-body"),class:t.bodyClass,key:"body"},[t.normalizeSlot(_.h,t.slotScope)])},A=function(e,t){var n=t.normalizeSlot(_.q,t.slotScope);return n?e("footer",{staticClass:"".concat("b-sidebar","-footer"),class:t.footerClass,key:"footer"},[n]):e()},z=function(e,t){var n=F(e,t);return t.lazy&&!t.isOpen?n:[n,W(e,t),A(e,t)]},N=function(e,t){if(!t.backdrop)return e();var n=t.backdropVariant;return e("div",{directives:[{name:"show",value:t.localShow}],staticClass:"b-sidebar-backdrop",class:D({},"bg-".concat(n),n),on:{click:t.onBackdropClick}})},I=s.a.extend({name:i.hb,mixins:[p.a,y.a,P,g.a,L.a],inheritAttrs:!1,props:R,data:function(){var e=!!this[C];return{localShow:e,isOpen:e}},computed:{transitionProps:function(){return this.noSlide?{css:!0}:{css:!0,enterClass:"",enterActiveClass:"slide",enterToClass:"show",leaveClass:"show",leaveActiveClass:"slide",leaveToClass:""}},slotScope:function(){return{hide:this.hide,right:this.right,visible:this.localShow}},hasTitle:function(){var e=this.$scopedSlots,t=this.$slots;return!(this.noHeader||this.hasNormalizedSlot(_.r)||!this.normalizeSlot(_.R,this.slotScope,e,t)&&!this.title)},titleId:function(){return this.hasTitle?this.safeId("__title__"):null},computedAttrs:function(){return v(v({},this.bvAttrs),{},{id:this.safeId(),tabindex:"-1",role:"dialog","aria-modal":this.backdrop?"true":"false","aria-hidden":this.localShow?null:"true","aria-label":this.ariaLabel||null,"aria-labelledby":this.ariaLabelledby||this.titleId||null})}},watch:(a={},D(a,C,(function(e,t){e!==t&&(this.localShow=e)})),D(a,"localShow",(function(e,t){e!==t&&(this.emitState(e),this.$emit(E,e))})),D(a,"$route",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.noCloseOnRouteChange||e.fullPath===t.fullPath||this.hide()})),a),created:function(){this.$_returnFocusEl=null},mounted:function(){var e=this;this.listenOnRoot(S,this.handleToggle),this.listenOnRoot(T,this.handleSync),this.$nextTick((function(){e.emitState(e.localShow)}))},activated:function(){this.emitSync()},beforeDestroy:function(){this.localShow=!1,this.$_returnFocusEl=null},methods:{hide:function(){this.localShow=!1},emitState:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(j,this.safeId(),e)},emitSync:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(x,this.safeId(),e)},handleToggle:function(e){e&&e===this.safeId()&&(this.localShow=!this.localShow)},handleSync:function(e){var t=this;e&&e===this.safeId()&&this.$nextTick((function(){t.emitSync(t.localShow)}))},onKeydown:function(e){var t=e.keyCode;!this.noCloseOnEsc&&t===u.d&&this.localShow&&this.hide()},onBackdropClick:function(){this.localShow&&!this.noCloseOnBackdrop&&this.hide()},onTopTrapFocus:function(){var e=Object(c.n)(this.$refs.content);this.enforceFocus(e.reverse()[0])},onBottomTrapFocus:function(){var e=Object(c.n)(this.$refs.content);this.enforceFocus(e[0])},onBeforeEnter:function(){this.$_returnFocusEl=Object(c.g)(o.f?[document.body]:[]),this.isOpen=!0},onAfterEnter:function(e){Object(c.f)(e,Object(c.g)())||this.enforceFocus(e),this.$emit(d.O)},onAfterLeave:function(){this.enforceFocus(this.$_returnFocusEl),this.$_returnFocusEl=null,this.isOpen=!1,this.$emit(d.s)},enforceFocus:function(e){this.noEnforceFocus||Object(c.d)(e)}},render:function(e){var t,n=this.bgVariant,a=this.width,r=this.textVariant,s=this.localShow,i=""===this.shadow||this.shadow,o=e(this.tag,{staticClass:"b-sidebar",class:[(t={shadow:!0===i},D(t,"shadow-".concat(i),i&&!0!==i),D(t,"".concat("b-sidebar","-right"),this.right),D(t,"bg-".concat(n),n),D(t,"text-".concat(r),r),t),this.sidebarClass],style:{width:a},attrs:this.computedAttrs,directives:[{name:"show",value:s}],ref:"content"},[z(e,this)]);o=e("transition",{props:this.transitionProps,on:{beforeEnter:this.onBeforeEnter,afterEnter:this.onAfterEnter,afterLeave:this.onAfterLeave}},[o]);var d=e(b.a,{props:{noFade:this.noSlide}},[N(e,this)]),u=e(),l=e();return this.backdrop&&s&&(u=e("div",{attrs:{tabindex:"0"},on:{focus:this.onTopTrapFocus}}),l=e("div",{attrs:{tabindex:"0"},on:{focus:this.onBottomTrapFocus}})),e("div",{staticClass:"b-sidebar-outer",style:{zIndex:this.zIndex},attrs:{tabindex:"-1"},on:{keydown:this.onKeydown}},[u,o,l,d])}})},wQk9:function(e,t,n){!function(e){"use strict";e.defineLocale("tzm",{months:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),monthsShort:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),weekdays:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysShort:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysMin:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ⴰⵙⴷⵅ ⴴ] LT",nextDay:"[ⴰⵙⴽⴰ ⴴ] LT",nextWeek:"dddd [ⴴ] LT",lastDay:"[ⴰⵚⴰⵏⵜ ⴴ] LT",lastWeek:"dddd [ⴴ] LT",sameElse:"L"},relativeTime:{future:"ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s",past:"ⵢⴰⵏ %s",s:"ⵉⵎⵉⴽ",ss:"%d ⵉⵎⵉⴽ",m:"ⵎⵉⵏⵓⴺ",mm:"%d ⵎⵉⵏⵓⴺ",h:"ⵙⴰⵄⴰ",hh:"%d ⵜⴰⵙⵙⴰⵄⵉⵏ",d:"ⴰⵙⵙ",dd:"%d oⵙⵙⴰⵏ",M:"ⴰⵢoⵓⵔ",MM:"%d ⵉⵢⵢⵉⵔⵏ",y:"ⴰⵙⴳⴰⵙ",yy:"%d ⵉⵙⴳⴰⵙⵏ"},week:{dow:6,doy:12}})}(n("wd/R"))},"wd/R":function(e,t,n){(function(e){e.exports=function(){"use strict";var t,a;function r(){return t.apply(null,arguments)}function s(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function i(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function o(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function d(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var t;for(t in e)if(o(e,t))return!1;return!0}function u(e){return void 0===e}function l(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function _(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function c(e,t){var n,a=[],r=e.length;for(n=0;n<r;++n)a.push(t(e[n],n));return a}function m(e,t){for(var n in t)o(t,n)&&(e[n]=t[n]);return o(t,"toString")&&(e.toString=t.toString),o(t,"valueOf")&&(e.valueOf=t.valueOf),e}function h(e,t,n,a){return vt(e,t,n,a,!0).utc()}function f(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function M(e){if(null==e._isValid){var t=f(e),n=a.call(t.parsedDateParts,(function(e){return null!=e})),r=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n);if(e._strict&&(r=r&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return r;e._isValid=r}return e._isValid}function p(e){var t=h(NaN);return null!=e?m(f(t),e):f(t).userInvalidated=!0,t}a=Array.prototype.some?Array.prototype.some:function(e){var t,n=Object(this),a=n.length>>>0;for(t=0;t<a;t++)if(t in n&&e.call(this,n[t],t,n))return!0;return!1};var y=r.momentProperties=[],g=!1;function L(e,t){var n,a,r,s=y.length;if(u(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),u(t._i)||(e._i=t._i),u(t._f)||(e._f=t._f),u(t._l)||(e._l=t._l),u(t._strict)||(e._strict=t._strict),u(t._tzm)||(e._tzm=t._tzm),u(t._isUTC)||(e._isUTC=t._isUTC),u(t._offset)||(e._offset=t._offset),u(t._pf)||(e._pf=f(t)),u(t._locale)||(e._locale=t._locale),s>0)for(n=0;n<s;n++)u(r=t[a=y[n]])||(e[a]=r);return e}function Y(e){L(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===g&&(g=!0,r.updateOffset(this),g=!1)}function w(e){return e instanceof Y||null!=e&&null!=e._isAMomentObject}function b(e){!1===r.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function k(e,t){var n=!0;return m((function(){if(null!=r.deprecationHandler&&r.deprecationHandler(null,e),n){var a,s,i,d=[],u=arguments.length;for(s=0;s<u;s++){if(a="","object"==typeof arguments[s]){for(i in a+="\n["+s+"] ",arguments[0])o(arguments[0],i)&&(a+=i+": "+arguments[0][i]+", ");a=a.slice(0,-2)}else a=arguments[s];d.push(a)}b(e+"\nArguments: "+Array.prototype.slice.call(d).join("")+"\n"+(new Error).stack),n=!1}return t.apply(this,arguments)}),t)}var v,D={};function T(e,t){null!=r.deprecationHandler&&r.deprecationHandler(e,t),D[e]||(b(t),D[e]=!0)}function S(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function j(e,t){var n,a=m({},e);for(n in t)o(t,n)&&(i(e[n])&&i(t[n])?(a[n]={},m(a[n],e[n]),m(a[n],t[n])):null!=t[n]?a[n]=t[n]:delete a[n]);for(n in e)o(e,n)&&!o(t,n)&&i(e[n])&&(a[n]=m({},a[n]));return a}function x(e){null!=e&&this.set(e)}function H(e,t,n){var a=""+Math.abs(e),r=t-a.length;return(e>=0?n?"+":"":"-")+Math.pow(10,Math.max(0,r)).toString().substr(1)+a}r.suppressDeprecationWarnings=!1,r.deprecationHandler=null,v=Object.keys?Object.keys:function(e){var t,n=[];for(t in e)o(e,t)&&n.push(t);return n};var P=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,O=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,C={},E={};function R(e,t,n,a){var r=a;"string"==typeof a&&(r=function(){return this[a]()}),e&&(E[e]=r),t&&(E[t[0]]=function(){return H(r.apply(this,arguments),t[1],t[2])}),n&&(E[n]=function(){return this.localeData().ordinal(r.apply(this,arguments),e)})}function F(e,t){return e.isValid()?(t=W(t,e.localeData()),C[t]=C[t]||function(e){var t,n,a,r=e.match(P);for(t=0,n=r.length;t<n;t++)E[r[t]]?r[t]=E[r[t]]:r[t]=(a=r[t]).match(/\[[\s\S]/)?a.replace(/^\[|\]$/g,""):a.replace(/\\/g,"");return function(t){var a,s="";for(a=0;a<n;a++)s+=S(r[a])?r[a].call(t,e):r[a];return s}}(t),C[t](e)):e.localeData().invalidDate()}function W(e,t){var n=5;function a(e){return t.longDateFormat(e)||e}for(O.lastIndex=0;n>=0&&O.test(e);)e=e.replace(O,a),O.lastIndex=0,n-=1;return e}var A={};function z(e,t){var n=e.toLowerCase();A[n]=A[n+"s"]=A[t]=e}function N(e){return"string"==typeof e?A[e]||A[e.toLowerCase()]:void 0}function I(e){var t,n,a={};for(n in e)o(e,n)&&(t=N(n))&&(a[t]=e[n]);return a}var U={};function J(e,t){U[e]=t}function B(e){return e%4==0&&e%100!=0||e%400==0}function G(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function $(e){var t=+e,n=0;return 0!==t&&isFinite(t)&&(n=G(t)),n}function V(e,t){return function(n){return null!=n?(Q(this,e,n),r.updateOffset(this,t),this):q(this,e)}}function q(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function Q(e,t,n){e.isValid()&&!isNaN(n)&&("FullYear"===t&&B(e.year())&&1===e.month()&&29===e.date()?(n=$(n),e._d["set"+(e._isUTC?"UTC":"")+t](n,e.month(),we(n,e.month()))):e._d["set"+(e._isUTC?"UTC":"")+t](n))}var K,X=/\d/,Z=/\d\d/,ee=/\d{3}/,te=/\d{4}/,ne=/[+-]?\d{6}/,ae=/\d\d?/,re=/\d\d\d\d?/,se=/\d\d\d\d\d\d?/,ie=/\d{1,3}/,oe=/\d{1,4}/,de=/[+-]?\d{1,6}/,ue=/\d+/,le=/[+-]?\d+/,_e=/Z|[+-]\d\d:?\d\d/gi,ce=/Z|[+-]\d\d(?::?\d\d)?/gi,me=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;function he(e,t,n){K[e]=S(t)?t:function(e,a){return e&&n?n:t}}function fe(e,t){return o(K,e)?K[e](t._strict,t._locale):new RegExp(Me(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,n,a,r){return t||n||a||r}))))}function Me(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}K={};var pe,ye={};function ge(e,t){var n,a,r=t;for("string"==typeof e&&(e=[e]),l(t)&&(r=function(e,n){n[t]=$(e)}),a=e.length,n=0;n<a;n++)ye[e[n]]=r}function Le(e,t){ge(e,(function(e,n,a,r){a._w=a._w||{},t(e,a._w,a,r)}))}function Ye(e,t,n){null!=t&&o(ye,e)&&ye[e](t,n._a,n,e)}function we(e,t){if(isNaN(e)||isNaN(t))return NaN;var n,a=(t%(n=12)+n)%n;return e+=(t-a)/12,1===a?B(e)?29:28:31-a%7%2}pe=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},R("M",["MM",2],"Mo",(function(){return this.month()+1})),R("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),R("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),z("month","M"),J("month",8),he("M",ae),he("MM",ae,Z),he("MMM",(function(e,t){return t.monthsShortRegex(e)})),he("MMMM",(function(e,t){return t.monthsRegex(e)})),ge(["M","MM"],(function(e,t){t[1]=$(e)-1})),ge(["MMM","MMMM"],(function(e,t,n,a){var r=n._locale.monthsParse(e,a,n._strict);null!=r?t[1]=r:f(n).invalidMonth=e}));var be="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ke="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),ve=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,De=me,Te=me;function Se(e,t,n){var a,r,s,i=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],a=0;a<12;++a)s=h([2e3,a]),this._shortMonthsParse[a]=this.monthsShort(s,"").toLocaleLowerCase(),this._longMonthsParse[a]=this.months(s,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(r=pe.call(this._shortMonthsParse,i))?r:null:-1!==(r=pe.call(this._longMonthsParse,i))?r:null:"MMM"===t?-1!==(r=pe.call(this._shortMonthsParse,i))||-1!==(r=pe.call(this._longMonthsParse,i))?r:null:-1!==(r=pe.call(this._longMonthsParse,i))||-1!==(r=pe.call(this._shortMonthsParse,i))?r:null}function je(e,t){var n;if(!e.isValid())return e;if("string"==typeof t)if(/^\d+$/.test(t))t=$(t);else if(!l(t=e.localeData().monthsParse(t)))return e;return n=Math.min(e.date(),we(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,n),e}function xe(e){return null!=e?(je(this,e),r.updateOffset(this,!0),this):q(this,"Month")}function He(){function e(e,t){return t.length-e.length}var t,n,a=[],r=[],s=[];for(t=0;t<12;t++)n=h([2e3,t]),a.push(this.monthsShort(n,"")),r.push(this.months(n,"")),s.push(this.months(n,"")),s.push(this.monthsShort(n,""));for(a.sort(e),r.sort(e),s.sort(e),t=0;t<12;t++)a[t]=Me(a[t]),r[t]=Me(r[t]);for(t=0;t<24;t++)s[t]=Me(s[t]);this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Pe(e){return B(e)?366:365}R("Y",0,0,(function(){var e=this.year();return e<=9999?H(e,4):"+"+e})),R(0,["YY",2],0,(function(){return this.year()%100})),R(0,["YYYY",4],0,"year"),R(0,["YYYYY",5],0,"year"),R(0,["YYYYYY",6,!0],0,"year"),z("year","y"),J("year",1),he("Y",le),he("YY",ae,Z),he("YYYY",oe,te),he("YYYYY",de,ne),he("YYYYYY",de,ne),ge(["YYYYY","YYYYYY"],0),ge("YYYY",(function(e,t){t[0]=2===e.length?r.parseTwoDigitYear(e):$(e)})),ge("YY",(function(e,t){t[0]=r.parseTwoDigitYear(e)})),ge("Y",(function(e,t){t[0]=parseInt(e,10)})),r.parseTwoDigitYear=function(e){return $(e)+($(e)>68?1900:2e3)};var Oe=V("FullYear",!0);function Ce(e,t,n,a,r,s,i){var o;return e<100&&e>=0?(o=new Date(e+400,t,n,a,r,s,i),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,a,r,s,i),o}function Ee(e){var t,n;return e<100&&e>=0?((n=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,n)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Re(e,t,n){var a=7+t-n;return-(7+Ee(e,0,a).getUTCDay()-t)%7+a-1}function Fe(e,t,n,a,r){var s,i,o=1+7*(t-1)+(7+n-a)%7+Re(e,a,r);return o<=0?i=Pe(s=e-1)+o:o>Pe(e)?(s=e+1,i=o-Pe(e)):(s=e,i=o),{year:s,dayOfYear:i}}function We(e,t,n){var a,r,s=Re(e.year(),t,n),i=Math.floor((e.dayOfYear()-s-1)/7)+1;return i<1?a=i+Ae(r=e.year()-1,t,n):i>Ae(e.year(),t,n)?(a=i-Ae(e.year(),t,n),r=e.year()+1):(r=e.year(),a=i),{week:a,year:r}}function Ae(e,t,n){var a=Re(e,t,n),r=Re(e+1,t,n);return(Pe(e)-a+r)/7}function ze(e,t){return e.slice(t,7).concat(e.slice(0,t))}R("w",["ww",2],"wo","week"),R("W",["WW",2],"Wo","isoWeek"),z("week","w"),z("isoWeek","W"),J("week",5),J("isoWeek",5),he("w",ae),he("ww",ae,Z),he("W",ae),he("WW",ae,Z),Le(["w","ww","W","WW"],(function(e,t,n,a){t[a.substr(0,1)]=$(e)})),R("d",0,"do","day"),R("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),R("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),R("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),R("e",0,0,"weekday"),R("E",0,0,"isoWeekday"),z("day","d"),z("weekday","e"),z("isoWeekday","E"),J("day",11),J("weekday",11),J("isoWeekday",11),he("d",ae),he("e",ae),he("E",ae),he("dd",(function(e,t){return t.weekdaysMinRegex(e)})),he("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),he("dddd",(function(e,t){return t.weekdaysRegex(e)})),Le(["dd","ddd","dddd"],(function(e,t,n,a){var r=n._locale.weekdaysParse(e,a,n._strict);null!=r?t.d=r:f(n).invalidWeekday=e})),Le(["d","e","E"],(function(e,t,n,a){t[a]=$(e)}));var Ne="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ie="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ue="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Je=me,Be=me,Ge=me;function $e(e,t,n){var a,r,s,i=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],a=0;a<7;++a)s=h([2e3,1]).day(a),this._minWeekdaysParse[a]=this.weekdaysMin(s,"").toLocaleLowerCase(),this._shortWeekdaysParse[a]=this.weekdaysShort(s,"").toLocaleLowerCase(),this._weekdaysParse[a]=this.weekdays(s,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(r=pe.call(this._weekdaysParse,i))?r:null:"ddd"===t?-1!==(r=pe.call(this._shortWeekdaysParse,i))?r:null:-1!==(r=pe.call(this._minWeekdaysParse,i))?r:null:"dddd"===t?-1!==(r=pe.call(this._weekdaysParse,i))||-1!==(r=pe.call(this._shortWeekdaysParse,i))||-1!==(r=pe.call(this._minWeekdaysParse,i))?r:null:"ddd"===t?-1!==(r=pe.call(this._shortWeekdaysParse,i))||-1!==(r=pe.call(this._weekdaysParse,i))||-1!==(r=pe.call(this._minWeekdaysParse,i))?r:null:-1!==(r=pe.call(this._minWeekdaysParse,i))||-1!==(r=pe.call(this._weekdaysParse,i))||-1!==(r=pe.call(this._shortWeekdaysParse,i))?r:null}function Ve(){function e(e,t){return t.length-e.length}var t,n,a,r,s,i=[],o=[],d=[],u=[];for(t=0;t<7;t++)n=h([2e3,1]).day(t),a=Me(this.weekdaysMin(n,"")),r=Me(this.weekdaysShort(n,"")),s=Me(this.weekdays(n,"")),i.push(a),o.push(r),d.push(s),u.push(a),u.push(r),u.push(s);i.sort(e),o.sort(e),d.sort(e),u.sort(e),this._weekdaysRegex=new RegExp("^("+u.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+d.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function qe(){return this.hours()%12||12}function Qe(e,t){R(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function Ke(e,t){return t._meridiemParse}R("H",["HH",2],0,"hour"),R("h",["hh",2],0,qe),R("k",["kk",2],0,(function(){return this.hours()||24})),R("hmm",0,0,(function(){return""+qe.apply(this)+H(this.minutes(),2)})),R("hmmss",0,0,(function(){return""+qe.apply(this)+H(this.minutes(),2)+H(this.seconds(),2)})),R("Hmm",0,0,(function(){return""+this.hours()+H(this.minutes(),2)})),R("Hmmss",0,0,(function(){return""+this.hours()+H(this.minutes(),2)+H(this.seconds(),2)})),Qe("a",!0),Qe("A",!1),z("hour","h"),J("hour",13),he("a",Ke),he("A",Ke),he("H",ae),he("h",ae),he("k",ae),he("HH",ae,Z),he("hh",ae,Z),he("kk",ae,Z),he("hmm",re),he("hmmss",se),he("Hmm",re),he("Hmmss",se),ge(["H","HH"],3),ge(["k","kk"],(function(e,t,n){var a=$(e);t[3]=24===a?0:a})),ge(["a","A"],(function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e})),ge(["h","hh"],(function(e,t,n){t[3]=$(e),f(n).bigHour=!0})),ge("hmm",(function(e,t,n){var a=e.length-2;t[3]=$(e.substr(0,a)),t[4]=$(e.substr(a)),f(n).bigHour=!0})),ge("hmmss",(function(e,t,n){var a=e.length-4,r=e.length-2;t[3]=$(e.substr(0,a)),t[4]=$(e.substr(a,2)),t[5]=$(e.substr(r)),f(n).bigHour=!0})),ge("Hmm",(function(e,t,n){var a=e.length-2;t[3]=$(e.substr(0,a)),t[4]=$(e.substr(a))})),ge("Hmmss",(function(e,t,n){var a=e.length-4,r=e.length-2;t[3]=$(e.substr(0,a)),t[4]=$(e.substr(a,2)),t[5]=$(e.substr(r))}));var Xe,Ze=V("Hours",!0),et={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:be,monthsShort:ke,week:{dow:0,doy:6},weekdays:Ne,weekdaysMin:Ue,weekdaysShort:Ie,meridiemParse:/[ap]\.?m?\.?/i},tt={},nt={};function at(e,t){var n,a=Math.min(e.length,t.length);for(n=0;n<a;n+=1)if(e[n]!==t[n])return n;return a}function rt(e){return e?e.toLowerCase().replace("_","-"):e}function st(t){var a=null;if(void 0===tt[t]&&void 0!==e&&e&&e.exports&&function(e){return null!=e.match("^[^/\\\\]*$")}(t))try{a=Xe._abbr,n("RnhZ")("./"+t),it(a)}catch(e){tt[t]=null}return tt[t]}function it(e,t){var n;return e&&((n=u(t)?dt(e):ot(e,t))?Xe=n:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),Xe._abbr}function ot(e,t){if(null!==t){var n,a=et;if(t.abbr=e,null!=tt[e])T("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),a=tt[e]._config;else if(null!=t.parentLocale)if(null!=tt[t.parentLocale])a=tt[t.parentLocale]._config;else{if(null==(n=st(t.parentLocale)))return nt[t.parentLocale]||(nt[t.parentLocale]=[]),nt[t.parentLocale].push({name:e,config:t}),null;a=n._config}return tt[e]=new x(j(a,t)),nt[e]&&nt[e].forEach((function(e){ot(e.name,e.config)})),it(e),tt[e]}return delete tt[e],null}function dt(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return Xe;if(!s(e)){if(t=st(e))return t;e=[e]}return function(e){for(var t,n,a,r,s=0;s<e.length;){for(t=(r=rt(e[s]).split("-")).length,n=(n=rt(e[s+1]))?n.split("-"):null;t>0;){if(a=st(r.slice(0,t).join("-")))return a;if(n&&n.length>=t&&at(r,n)>=t-1)break;t--}s++}return Xe}(e)}function ut(e){var t,n=e._a;return n&&-2===f(e).overflow&&(t=n[1]<0||n[1]>11?1:n[2]<1||n[2]>we(n[0],n[1])?2:n[3]<0||n[3]>24||24===n[3]&&(0!==n[4]||0!==n[5]||0!==n[6])?3:n[4]<0||n[4]>59?4:n[5]<0||n[5]>59?5:n[6]<0||n[6]>999?6:-1,f(e)._overflowDayOfYear&&(t<0||t>2)&&(t=2),f(e)._overflowWeeks&&-1===t&&(t=7),f(e)._overflowWeekday&&-1===t&&(t=8),f(e).overflow=t),e}var lt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,_t=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ct=/Z|[+-]\d\d(?::?\d\d)?/,mt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ht=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ft=/^\/?Date\((-?\d+)/i,Mt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,pt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function yt(e){var t,n,a,r,s,i,o=e._i,d=lt.exec(o)||_t.exec(o),u=mt.length,l=ht.length;if(d){for(f(e).iso=!0,t=0,n=u;t<n;t++)if(mt[t][1].exec(d[1])){r=mt[t][0],a=!1!==mt[t][2];break}if(null==r)return void(e._isValid=!1);if(d[3]){for(t=0,n=l;t<n;t++)if(ht[t][1].exec(d[3])){s=(d[2]||" ")+ht[t][0];break}if(null==s)return void(e._isValid=!1)}if(!a&&null!=s)return void(e._isValid=!1);if(d[4]){if(!ct.exec(d[4]))return void(e._isValid=!1);i="Z"}e._f=r+(s||"")+(i||""),bt(e)}else e._isValid=!1}function gt(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function Lt(e){var t,n,a,r,s,i,o,d,u=Mt.exec(e._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(u){if(n=u[4],a=u[3],r=u[2],s=u[5],i=u[6],o=u[7],d=[gt(n),ke.indexOf(a),parseInt(r,10),parseInt(s,10),parseInt(i,10)],o&&d.push(parseInt(o,10)),t=d,!function(e,t,n){return!e||Ie.indexOf(e)===new Date(t[0],t[1],t[2]).getDay()||(f(n).weekdayMismatch=!0,n._isValid=!1,!1)}(u[1],t,e))return;e._a=t,e._tzm=function(e,t,n){if(e)return pt[e];if(t)return 0;var a=parseInt(n,10),r=a%100;return(a-r)/100*60+r}(u[8],u[9],u[10]),e._d=Ee.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),f(e).rfc2822=!0}else e._isValid=!1}function Yt(e,t,n){return null!=e?e:null!=t?t:n}function wt(e){var t,n,a,s,i,o=[];if(!e._d){for(a=function(e){var t=new Date(r.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}(e),e._w&&null==e._a[2]&&null==e._a[1]&&function(e){var t,n,a,r,s,i,o,d,u;null!=(t=e._w).GG||null!=t.W||null!=t.E?(s=1,i=4,n=Yt(t.GG,e._a[0],We(Dt(),1,4).year),a=Yt(t.W,1),((r=Yt(t.E,1))<1||r>7)&&(d=!0)):(s=e._locale._week.dow,i=e._locale._week.doy,u=We(Dt(),s,i),n=Yt(t.gg,e._a[0],u.year),a=Yt(t.w,u.week),null!=t.d?((r=t.d)<0||r>6)&&(d=!0):null!=t.e?(r=t.e+s,(t.e<0||t.e>6)&&(d=!0)):r=s),a<1||a>Ae(n,s,i)?f(e)._overflowWeeks=!0:null!=d?f(e)._overflowWeekday=!0:(o=Fe(n,a,r,s,i),e._a[0]=o.year,e._dayOfYear=o.dayOfYear)}(e),null!=e._dayOfYear&&(i=Yt(e._a[0],a[0]),(e._dayOfYear>Pe(i)||0===e._dayOfYear)&&(f(e)._overflowDayOfYear=!0),n=Ee(i,0,e._dayOfYear),e._a[1]=n.getUTCMonth(),e._a[2]=n.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=o[t]=a[t];for(;t<7;t++)e._a[t]=o[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[3]&&0===e._a[4]&&0===e._a[5]&&0===e._a[6]&&(e._nextDay=!0,e._a[3]=0),e._d=(e._useUTC?Ee:Ce).apply(null,o),s=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[3]=24),e._w&&void 0!==e._w.d&&e._w.d!==s&&(f(e).weekdayMismatch=!0)}}function bt(e){if(e._f!==r.ISO_8601)if(e._f!==r.RFC_2822){e._a=[],f(e).empty=!0;var t,n,a,s,i,o,d,u=""+e._i,l=u.length,_=0;for(d=(a=W(e._f,e._locale).match(P)||[]).length,t=0;t<d;t++)s=a[t],(n=(u.match(fe(s,e))||[])[0])&&((i=u.substr(0,u.indexOf(n))).length>0&&f(e).unusedInput.push(i),u=u.slice(u.indexOf(n)+n.length),_+=n.length),E[s]?(n?f(e).empty=!1:f(e).unusedTokens.push(s),Ye(s,n,e)):e._strict&&!n&&f(e).unusedTokens.push(s);f(e).charsLeftOver=l-_,u.length>0&&f(e).unusedInput.push(u),e._a[3]<=12&&!0===f(e).bigHour&&e._a[3]>0&&(f(e).bigHour=void 0),f(e).parsedDateParts=e._a.slice(0),f(e).meridiem=e._meridiem,e._a[3]=function(e,t,n){var a;return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((a=e.isPM(n))&&t<12&&(t+=12),a||12!==t||(t=0),t):t}(e._locale,e._a[3],e._meridiem),null!==(o=f(e).era)&&(e._a[0]=e._locale.erasConvertYear(o,e._a[0])),wt(e),ut(e)}else Lt(e);else yt(e)}function kt(e){var t=e._i,n=e._f;return e._locale=e._locale||dt(e._l),null===t||void 0===n&&""===t?p({nullInput:!0}):("string"==typeof t&&(e._i=t=e._locale.preparse(t)),w(t)?new Y(ut(t)):(_(t)?e._d=t:s(n)?function(e){var t,n,a,r,s,i,o=!1,d=e._f.length;if(0===d)return f(e).invalidFormat=!0,void(e._d=new Date(NaN));for(r=0;r<d;r++)s=0,i=!1,t=L({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[r],bt(t),M(t)&&(i=!0),s+=f(t).charsLeftOver,s+=10*f(t).unusedTokens.length,f(t).score=s,o?s<a&&(a=s,n=t):(null==a||s<a||i)&&(a=s,n=t,i&&(o=!0));m(e,n||t)}(e):n?bt(e):function(e){var t=e._i;u(t)?e._d=new Date(r.now()):_(t)?e._d=new Date(t.valueOf()):"string"==typeof t?function(e){var t=ft.exec(e._i);null===t?(yt(e),!1===e._isValid&&(delete e._isValid,Lt(e),!1===e._isValid&&(delete e._isValid,e._strict?e._isValid=!1:r.createFromInputFallback(e)))):e._d=new Date(+t[1])}(e):s(t)?(e._a=c(t.slice(0),(function(e){return parseInt(e,10)})),wt(e)):i(t)?function(e){if(!e._d){var t=I(e._i),n=void 0===t.day?t.date:t.day;e._a=c([t.year,t.month,n,t.hour,t.minute,t.second,t.millisecond],(function(e){return e&&parseInt(e,10)})),wt(e)}}(e):l(t)?e._d=new Date(t):r.createFromInputFallback(e)}(e),M(e)||(e._d=null),e))}function vt(e,t,n,a,r){var o,u={};return!0!==t&&!1!==t||(a=t,t=void 0),!0!==n&&!1!==n||(a=n,n=void 0),(i(e)&&d(e)||s(e)&&0===e.length)&&(e=void 0),u._isAMomentObject=!0,u._useUTC=u._isUTC=r,u._l=n,u._i=e,u._f=t,u._strict=a,(o=new Y(ut(kt(u))))._nextDay&&(o.add(1,"d"),o._nextDay=void 0),o}function Dt(e,t,n,a){return vt(e,t,n,a,!1)}r.createFromInputFallback=k("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),r.ISO_8601=function(){},r.RFC_2822=function(){};var Tt=k("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Dt.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:p()})),St=k("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Dt.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:p()}));function jt(e,t){var n,a;if(1===t.length&&s(t[0])&&(t=t[0]),!t.length)return Dt();for(n=t[0],a=1;a<t.length;++a)t[a].isValid()&&!t[a][e](n)||(n=t[a]);return n}var xt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ht(e){var t=I(e),n=t.year||0,a=t.quarter||0,r=t.month||0,s=t.week||t.isoWeek||0,i=t.day||0,d=t.hour||0,u=t.minute||0,l=t.second||0,_=t.millisecond||0;this._isValid=function(e){var t,n,a=!1,r=xt.length;for(t in e)if(o(e,t)&&(-1===pe.call(xt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<r;++n)if(e[xt[n]]){if(a)return!1;parseFloat(e[xt[n]])!==$(e[xt[n]])&&(a=!0)}return!0}(t),this._milliseconds=+_+1e3*l+6e4*u+1e3*d*60*60,this._days=+i+7*s,this._months=+r+3*a+12*n,this._data={},this._locale=dt(),this._bubble()}function Pt(e){return e instanceof Ht}function Ot(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Ct(e,t){R(e,0,0,(function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+H(~~(e/60),2)+t+H(~~e%60,2)}))}Ct("Z",":"),Ct("ZZ",""),he("Z",ce),he("ZZ",ce),ge(["Z","ZZ"],(function(e,t,n){n._useUTC=!0,n._tzm=Rt(ce,e)}));var Et=/([\+\-]|\d\d)/gi;function Rt(e,t){var n,a,r=(t||"").match(e);return null===r?null:0===(a=60*(n=((r[r.length-1]||[])+"").match(Et)||["-",0,0])[1]+$(n[2]))?0:"+"===n[0]?a:-a}function Ft(e,t){var n,a;return t._isUTC?(n=t.clone(),a=(w(e)||_(e)?e.valueOf():Dt(e).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+a),r.updateOffset(n,!1),n):Dt(e).local()}function Wt(e){return-Math.round(e._d.getTimezoneOffset())}function At(){return!!this.isValid()&&this._isUTC&&0===this._offset}r.updateOffset=function(){};var zt=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Nt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function It(e,t){var n,a,r,s,i,d,u=e,_=null;return Pt(e)?u={ms:e._milliseconds,d:e._days,M:e._months}:l(e)||!isNaN(+e)?(u={},t?u[t]=+e:u.milliseconds=+e):(_=zt.exec(e))?(n="-"===_[1]?-1:1,u={y:0,d:$(_[2])*n,h:$(_[3])*n,m:$(_[4])*n,s:$(_[5])*n,ms:$(Ot(1e3*_[6]))*n}):(_=Nt.exec(e))?(n="-"===_[1]?-1:1,u={y:Ut(_[2],n),M:Ut(_[3],n),w:Ut(_[4],n),d:Ut(_[5],n),h:Ut(_[6],n),m:Ut(_[7],n),s:Ut(_[8],n)}):null==u?u={}:"object"==typeof u&&("from"in u||"to"in u)&&(s=Dt(u.from),i=Dt(u.to),r=s.isValid()&&i.isValid()?(i=Ft(i,s),s.isBefore(i)?d=Jt(s,i):((d=Jt(i,s)).milliseconds=-d.milliseconds,d.months=-d.months),d):{milliseconds:0,months:0},(u={}).ms=r.milliseconds,u.M=r.months),a=new Ht(u),Pt(e)&&o(e,"_locale")&&(a._locale=e._locale),Pt(e)&&o(e,"_isValid")&&(a._isValid=e._isValid),a}function Ut(e,t){var n=e&&parseFloat(e.replace(",","."));return(isNaN(n)?0:n)*t}function Jt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function Bt(e,t){return function(n,a){var r;return null===a||isNaN(+a)||(T(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),r=n,n=a,a=r),Gt(this,It(n,a),e),this}}function Gt(e,t,n,a){var s=t._milliseconds,i=Ot(t._days),o=Ot(t._months);e.isValid()&&(a=null==a||a,o&&je(e,q(e,"Month")+o*n),i&&Q(e,"Date",q(e,"Date")+i*n),s&&e._d.setTime(e._d.valueOf()+s*n),a&&r.updateOffset(e,i||o))}It.fn=Ht.prototype,It.invalid=function(){return It(NaN)};var $t=Bt(1,"add"),Vt=Bt(-1,"subtract");function qt(e){return"string"==typeof e||e instanceof String}function Qt(e){return w(e)||_(e)||qt(e)||l(e)||function(e){var t=s(e),n=!1;return t&&(n=0===e.filter((function(t){return!l(t)&&qt(e)})).length),t&&n}(e)||function(e){var t,n,a=i(e)&&!d(e),r=!1,s=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],u=s.length;for(t=0;t<u;t+=1)n=s[t],r=r||o(e,n);return a&&r}(e)||null==e}function Kt(e){var t,n=i(e)&&!d(e),a=!1,r=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(t=0;t<r.length;t+=1)a=a||o(e,r[t]);return n&&a}function Xt(e,t){if(e.date()<t.date())return-Xt(t,e);var n=12*(t.year()-e.year())+(t.month()-e.month()),a=e.clone().add(n,"months");return-(n+(t-a<0?(t-a)/(a-e.clone().add(n-1,"months")):(t-a)/(e.clone().add(n+1,"months")-a)))||0}function Zt(e){var t;return void 0===e?this._locale._abbr:(null!=(t=dt(e))&&(this._locale=t),this)}r.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",r.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var en=k("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));function tn(){return this._locale}function nn(e,t){return(e%t+t)%t}function an(e,t,n){return e<100&&e>=0?new Date(e+400,t,n)-126227808e5:new Date(e,t,n).valueOf()}function rn(e,t,n){return e<100&&e>=0?Date.UTC(e+400,t,n)-126227808e5:Date.UTC(e,t,n)}function sn(e,t){return t.erasAbbrRegex(e)}function on(){var e,t,n=[],a=[],r=[],s=[],i=this.eras();for(e=0,t=i.length;e<t;++e)a.push(Me(i[e].name)),n.push(Me(i[e].abbr)),r.push(Me(i[e].narrow)),s.push(Me(i[e].name)),s.push(Me(i[e].abbr)),s.push(Me(i[e].narrow));this._erasRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+a.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+n.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+r.join("|")+")","i")}function dn(e,t){R(0,[e,e.length],0,t)}function un(e,t,n,a,r){var s;return null==e?We(this,a,r).year:(t>(s=Ae(e,a,r))&&(t=s),ln.call(this,e,t,n,a,r))}function ln(e,t,n,a,r){var s=Fe(e,t,n,a,r),i=Ee(s.year,0,s.dayOfYear);return this.year(i.getUTCFullYear()),this.month(i.getUTCMonth()),this.date(i.getUTCDate()),this}R("N",0,0,"eraAbbr"),R("NN",0,0,"eraAbbr"),R("NNN",0,0,"eraAbbr"),R("NNNN",0,0,"eraName"),R("NNNNN",0,0,"eraNarrow"),R("y",["y",1],"yo","eraYear"),R("y",["yy",2],0,"eraYear"),R("y",["yyy",3],0,"eraYear"),R("y",["yyyy",4],0,"eraYear"),he("N",sn),he("NN",sn),he("NNN",sn),he("NNNN",(function(e,t){return t.erasNameRegex(e)})),he("NNNNN",(function(e,t){return t.erasNarrowRegex(e)})),ge(["N","NN","NNN","NNNN","NNNNN"],(function(e,t,n,a){var r=n._locale.erasParse(e,a,n._strict);r?f(n).era=r:f(n).invalidEra=e})),he("y",ue),he("yy",ue),he("yyy",ue),he("yyyy",ue),he("yo",(function(e,t){return t._eraYearOrdinalRegex||ue})),ge(["y","yy","yyy","yyyy"],0),ge(["yo"],(function(e,t,n,a){var r;n._locale._eraYearOrdinalRegex&&(r=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[0]=n._locale.eraYearOrdinalParse(e,r):t[0]=parseInt(e,10)})),R(0,["gg",2],0,(function(){return this.weekYear()%100})),R(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),dn("gggg","weekYear"),dn("ggggg","weekYear"),dn("GGGG","isoWeekYear"),dn("GGGGG","isoWeekYear"),z("weekYear","gg"),z("isoWeekYear","GG"),J("weekYear",1),J("isoWeekYear",1),he("G",le),he("g",le),he("GG",ae,Z),he("gg",ae,Z),he("GGGG",oe,te),he("gggg",oe,te),he("GGGGG",de,ne),he("ggggg",de,ne),Le(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,n,a){t[a.substr(0,2)]=$(e)})),Le(["gg","GG"],(function(e,t,n,a){t[a]=r.parseTwoDigitYear(e)})),R("Q",0,"Qo","quarter"),z("quarter","Q"),J("quarter",7),he("Q",X),ge("Q",(function(e,t){t[1]=3*($(e)-1)})),R("D",["DD",2],"Do","date"),z("date","D"),J("date",9),he("D",ae),he("DD",ae,Z),he("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),ge(["D","DD"],2),ge("Do",(function(e,t){t[2]=$(e.match(ae)[0])}));var _n=V("Date",!0);R("DDD",["DDDD",3],"DDDo","dayOfYear"),z("dayOfYear","DDD"),J("dayOfYear",4),he("DDD",ie),he("DDDD",ee),ge(["DDD","DDDD"],(function(e,t,n){n._dayOfYear=$(e)})),R("m",["mm",2],0,"minute"),z("minute","m"),J("minute",14),he("m",ae),he("mm",ae,Z),ge(["m","mm"],4);var cn=V("Minutes",!1);R("s",["ss",2],0,"second"),z("second","s"),J("second",15),he("s",ae),he("ss",ae,Z),ge(["s","ss"],5);var mn,hn,fn=V("Seconds",!1);for(R("S",0,0,(function(){return~~(this.millisecond()/100)})),R(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),R(0,["SSS",3],0,"millisecond"),R(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),R(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),R(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),R(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),R(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),R(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),z("millisecond","ms"),J("millisecond",16),he("S",ie,X),he("SS",ie,Z),he("SSS",ie,ee),mn="SSSS";mn.length<=9;mn+="S")he(mn,ue);function Mn(e,t){t[6]=$(1e3*("0."+e))}for(mn="S";mn.length<=9;mn+="S")ge(mn,Mn);hn=V("Milliseconds",!1),R("z",0,0,"zoneAbbr"),R("zz",0,0,"zoneName");var pn=Y.prototype;function yn(e){return e}pn.add=$t,pn.calendar=function(e,t){1===arguments.length&&(arguments[0]?Qt(arguments[0])?(e=arguments[0],t=void 0):Kt(arguments[0])&&(t=arguments[0],e=void 0):(e=void 0,t=void 0));var n=e||Dt(),a=Ft(n,this).startOf("day"),s=r.calendarFormat(this,a)||"sameElse",i=t&&(S(t[s])?t[s].call(this,n):t[s]);return this.format(i||this.localeData().calendar(s,this,Dt(n)))},pn.clone=function(){return new Y(this)},pn.diff=function(e,t,n){var a,r,s;if(!this.isValid())return NaN;if(!(a=Ft(e,this)).isValid())return NaN;switch(r=6e4*(a.utcOffset()-this.utcOffset()),t=N(t)){case"year":s=Xt(this,a)/12;break;case"month":s=Xt(this,a);break;case"quarter":s=Xt(this,a)/3;break;case"second":s=(this-a)/1e3;break;case"minute":s=(this-a)/6e4;break;case"hour":s=(this-a)/36e5;break;case"day":s=(this-a-r)/864e5;break;case"week":s=(this-a-r)/6048e5;break;default:s=this-a}return n?s:G(s)},pn.endOf=function(e){var t,n;if(void 0===(e=N(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?rn:an,e){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-nn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-nn(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-nn(t,1e3)-1}return this._d.setTime(t),r.updateOffset(this,!0),this},pn.format=function(e){e||(e=this.isUtc()?r.defaultFormatUtc:r.defaultFormat);var t=F(this,e);return this.localeData().postformat(t)},pn.from=function(e,t){return this.isValid()&&(w(e)&&e.isValid()||Dt(e).isValid())?It({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},pn.fromNow=function(e){return this.from(Dt(),e)},pn.to=function(e,t){return this.isValid()&&(w(e)&&e.isValid()||Dt(e).isValid())?It({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},pn.toNow=function(e){return this.to(Dt(),e)},pn.get=function(e){return S(this[e=N(e)])?this[e]():this},pn.invalidAt=function(){return f(this).overflow},pn.isAfter=function(e,t){var n=w(e)?e:Dt(e);return!(!this.isValid()||!n.isValid())&&("millisecond"===(t=N(t)||"millisecond")?this.valueOf()>n.valueOf():n.valueOf()<this.clone().startOf(t).valueOf())},pn.isBefore=function(e,t){var n=w(e)?e:Dt(e);return!(!this.isValid()||!n.isValid())&&("millisecond"===(t=N(t)||"millisecond")?this.valueOf()<n.valueOf():this.clone().endOf(t).valueOf()<n.valueOf())},pn.isBetween=function(e,t,n,a){var r=w(e)?e:Dt(e),s=w(t)?t:Dt(t);return!!(this.isValid()&&r.isValid()&&s.isValid())&&(("("===(a=a||"()")[0]?this.isAfter(r,n):!this.isBefore(r,n))&&(")"===a[1]?this.isBefore(s,n):!this.isAfter(s,n)))},pn.isSame=function(e,t){var n,a=w(e)?e:Dt(e);return!(!this.isValid()||!a.isValid())&&("millisecond"===(t=N(t)||"millisecond")?this.valueOf()===a.valueOf():(n=a.valueOf(),this.clone().startOf(t).valueOf()<=n&&n<=this.clone().endOf(t).valueOf()))},pn.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},pn.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},pn.isValid=function(){return M(this)},pn.lang=en,pn.locale=Zt,pn.localeData=tn,pn.max=St,pn.min=Tt,pn.parsingFlags=function(){return m({},f(this))},pn.set=function(e,t){if("object"==typeof e){var n,a=function(e){var t,n=[];for(t in e)o(e,t)&&n.push({unit:t,priority:U[t]});return n.sort((function(e,t){return e.priority-t.priority})),n}(e=I(e)),r=a.length;for(n=0;n<r;n++)this[a[n].unit](e[a[n].unit])}else if(S(this[e=N(e)]))return this[e](t);return this},pn.startOf=function(e){var t,n;if(void 0===(e=N(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?rn:an,e){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=nn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=nn(t,6e4);break;case"second":t=this._d.valueOf(),t-=nn(t,1e3)}return this._d.setTime(t),r.updateOffset(this,!0),this},pn.subtract=Vt,pn.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},pn.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},pn.toDate=function(){return new Date(this.valueOf())},pn.toISOString=function(e){if(!this.isValid())return null;var t=!0!==e,n=t?this.clone().utc():this;return n.year()<0||n.year()>9999?F(n,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):S(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",F(n,"Z")):F(n,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},pn.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e,t,n,a="moment",r="";return this.isLocal()||(a=0===this.utcOffset()?"moment.utc":"moment.parseZone",r="Z"),e="["+a+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",n=r+'[")]',this.format(e+t+"-MM-DD[T]HH:mm:ss.SSS"+n)},"undefined"!=typeof Symbol&&null!=Symbol.for&&(pn[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),pn.toJSON=function(){return this.isValid()?this.toISOString():null},pn.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},pn.unix=function(){return Math.floor(this.valueOf()/1e3)},pn.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},pn.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},pn.eraName=function(){var e,t,n,a=this.localeData().eras();for(e=0,t=a.length;e<t;++e){if(n=this.clone().startOf("day").valueOf(),a[e].since<=n&&n<=a[e].until)return a[e].name;if(a[e].until<=n&&n<=a[e].since)return a[e].name}return""},pn.eraNarrow=function(){var e,t,n,a=this.localeData().eras();for(e=0,t=a.length;e<t;++e){if(n=this.clone().startOf("day").valueOf(),a[e].since<=n&&n<=a[e].until)return a[e].narrow;if(a[e].until<=n&&n<=a[e].since)return a[e].narrow}return""},pn.eraAbbr=function(){var e,t,n,a=this.localeData().eras();for(e=0,t=a.length;e<t;++e){if(n=this.clone().startOf("day").valueOf(),a[e].since<=n&&n<=a[e].until)return a[e].abbr;if(a[e].until<=n&&n<=a[e].since)return a[e].abbr}return""},pn.eraYear=function(){var e,t,n,a,s=this.localeData().eras();for(e=0,t=s.length;e<t;++e)if(n=s[e].since<=s[e].until?1:-1,a=this.clone().startOf("day").valueOf(),s[e].since<=a&&a<=s[e].until||s[e].until<=a&&a<=s[e].since)return(this.year()-r(s[e].since).year())*n+s[e].offset;return this.year()},pn.year=Oe,pn.isLeapYear=function(){return B(this.year())},pn.weekYear=function(e){return un.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},pn.isoWeekYear=function(e){return un.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},pn.quarter=pn.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},pn.month=xe,pn.daysInMonth=function(){return we(this.year(),this.month())},pn.week=pn.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},pn.isoWeek=pn.isoWeeks=function(e){var t=We(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},pn.weeksInYear=function(){var e=this.localeData()._week;return Ae(this.year(),e.dow,e.doy)},pn.weeksInWeekYear=function(){var e=this.localeData()._week;return Ae(this.weekYear(),e.dow,e.doy)},pn.isoWeeksInYear=function(){return Ae(this.year(),1,4)},pn.isoWeeksInISOWeekYear=function(){return Ae(this.isoWeekYear(),1,4)},pn.date=_n,pn.day=pn.days=function(e){if(!this.isValid())return null!=e?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=function(e,t){return"string"!=typeof e?e:isNaN(e)?"number"==typeof(e=t.weekdaysParse(e))?e:null:parseInt(e,10)}(e,this.localeData()),this.add(e-t,"d")):t},pn.weekday=function(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},pn.isoWeekday=function(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=function(e,t){return"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7},pn.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},pn.hour=pn.hours=Ze,pn.minute=pn.minutes=cn,pn.second=pn.seconds=fn,pn.millisecond=pn.milliseconds=hn,pn.utcOffset=function(e,t,n){var a,s=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(null===(e=Rt(ce,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&t&&(a=Wt(this)),this._offset=e,this._isUTC=!0,null!=a&&this.add(a,"m"),s!==e&&(!t||this._changeInProgress?Gt(this,It(e-s,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,r.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?s:Wt(this)},pn.utc=function(e){return this.utcOffset(0,e)},pn.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Wt(this),"m")),this},pn.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var e=Rt(_e,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this},pn.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Dt(e).utcOffset():0,(this.utcOffset()-e)%60==0)},pn.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},pn.isLocal=function(){return!!this.isValid()&&!this._isUTC},pn.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},pn.isUtc=At,pn.isUTC=At,pn.zoneAbbr=function(){return this._isUTC?"UTC":""},pn.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},pn.dates=k("dates accessor is deprecated. Use date instead.",_n),pn.months=k("months accessor is deprecated. Use month instead",xe),pn.years=k("years accessor is deprecated. Use year instead",Oe),pn.zone=k("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(e,t){return null!=e?("string"!=typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()})),pn.isDSTShifted=k("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){if(!u(this._isDSTShifted))return this._isDSTShifted;var e,t={};return L(t,this),(t=kt(t))._a?(e=t._isUTC?h(t._a):Dt(t._a),this._isDSTShifted=this.isValid()&&function(e,t,n){var a,r=Math.min(e.length,t.length),s=Math.abs(e.length-t.length),i=0;for(a=0;a<r;a++)(n&&e[a]!==t[a]||!n&&$(e[a])!==$(t[a]))&&i++;return i+s}(t._a,e.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}));var gn=x.prototype;function Ln(e,t,n,a){var r=dt(),s=h().set(a,t);return r[n](s,e)}function Yn(e,t,n){if(l(e)&&(t=e,e=void 0),e=e||"",null!=t)return Ln(e,t,n,"month");var a,r=[];for(a=0;a<12;a++)r[a]=Ln(e,a,n,"month");return r}function wn(e,t,n,a){"boolean"==typeof e?(l(t)&&(n=t,t=void 0),t=t||""):(n=t=e,e=!1,l(t)&&(n=t,t=void 0),t=t||"");var r,s=dt(),i=e?s._week.dow:0,o=[];if(null!=n)return Ln(t,(n+i)%7,a,"day");for(r=0;r<7;r++)o[r]=Ln(t,(r+i)%7,a,"day");return o}gn.calendar=function(e,t,n){var a=this._calendar[e]||this._calendar.sameElse;return S(a)?a.call(t,n):a},gn.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(P).map((function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e})).join(""),this._longDateFormat[e])},gn.invalidDate=function(){return this._invalidDate},gn.ordinal=function(e){return this._ordinal.replace("%d",e)},gn.preparse=yn,gn.postformat=yn,gn.relativeTime=function(e,t,n,a){var r=this._relativeTime[n];return S(r)?r(e,t,n,a):r.replace(/%d/i,e)},gn.pastFuture=function(e,t){var n=this._relativeTime[e>0?"future":"past"];return S(n)?n(t):n.replace(/%s/i,t)},gn.set=function(e){var t,n;for(n in e)o(e,n)&&(S(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},gn.eras=function(e,t){var n,a,s,i=this._eras||dt("en")._eras;for(n=0,a=i.length;n<a;++n){switch(typeof i[n].since){case"string":s=r(i[n].since).startOf("day"),i[n].since=s.valueOf()}switch(typeof i[n].until){case"undefined":i[n].until=1/0;break;case"string":s=r(i[n].until).startOf("day").valueOf(),i[n].until=s.valueOf()}}return i},gn.erasParse=function(e,t,n){var a,r,s,i,o,d=this.eras();for(e=e.toUpperCase(),a=0,r=d.length;a<r;++a)if(s=d[a].name.toUpperCase(),i=d[a].abbr.toUpperCase(),o=d[a].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(i===e)return d[a];break;case"NNNN":if(s===e)return d[a];break;case"NNNNN":if(o===e)return d[a]}else if([s,i,o].indexOf(e)>=0)return d[a]},gn.erasConvertYear=function(e,t){var n=e.since<=e.until?1:-1;return void 0===t?r(e.since).year():r(e.since).year()+(t-e.offset)*n},gn.erasAbbrRegex=function(e){return o(this,"_erasAbbrRegex")||on.call(this),e?this._erasAbbrRegex:this._erasRegex},gn.erasNameRegex=function(e){return o(this,"_erasNameRegex")||on.call(this),e?this._erasNameRegex:this._erasRegex},gn.erasNarrowRegex=function(e){return o(this,"_erasNarrowRegex")||on.call(this),e?this._erasNarrowRegex:this._erasRegex},gn.months=function(e,t){return e?s(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||ve).test(t)?"format":"standalone"][e.month()]:s(this._months)?this._months:this._months.standalone},gn.monthsShort=function(e,t){return e?s(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[ve.test(t)?"format":"standalone"][e.month()]:s(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},gn.monthsParse=function(e,t,n){var a,r,s;if(this._monthsParseExact)return Se.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),a=0;a<12;a++){if(r=h([2e3,a]),n&&!this._longMonthsParse[a]&&(this._longMonthsParse[a]=new RegExp("^"+this.months(r,"").replace(".","")+"$","i"),this._shortMonthsParse[a]=new RegExp("^"+this.monthsShort(r,"").replace(".","")+"$","i")),n||this._monthsParse[a]||(s="^"+this.months(r,"")+"|^"+this.monthsShort(r,""),this._monthsParse[a]=new RegExp(s.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[a].test(e))return a;if(n&&"MMM"===t&&this._shortMonthsParse[a].test(e))return a;if(!n&&this._monthsParse[a].test(e))return a}},gn.monthsRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||He.call(this),e?this._monthsStrictRegex:this._monthsRegex):(o(this,"_monthsRegex")||(this._monthsRegex=Te),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},gn.monthsShortRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||He.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(o(this,"_monthsShortRegex")||(this._monthsShortRegex=De),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},gn.week=function(e){return We(e,this._week.dow,this._week.doy).week},gn.firstDayOfYear=function(){return this._week.doy},gn.firstDayOfWeek=function(){return this._week.dow},gn.weekdays=function(e,t){var n=s(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"];return!0===e?ze(n,this._week.dow):e?n[e.day()]:n},gn.weekdaysMin=function(e){return!0===e?ze(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},gn.weekdaysShort=function(e){return!0===e?ze(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},gn.weekdaysParse=function(e,t,n){var a,r,s;if(this._weekdaysParseExact)return $e.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),a=0;a<7;a++){if(r=h([2e3,1]).day(a),n&&!this._fullWeekdaysParse[a]&&(this._fullWeekdaysParse[a]=new RegExp("^"+this.weekdays(r,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[a]=new RegExp("^"+this.weekdaysShort(r,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[a]=new RegExp("^"+this.weekdaysMin(r,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[a]||(s="^"+this.weekdays(r,"")+"|^"+this.weekdaysShort(r,"")+"|^"+this.weekdaysMin(r,""),this._weekdaysParse[a]=new RegExp(s.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[a].test(e))return a;if(n&&"ddd"===t&&this._shortWeekdaysParse[a].test(e))return a;if(n&&"dd"===t&&this._minWeekdaysParse[a].test(e))return a;if(!n&&this._weekdaysParse[a].test(e))return a}},gn.weekdaysRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Ve.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(o(this,"_weekdaysRegex")||(this._weekdaysRegex=Je),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},gn.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Ve.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(o(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Be),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},gn.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||Ve.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(o(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Ge),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},gn.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},gn.meridiem=function(e,t,n){return e>11?n?"pm":"PM":n?"am":"AM"},it("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===$(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}}),r.lang=k("moment.lang is deprecated. Use moment.locale instead.",it),r.langData=k("moment.langData is deprecated. Use moment.localeData instead.",dt);var bn=Math.abs;function kn(e,t,n,a){var r=It(t,n);return e._milliseconds+=a*r._milliseconds,e._days+=a*r._days,e._months+=a*r._months,e._bubble()}function vn(e){return e<0?Math.floor(e):Math.ceil(e)}function Dn(e){return 4800*e/146097}function Tn(e){return 146097*e/4800}function Sn(e){return function(){return this.as(e)}}var jn=Sn("ms"),xn=Sn("s"),Hn=Sn("m"),Pn=Sn("h"),On=Sn("d"),Cn=Sn("w"),En=Sn("M"),Rn=Sn("Q"),Fn=Sn("y");function Wn(e){return function(){return this.isValid()?this._data[e]:NaN}}var An=Wn("milliseconds"),zn=Wn("seconds"),Nn=Wn("minutes"),In=Wn("hours"),Un=Wn("days"),Jn=Wn("months"),Bn=Wn("years"),Gn=Math.round,$n={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function Vn(e,t,n,a,r){return r.relativeTime(t||1,!!n,e,a)}var qn=Math.abs;function Qn(e){return(e>0)-(e<0)||+e}function Kn(){if(!this.isValid())return this.localeData().invalidDate();var e,t,n,a,r,s,i,o,d=qn(this._milliseconds)/1e3,u=qn(this._days),l=qn(this._months),_=this.asSeconds();return _?(e=G(d/60),t=G(e/60),d%=60,e%=60,n=G(l/12),l%=12,a=d?d.toFixed(3).replace(/\.?0+$/,""):"",r=_<0?"-":"",s=Qn(this._months)!==Qn(_)?"-":"",i=Qn(this._days)!==Qn(_)?"-":"",o=Qn(this._milliseconds)!==Qn(_)?"-":"",r+"P"+(n?s+n+"Y":"")+(l?s+l+"M":"")+(u?i+u+"D":"")+(t||e||d?"T":"")+(t?o+t+"H":"")+(e?o+e+"M":"")+(d?o+a+"S":"")):"P0D"}var Xn=Ht.prototype;return Xn.isValid=function(){return this._isValid},Xn.abs=function(){var e=this._data;return this._milliseconds=bn(this._milliseconds),this._days=bn(this._days),this._months=bn(this._months),e.milliseconds=bn(e.milliseconds),e.seconds=bn(e.seconds),e.minutes=bn(e.minutes),e.hours=bn(e.hours),e.months=bn(e.months),e.years=bn(e.years),this},Xn.add=function(e,t){return kn(this,e,t,1)},Xn.subtract=function(e,t){return kn(this,e,t,-1)},Xn.as=function(e){if(!this.isValid())return NaN;var t,n,a=this._milliseconds;if("month"===(e=N(e))||"quarter"===e||"year"===e)switch(t=this._days+a/864e5,n=this._months+Dn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(Tn(this._months)),e){case"week":return t/7+a/6048e5;case"day":return t+a/864e5;case"hour":return 24*t+a/36e5;case"minute":return 1440*t+a/6e4;case"second":return 86400*t+a/1e3;case"millisecond":return Math.floor(864e5*t)+a;default:throw new Error("Unknown unit "+e)}},Xn.asMilliseconds=jn,Xn.asSeconds=xn,Xn.asMinutes=Hn,Xn.asHours=Pn,Xn.asDays=On,Xn.asWeeks=Cn,Xn.asMonths=En,Xn.asQuarters=Rn,Xn.asYears=Fn,Xn.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*$(this._months/12):NaN},Xn._bubble=function(){var e,t,n,a,r,s=this._milliseconds,i=this._days,o=this._months,d=this._data;return s>=0&&i>=0&&o>=0||s<=0&&i<=0&&o<=0||(s+=864e5*vn(Tn(o)+i),i=0,o=0),d.milliseconds=s%1e3,e=G(s/1e3),d.seconds=e%60,t=G(e/60),d.minutes=t%60,n=G(t/60),d.hours=n%24,i+=G(n/24),r=G(Dn(i)),o+=r,i-=vn(Tn(r)),a=G(o/12),o%=12,d.days=i,d.months=o,d.years=a,this},Xn.clone=function(){return It(this)},Xn.get=function(e){return e=N(e),this.isValid()?this[e+"s"]():NaN},Xn.milliseconds=An,Xn.seconds=zn,Xn.minutes=Nn,Xn.hours=In,Xn.days=Un,Xn.weeks=function(){return G(this.days()/7)},Xn.months=Jn,Xn.years=Bn,Xn.humanize=function(e,t){if(!this.isValid())return this.localeData().invalidDate();var n,a,r=!1,s=$n;return"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(r=e),"object"==typeof t&&(s=Object.assign({},$n,t),null!=t.s&&null==t.ss&&(s.ss=t.s-1)),n=this.localeData(),a=function(e,t,n,a){var r=It(e).abs(),s=Gn(r.as("s")),i=Gn(r.as("m")),o=Gn(r.as("h")),d=Gn(r.as("d")),u=Gn(r.as("M")),l=Gn(r.as("w")),_=Gn(r.as("y")),c=s<=n.ss&&["s",s]||s<n.s&&["ss",s]||i<=1&&["m"]||i<n.m&&["mm",i]||o<=1&&["h"]||o<n.h&&["hh",o]||d<=1&&["d"]||d<n.d&&["dd",d];return null!=n.w&&(c=c||l<=1&&["w"]||l<n.w&&["ww",l]),(c=c||u<=1&&["M"]||u<n.M&&["MM",u]||_<=1&&["y"]||["yy",_])[2]=t,c[3]=+e>0,c[4]=a,Vn.apply(null,c)}(this,!r,s,n),r&&(a=n.pastFuture(+this,a)),n.postformat(a)},Xn.toISOString=Kn,Xn.toString=Kn,Xn.toJSON=Kn,Xn.locale=Zt,Xn.localeData=tn,Xn.toIsoString=k("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Kn),Xn.lang=en,R("X",0,0,"unix"),R("x",0,0,"valueOf"),he("x",le),he("X",/[+-]?\d+(\.\d{1,3})?/),ge("X",(function(e,t,n){n._d=new Date(1e3*parseFloat(e))})),ge("x",(function(e,t,n){n._d=new Date($(e))})),r.version="2.29.3",t=Dt,r.fn=pn,r.min=function(){var e=[].slice.call(arguments,0);return jt("isBefore",e)},r.max=function(){var e=[].slice.call(arguments,0);return jt("isAfter",e)},r.now=function(){return Date.now?Date.now():+new Date},r.utc=h,r.unix=function(e){return Dt(1e3*e)},r.months=function(e,t){return Yn(e,t,"months")},r.isDate=_,r.locale=it,r.invalid=p,r.duration=It,r.isMoment=w,r.weekdays=function(e,t,n){return wn(e,t,n,"weekdays")},r.parseZone=function(){return Dt.apply(null,arguments).parseZone()},r.localeData=dt,r.isDuration=Pt,r.monthsShort=function(e,t){return Yn(e,t,"monthsShort")},r.weekdaysMin=function(e,t,n){return wn(e,t,n,"weekdaysMin")},r.defineLocale=ot,r.updateLocale=function(e,t){if(null!=t){var n,a,r=et;null!=tt[e]&&null!=tt[e].parentLocale?tt[e].set(j(tt[e]._config,t)):(null!=(a=st(e))&&(r=a._config),t=j(r,t),null==a&&(t.abbr=e),(n=new x(t)).parentLocale=tt[e],tt[e]=n),it(e)}else null!=tt[e]&&(null!=tt[e].parentLocale?(tt[e]=tt[e].parentLocale,e===it()&&it(e)):null!=tt[e]&&delete tt[e]);return tt[e]},r.locales=function(){return v(tt)},r.weekdaysShort=function(e,t,n){return wn(e,t,n,"weekdaysShort")},r.normalizeUnits=N,r.relativeTimeRounding=function(e){return void 0===e?Gn:"function"==typeof e&&(Gn=e,!0)},r.relativeTimeThreshold=function(e,t){return void 0!==$n[e]&&(void 0===t?$n[e]:($n[e]=t,"s"===e&&($n.ss=t-1),!0))},r.calendarFormat=function(e,t){var n=e.diff(t,"days",!0);return n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse"},r.prototype=pn,r.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},r}()}).call(this,n("YuTi")(e))},x6pH:function(e,t,n){!function(e){"use strict";e.defineLocale("he",{months:"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"),monthsShort:"ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳".split("_"),weekdays:"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"),weekdaysShort:"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"),weekdaysMin:"א_ב_ג_ד_ה_ו_ש".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [ב]MMMM YYYY",LLL:"D [ב]MMMM YYYY HH:mm",LLLL:"dddd, D [ב]MMMM YYYY HH:mm",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[היום ב־]LT",nextDay:"[מחר ב־]LT",nextWeek:"dddd [בשעה] LT",lastDay:"[אתמול ב־]LT",lastWeek:"[ביום] dddd [האחרון בשעה] LT",sameElse:"L"},relativeTime:{future:"בעוד %s",past:"לפני %s",s:"מספר שניות",ss:"%d שניות",m:"דקה",mm:"%d דקות",h:"שעה",hh:function(e){return 2===e?"שעתיים":e+" שעות"},d:"יום",dd:function(e){return 2===e?"יומיים":e+" ימים"},M:"חודש",MM:function(e){return 2===e?"חודשיים":e+" חודשים"},y:"שנה",yy:function(e){return 2===e?"שנתיים":e%10==0&&10!==e?e+" שנה":e+" שנים"}},meridiemParse:/אחה"צ|לפנה"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,isPM:function(e){return/^(אחה"צ|אחרי הצהריים|בערב)$/.test(e)},meridiem:function(e,t,n){return e<5?"לפנות בוקר":e<10?"בבוקר":e<12?n?'לפנה"צ':"לפני הצהריים":e<18?n?'אחה"צ':"אחרי הצהריים":"בערב"}})}(n("wd/R"))},yPMs:function(e,t,n){!function(e){"use strict";e.defineLocale("sq",{months:"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor".split("_"),monthsShort:"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj".split("_"),weekdays:"E Diel_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë".split("_"),weekdaysShort:"Die_Hën_Mar_Mër_Enj_Pre_Sht".split("_"),weekdaysMin:"D_H_Ma_Më_E_P_Sh".split("_"),weekdaysParseExact:!0,meridiemParse:/PD|MD/,isPM:function(e){return"M"===e.charAt(0)},meridiem:function(e,t,n){return e<12?"PD":"MD"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Sot në] LT",nextDay:"[Nesër në] LT",nextWeek:"dddd [në] LT",lastDay:"[Dje në] LT",lastWeek:"dddd [e kaluar në] LT",sameElse:"L"},relativeTime:{future:"në %s",past:"%s më parë",s:"disa sekonda",ss:"%d sekonda",m:"një minutë",mm:"%d minuta",h:"një orë",hh:"%d orë",d:"një ditë",dd:"%d ditë",M:"një muaj",MM:"%d muaj",y:"një vit",yy:"%d vite"},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},z1FC:function(e,t,n){!function(e){"use strict";function t(e,t,n,a){var r={s:["viensas secunds","'iensas secunds"],ss:[e+" secunds",e+" secunds"],m:["'n míut","'iens míut"],mm:[e+" míuts",e+" míuts"],h:["'n þora","'iensa þora"],hh:[e+" þoras",e+" þoras"],d:["'n ziua","'iensa ziua"],dd:[e+" ziuas",e+" ziuas"],M:["'n mes","'iens mes"],MM:[e+" mesen",e+" mesen"],y:["'n ar","'iens ar"],yy:[e+" ars",e+" ars"]};return a||t?r[n][0]:r[n][1]}e.defineLocale("tzl",{months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdays:"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi".split("_"),weekdaysShort:"Súl_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"},meridiemParse:/d\'o|d\'a/i,isPM:function(e){return"d'o"===e.toLowerCase()},meridiem:function(e,t,n){return e>11?n?"d'o":"D'O":n?"d'a":"D'A"},calendar:{sameDay:"[oxhi à] LT",nextDay:"[demà à] LT",nextWeek:"dddd [à] LT",lastDay:"[ieiri à] LT",lastWeek:"[sür el] dddd [lasteu à] LT",sameElse:"L"},relativeTime:{future:"osprei %s",past:"ja%s",s:t,ss:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},z3Vd:function(e,t,n){!function(e){"use strict";var t="pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut".split("_");function n(e,n,a,r){var s=function(e){var n=Math.floor(e%1e3/100),a=Math.floor(e%100/10),r=e%10,s="";return n>0&&(s+=t[n]+"vatlh"),a>0&&(s+=(""!==s?" ":"")+t[a]+"maH"),r>0&&(s+=(""!==s?" ":"")+t[r]),""===s?"pagh":s}(e);switch(a){case"ss":return s+" lup";case"mm":return s+" tup";case"hh":return s+" rep";case"dd":return s+" jaj";case"MM":return s+" jar";case"yy":return s+" DIS"}}e.defineLocale("tlh",{months:"tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’".split("_"),monthsShort:"jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’".split("_"),monthsParseExact:!0,weekdays:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysShort:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysMin:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[DaHjaj] LT",nextDay:"[wa’leS] LT",nextWeek:"LLL",lastDay:"[wa’Hu’] LT",lastWeek:"LLL",sameElse:"L"},relativeTime:{future:function(e){var t=e;return t=-1!==e.indexOf("jaj")?t.slice(0,-3)+"leS":-1!==e.indexOf("jar")?t.slice(0,-3)+"waQ":-1!==e.indexOf("DIS")?t.slice(0,-3)+"nem":t+" pIq"},past:function(e){var t=e;return t=-1!==e.indexOf("jaj")?t.slice(0,-3)+"Hu’":-1!==e.indexOf("jar")?t.slice(0,-3)+"wen":-1!==e.indexOf("DIS")?t.slice(0,-3)+"ben":t+" ret"},s:"puS lup",ss:n,m:"wa’ tup",mm:n,h:"wa’ rep",hh:n,d:"wa’ jaj",dd:n,M:"wa’ jar",MM:n,y:"wa’ DIS",yy:n},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(n("wd/R"))},zx6S:function(e,t,n){!function(e){"use strict";var t={words:{ss:["sekunda","sekunde","sekundi"],m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],d:["jedan dan","jednog dana"],dd:["dan","dana","dana"],M:["jedan mesec","jednog meseca"],MM:["mesec","meseca","meseci"],y:["jednu godinu","jedne godine"],yy:["godinu","godine","godina"]},correctGrammaticalCase:function(e,t){return e%10>=1&&e%10<=4&&(e%100<10||e%100>=20)?e%10==1?t[0]:t[1]:t[2]},translate:function(e,n,a,r){var s,i=t.words[a];return 1===a.length?"y"===a&&n?"jedna godina":r||n?i[0]:i[1]:(s=t.correctGrammaticalCase(e,i),"yy"===a&&n&&"godinu"===s?e+" godina":e+" "+s)}};e.defineLocale("sr",{months:"januar_februar_mart_april_maj_jun_jul_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj_jun_jul_avg._sep._okt._nov._dec.".split("_"),monthsParseExact:!0,weekdays:"nedelja_ponedeljak_utorak_sreda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sre._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),weekdaysParseExact:!0,longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D. M. YYYY.",LL:"D. MMMM YYYY.",LLL:"D. MMMM YYYY. H:mm",LLLL:"dddd, D. MMMM YYYY. H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedelju] [u] LT";case 3:return"[u] [sredu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedelje] [u] LT","[prošlog] [ponedeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"pre %s",s:"nekoliko sekundi",ss:t.translate,m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:t.translate,dd:t.translate,M:t.translate,MM:t.translate,y:t.translate,yy:t.translate},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}})}(n("wd/R"))}}]);