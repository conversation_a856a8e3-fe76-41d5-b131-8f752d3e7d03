RewriteEngine On

# إعادة توجيه API routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/auth/login/?$ api/auth/login.php [L]
RewriteRule ^api/auth/signup/?$ api/auth/signup.php [L]
RewriteRule ^api/auth/social/([^/]+)/?$ api/auth/social.php?service=$1 [L]

RewriteRule ^api/admin/login/?$ api/admin/login.php [L]

RewriteRule ^api/outlooks/?$ api/outlooks/get.php [L]
RewriteRule ^api/outlooks/([^/]+)/?$ api/outlooks/get_by_country.php?country=$1 [L]

RewriteRule ^api/weather-shots/?$ api/weather-shots/get.php [L]

RewriteRule ^api/countries/?$ api/countries/get.php [L]

RewriteRule ^api/notifications/?$ api/notifications/get.php [L]

RewriteRule ^api/coupons/?$ api/coupons/get.php [L]
RewriteRule ^api/coupon/([^/]+)/?$ api/coupons/get_by_id.php?id=$1 [L]

RewriteRule ^api/usersCount/?$ api/stats/counts.php?type=users [L]
RewriteRule ^api/subsCount/?$ api/stats/counts.php?type=subs [L]
RewriteRule ^api/postsCount/?$ api/stats/counts.php?type=posts [L]
RewriteRule ^api/weatherShotsCount/?$ api/stats/counts.php?type=weatherShots [L]
RewriteRule ^api/activeCount/?$ api/stats/counts.php?type=active [L]
RewriteRule ^api/most-likes-posts/?$ api/stats/most_liked_posts.php [L]

# إعدادات الأمان
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "config/*">
    Order Deny,Allow
    Deny from all
</Files>

<Files "includes/*">
    Order Deny,Allow
    Deny from all
</Files>

<Files "logs/*">
    Order Deny,Allow
    Deny from all
</Files>

# إعدادات الملفات المرفوعة
<Directory "uploads/">
    Options -Indexes
    <Files "*.php">
        Order Deny,Allow
        Deny from all
    </Files>
</Directory>

# إعدادات الضغط
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType video/mp4 "access plus 1 month"
</IfModule>

# إعدادات الأمان الإضافية
ServerSignature Off
Options -Indexes

# منع الوصول للملفات الحساسة
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

# إعدادات CORS للـ API
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    Header always set Access-Control-Max-Age "3600"
</IfModule>

# معالجة طلبات OPTIONS
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
