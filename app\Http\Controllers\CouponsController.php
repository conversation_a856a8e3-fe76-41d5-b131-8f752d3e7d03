<?php

namespace App\Http\Controllers;

use App\Models\Coupons;
use App\Models\UsedCoupons;
use Illuminate\Http\Request;

class CouponsController extends Controller
{
    public function get()
    {
        return Coupons::orderBy('id', 'DESC')->with('usedBy')->get();
    }
    public function getByID($id)
    {
        return Coupons::where('id', $id)->first();
    }
    public function edit(Request $request)
    {
        return Coupons::where('id', $request->id)->update([
            'coupon' => $request->coupon,
            'country' => $request->country,
            'expire_date' => $request->expire_date,
        ]);
    }
    public function add(Request $request)
    {
        $countries = $request->get("country");

        if(is_array($countries) && count($countries) > 0)
        {
            foreach($countries as $country)
            {
                Coupons::create([
                    'coupon' => $request->coupon,
                    'country' => $country,
                    'days' => $request->days,
                    'expire_date' => $request->expire_date,
                    'active' => 1,
                ]);
            }

            return true;
        }else{
            abort(500, "Countries needs to be type of an array");
        }

    }
    public function disable(Request $request)
    {
        Coupons::where('id', $request->id)->update([
            'active' => 0,
        ]);
    }
    public function multiDisable(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Coupons::where('id', $id)->update([
                'active' => 0,
            ]);
        }

        return true;
    }
    public function enable(Request $request)
    {
        Coupons::where('id', $request->id)->update([
            'active' => 1,
        ]);
    }
    public function multiEnable(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Coupons::where('id', $id)->update([
                'active' => 1,
            ]);
        }

        return true;
    }
    public function delete(Request $request)
    {
        Coupons::where('id', $request->id)->delete();
        UsedCoupons::where('c_id', $request->id)->delete();
    }
    public function multiDelete(Request $request)
    {
        $data = $request->validate([
           'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Coupons::where('id', $id)->delete();
            UsedCoupons::where('c_id', $id)->delete();
        }

        return true;
    }
}
