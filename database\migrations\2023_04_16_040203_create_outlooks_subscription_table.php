<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOutlooksSubscriptionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('outlooks_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id');
            $table->boolean('is_registered_user')->default(false);
            $table->text('country')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });

        Schema::table('system_settings', function(Blueprint $table) {

            $table->boolean("subscription_mode")->default(false);
            $table->integer('subscription_length')->default(2);

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('outlooks_subscriptions');
        Schema::dropIfExists("system_settings.subscription_mode");
        Schema::dropIfExists("system_settings.subscription_length");
    }
}
