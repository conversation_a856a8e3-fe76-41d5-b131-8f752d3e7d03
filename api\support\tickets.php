<?php
/**
 * إدارة تذاكر الدعم الفني
 * Support Tickets Management API
 */

require_once '../../includes/init.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getTickets();
        break;
    case 'POST':
        addTicket();
        break;
    default:
        errorResponse('Method not allowed', 405);
}

function getTickets() {
    global $db;
    
    // التحقق من صلاحيات المشرف للحصول على جميع التذاكر
    $isAdmin = false;
    $token = getAuthToken();
    if ($token) {
        $admin = $db->selectOne("SELECT id FROM admins WHERE token = ?", [$token]);
        $isAdmin = $admin ? true : false;
    }
    
    if ($isAdmin) {
        // المشرف يمكنه رؤية جميع التذاكر
        $sql = "SELECT st.*, u.name as user_name 
                FROM support_tickets st 
                LEFT JOIN users u ON st.user_id = u.id 
                WHERE st.active = 1 
                ORDER BY st.id DESC";
        $tickets = $db->select($sql);
    } else {
        // المستخدم العادي يرى تذاكره فقط
        $user = requireAuth('user');
        $sql = "SELECT * FROM support_tickets 
                WHERE user_id = ? AND active = 1 
                ORDER BY id DESC";
        $tickets = $db->select($sql, [$user['id']]);
    }
    
    // تنسيق البيانات
    foreach ($tickets as &$ticket) {
        if ($ticket['date']) {
            $ticket['formatted_date'] = formatArabicDate($ticket['date']);
        }
    }
    
    successResponse($tickets);
}

function addTicket() {
    global $db;
    
    $data = getRequestData();
    
    // التحقق من البيانات المطلوبة
    $requiredFields = ['content'];
    $errors = validateRequired($data, $requiredFields);
    
    if (!empty($errors)) {
        errorResponse(implode(', ', $errors));
    }
    
    $content = $data['content'];
    $email = $data['email'] ?? null;
    $subject = $data['subject'] ?? '';
    $userId = null;
    
    // التحقق من المصادقة (اختياري)
    $token = getAuthToken();
    if ($token) {
        $user = $db->selectOne("SELECT id, email FROM users WHERE token = ?", [$token]);
        if ($user) {
            $userId = $user['id'];
            $email = $email ?: $user['email'];
        }
    }
    
    // إذا لم يكن هناك مستخدم مسجل، يجب توفير البريد الإلكتروني
    if (!$userId && !$email) {
        errorResponse('البريد الإلكتروني مطلوب');
    }
    
    if ($email && !validateEmail($email)) {
        errorResponse('البريد الإلكتروني غير صحيح');
    }
    
    // إضافة التذكرة
    $insertSql = "INSERT INTO support_tickets (user_id, email, subject, content, date, active) 
                  VALUES (?, ?, ?, ?, CURDATE(), 1)";
    
    $ticketId = $db->insert($insertSql, [$userId, $email, $subject, $content]);
    
    if ($ticketId) {
        successResponse(['ticket_id' => $ticketId], 'تم إرسال تذكرة الدعم بنجاح');
    } else {
        errorResponse('حدث خطأ أثناء إرسال التذكرة');
    }
}
