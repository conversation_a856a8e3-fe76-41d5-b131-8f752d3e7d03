<?php
/**
 * إعجاب/إلغاء إعجاب بمنشور
 * Like/Unlike Post API
 */

require_once '../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

// التحقق من المصادقة
$user = requireAuth('user');

$data = getRequestData();

// التحقق من البيانات المطلوبة
$requiredFields = ['outlook_id'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$outlookId = $data['outlook_id'];

// التحقق من وجود التوقع
$outlookSql = "SELECT id, likes FROM outlooks WHERE id = ?";
$outlook = $db->selectOne($outlookSql, [$outlookId]);

if (!$outlook) {
    errorResponse('التوقع غير موجود', 404);
}

// التحقق من وجود إعجاب سابق
$likeSql = "SELECT id FROM outlooks_likes WHERE outlook_id = ? AND user_id = ?";
$existingLike = $db->selectOne($likeSql, [$outlookId, $user['id']]);

$db->beginTransaction();

try {
    if ($existingLike) {
        // إلغاء الإعجاب
        $deleteLikeSql = "DELETE FROM outlooks_likes WHERE id = ?";
        $db->delete($deleteLikeSql, [$existingLike['id']]);
        
        // تقليل عدد الإعجابات
        $updateOutlookSql = "UPDATE outlooks SET likes = likes - 1 WHERE id = ?";
        $db->update($updateOutlookSql, [$outlookId]);
        
        $action = 'unliked';
        $message = 'تم إلغاء الإعجاب';
        $newLikesCount = max(0, $outlook['likes'] - 1);
        
    } else {
        // إضافة إعجاب
        $insertLikeSql = "INSERT INTO outlooks_likes (outlook_id, user_id) VALUES (?, ?)";
        $db->insert($insertLikeSql, [$outlookId, $user['id']]);
        
        // زيادة عدد الإعجابات
        $updateOutlookSql = "UPDATE outlooks SET likes = likes + 1 WHERE id = ?";
        $db->update($updateOutlookSql, [$outlookId]);
        
        $action = 'liked';
        $message = 'تم الإعجاب بالمنشور';
        $newLikesCount = $outlook['likes'] + 1;
    }
    
    $db->commit();
    
    successResponse([
        'action' => $action,
        'likes_count' => $newLikesCount,
        'liked' => $action === 'liked'
    ], $message);
    
} catch (Exception $e) {
    $db->rollback();
    errorResponse('حدث خطأ أثناء معالجة الإعجاب');
}
