<?php
/**
 * تسجيل مستخدم جديد
 * User Registration API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$data = getRequestData();

// التحقق من البيانات المطلوبة
$requiredFields = ['name', 'email', 'password'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$name = $data['name'];
$email = $data['email'];
$password = $data['password'];
$country = $data['country'] ?? null;
$phone = $data['phone'] ?? null;
$coupon = $data['coupon'] ?? null;

// التحقق من صحة البريد الإلكتروني
if (!validateEmail($email)) {
    errorResponse('البريد الإلكتروني غير صحيح');
}

// التحقق من قوة كلمة المرور
if (!validatePassword($password)) {
    errorResponse('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
}

// التحقق من الكوبون إذا تم إدخاله
$marketerName = null;
if ($coupon) {
    $marketerSql = "SELECT full_name FROM marketers WHERE coupon = ? AND active = 1";
    $marketer = $db->selectOne($marketerSql, [$coupon]);
    if ($marketer) {
        $marketerName = $marketer['full_name'];
    }
}

// محاولة تسجيل المستخدم
$result = $auth->registerUser($name, $email, $password, $country, $phone, $coupon);

if ($result['success']) {
    // إذا كان هناك كوبون صحيح، سجل عمولة للمسوق
    if ($coupon && $marketerName) {
        $marketerDetailsSql = "INSERT INTO marketer_details (marketer_id, user_id, name, email, country, type, amount, date) 
                              SELECT m.id, ?, ?, ?, ?, 'register', m.reg_commission, CURDATE()
                              FROM marketers m WHERE m.coupon = ?";
        $db->insert($marketerDetailsSql, [$result['user_id'], $name, $email, $country, $coupon]);
    }
    
    successResponse(['user_id' => $result['user_id']], $result['message']);
} else {
    errorResponse($result['message'], 404);
}
