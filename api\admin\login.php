<?php
/**
 * تسجيل دخول المشرفين
 * Admin Login API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

$data = getRequestData();

// التحقق من البيانات المطلوبة
$requiredFields = ['email', 'password'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$email = $data['email'];
$password = $data['password'];

// التحقق من صحة البريد الإلكتروني
if (!validateEmail($email)) {
    errorResponse('البريد الإلكتروني غير صحيح');
}

// محاولة تسجيل الدخول
$result = $auth->loginAdmin($email, $password);

if ($result['success']) {
    successResponse($result['admin'], $result['message']);
} else {
    errorResponse($result['message'], 404);
}
