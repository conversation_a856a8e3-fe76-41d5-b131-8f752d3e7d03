<?php

namespace App\Http\Controllers;

use App\Models\Ads;
use App\Models\AdsDetails;
use App\Models\OutlooksSubscription;
use App\Models\SystemSettings;
use App\Models\Users;
use Illuminate\Http\Request;

class AdsController extends Controller
{
    public function get()
    {
        return Ads::orderBy('id', 'DESC')->with('details')->get();
    }
    public function views(Request $request)
    {
        $header = $request->header('Authorization');
        $user = Users::where('token', $header)->first();
        $checkCountry = AdsDetails::where([
            ['country', '=', $user->country], ['type', '=', 'view']
        ])->first();
        if ($checkCountry == null) {
            return AdsDetails::create([
                'ad_id' => $request->ad_id,
                'country' => $user->country,
                'count' => 1,
                'type' => 'view'
            ]);
        } else {
            return AdsDetails::where([
                ['country', '=', $user->country],
                ['type', '=', 'view']
            ])->update([
                'count' => $checkCountry->count + 1
            ]);
        }
    }
    public function clicks(Request $request)
    {
        $header = $request->header('Authorization');
        $user = Users::where('token', $header)->first();
        $checkCountry = AdsDetails::where([
            ['country', '=', $user->country], ['type', '=', 'click']
        ])->first();
        if ($checkCountry == null) {
            return AdsDetails::create([
                'ad_id' => $request->ad_id,
                'country' => $user->country,
                'count' => 1,
                'type' => 'click'
            ]);
        } else {
            return AdsDetails::where([
                ['country', '=', $user->country],
                ['type', '=', 'click']
            ])->update([
                'count' => $checkCountry->count + 1
            ]);
        }
    }
    public function add(Request $request)
    {
        $validate = $request->validate([
            'media' => 'required|mimes:jpeg,png,jpg,mp4,flv,3gp,mov,avi,wmv'
        ]);
        if ($validate) {
            $dataDecode = json_decode($request->data, true);
            $filename = time() . '.' . $request->media->getClientOriginalExtension();
            $file_path = $request->file('media')->storeAs('/ads/', $filename, 'public');

            $countries = $dataDecode['country'];

            if(is_array($countries) && count($countries) > 0)
            {
                foreach($countries as $country)
                {
                    Ads::create([
                        'title' => $dataDecode['title'],
                        'redirect' => $dataDecode['redirect'],
                        'hide' => $dataDecode['hide'],
                        'location' => $dataDecode['location'],
                        'country' => $country,
                        'media' => $filename
                    ]);
                }

                return true;
            }else{
                if($countries == '') {
                    // create general ad without limiting it to a specific country
                    Ads::create([
                        'title' => $dataDecode['title'],
                        'redirect' => $dataDecode['redirect'],
                        'hide' => $dataDecode['hide'],
                        'location' => $dataDecode['location'],
                        'country' => 'عام',
                        'media' => $filename
                    ]);

                    return true;
                }

                abort(500, "Countries needs to be type of an array");
            }
        }
    }
    public function delete(Request $request)
    {
        Ads::where('id', $request->id)->delete();
        AdsDetails::where('ad_id', $request->id)->delete();
    }

    public function subscribeViaAd(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();

        $data = $request->validate([
            'user_id' => 'required',
            'country' => 'required',
        ]);

        $data['is_registered_user'] = !is_null($user);

        $subscription_length = SystemSettings::select(['subscription_mode', 'subscription_length'])->where("id", 1)->first()['subscription_length'];
        $data['expires_at'] = now()->addHours($subscription_length);

        return OutlooksSubscription::create($data);
    }
}
