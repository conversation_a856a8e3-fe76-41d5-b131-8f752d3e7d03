<?php
/**
 * إضافة توقع جوي جديد
 * Add New Weather Outlook API
 */

require_once '../../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

// التحقق من صلاحيات المشرف
$admin = requireAuth('admin');

// الحصول على البيانات
$data = [];
if (isset($_POST['data'])) {
    $data = json_decode($_POST['data'], true);
}

// التحقق من البيانات المطلوبة
$requiredFields = ['details'];
$errors = validateRequired($data, $requiredFields);

if (!empty($errors)) {
    errorResponse(implode(', ', $errors));
}

$title = $data['title'] ?? '';
$details = $data['details'];
$countries = $data['countries'] ?? [];
$schedule = $data['schedule'] ?? null;
$hide = $data['hide'] ?? null;

// التحقق من وجود ملفات
$uploadedFiles = [];
if (isset($_FILES['media']) && !empty($_FILES['media']['name'][0])) {
    $files = $_FILES['media'];
    $fileCount = count($files['name']);
    
    for ($i = 0; $i < $fileCount; $i++) {
        $file = [
            'name' => $files['name'][$i],
            'type' => $files['type'][$i],
            'tmp_name' => $files['tmp_name'][$i],
            'error' => $files['error'][$i],
            'size' => $files['size'][$i]
        ];
        
        // رفع الملف
        $allowedTypes = array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_VIDEO_TYPES);
        $uploadResult = uploadFile($file, 'outlooks', $allowedTypes);
        
        if ($uploadResult['success']) {
            $uploadedFiles[] = $uploadResult['filename'];
        } else {
            errorResponse($uploadResult['message']);
        }
    }
}

// إذا لم يتم تحديد بلدان، أضف لجميع البلدان
if (empty($countries)) {
    $countriesSql = "SELECT country FROM countries ORDER BY id";
    $allCountries = $db->select($countriesSql);
    $countries = array_column($allCountries, 'country');
}

$db->beginTransaction();

try {
    // إضافة التوقع لكل بلد
    foreach ($countries as $country) {
        $outlookSql = "INSERT INTO outlooks (title, date, country, details, schedule, hide, likes, shares) 
                       VALUES (?, NOW(), ?, ?, ?, ?, 0, 0)";
        
        $outlookId = $db->insert($outlookSql, [
            $title,
            $country,
            $details,
            $schedule,
            $hide
        ]);
        
        if (!$outlookId) {
            throw new Exception('فشل في إضافة التوقع');
        }
        
        // إضافة الملفات
        foreach ($uploadedFiles as $filename) {
            $fileSql = "INSERT INTO outlooks_files (outlook_id, file) VALUES (?, ?)";
            $db->insert($fileSql, [$outlookId, $filename]);
        }
    }
    
    $db->commit();
    successResponse(['uploaded_files' => $uploadedFiles], 'تم إضافة التوقع بنجاح');
    
} catch (Exception $e) {
    $db->rollback();
    
    // حذف الملفات المرفوعة في حالة الخطأ
    foreach ($uploadedFiles as $filename) {
        deleteFile(UPLOAD_PATH . 'outlooks/' . $filename);
    }
    
    errorResponse('حدث خطأ أثناء إضافة التوقع: ' . $e->getMessage());
}
