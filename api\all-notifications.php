<?php
/**
 * الحصول على جميع الإشعارات
 * Get All Notifications API
 */

require_once '../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// الحصول على المعاملات
$country = $_GET['country'] ?? '';
$appearanceFor = $_GET['appearance_for'] ?? 'all';

// بناء الاستعلام
$sql = "SELECT * FROM notifications WHERE 1=1";
$params = [];

// تصفية حسب البلد
if (!empty($country)) {
    $sql .= " AND (country = ? OR country IS NULL)";
    $params[] = $country;
}

// تصفية حسب نوع الظهور
if ($appearanceFor !== 'all') {
    $sql .= " AND appearance_for = ?";
    $params[] = $appearanceFor;
}

// ترتيب حسب التاريخ
$sql .= " ORDER BY id DESC";

$notifications = $db->select($sql, $params);

// تنسيق البيانات
foreach ($notifications as &$notification) {
    // إضافة رابط الصورة
    if ($notification['media']) {
        $notification['media_url'] = APP_URL . '/uploads/notifications/' . $notification['media'];
    }
    
    // تنسيق التاريخ
    if ($notification['date']) {
        $notification['formatted_date'] = formatArabicDate($notification['date']);
    }
    
    // تنسيق تاريخ الجدولة
    if ($notification['schedule']) {
        $notification['formatted_schedule'] = formatArabicDate($notification['schedule']);
    }
}

successResponse($notifications);
