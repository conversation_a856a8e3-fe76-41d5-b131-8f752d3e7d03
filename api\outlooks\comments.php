<?php
/**
 * إدارة تعليقات التوقعات
 * Outlook Comments Management API
 */

require_once '../../includes/init.php';

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        getComments();
        break;
    case 'POST':
        addComment();
        break;
    default:
        errorResponse('Method not allowed', 405);
}

function getComments() {
    global $db;
    
    $outlookId = $_GET['outlook_id'] ?? '';
    
    if (empty($outlookId)) {
        errorResponse('معرف التوقع مطلوب');
    }
    
    $sql = "SELECT oc.*, u.name as user_name, u.pic as user_pic 
            FROM outlooks_comments oc 
            LEFT JOIN users u ON oc.user_id = u.id 
            WHERE oc.outlook_id = ? 
            ORDER BY oc.id DESC";
    
    $comments = $db->select($sql, [$outlookId]);
    
    // تنسيق البيانات
    foreach ($comments as &$comment) {
        $comment['formatted_date'] = formatArabicDate($comment['date']);
        
        // إضافة رابط صورة المستخدم إذا كانت موجودة
        if ($comment['user_pic']) {
            $comment['user_pic_url'] = APP_URL . '/uploads/profiles/' . $comment['user_pic'];
        }
    }
    
    successResponse($comments);
}

function addComment() {
    global $db, $auth;
    
    // التحقق من المصادقة
    $user = requireAuth('user');
    
    $data = getRequestData();
    
    // التحقق من البيانات المطلوبة
    $requiredFields = ['outlook_id', 'comment'];
    $errors = validateRequired($data, $requiredFields);
    
    if (!empty($errors)) {
        errorResponse(implode(', ', $errors));
    }
    
    $outlookId = $data['outlook_id'];
    $comment = $data['comment'];
    
    // التحقق من وجود التوقع
    $outlookSql = "SELECT id FROM outlooks WHERE id = ?";
    $outlook = $db->selectOne($outlookSql, [$outlookId]);
    
    if (!$outlook) {
        errorResponse('التوقع غير موجود', 404);
    }
    
    // إضافة التعليق
    $insertSql = "INSERT INTO outlooks_comments (outlook_id, user_id, comment, date) VALUES (?, ?, ?, CURDATE())";
    $commentId = $db->insert($insertSql, [$outlookId, $user['id'], $comment]);
    
    if ($commentId) {
        // الحصول على التعليق المضاف مع بيانات المستخدم
        $newCommentSql = "SELECT oc.*, u.name as user_name, u.pic as user_pic 
                          FROM outlooks_comments oc 
                          LEFT JOIN users u ON oc.user_id = u.id 
                          WHERE oc.id = ?";
        $newComment = $db->selectOne($newCommentSql, [$commentId]);
        
        if ($newComment) {
            $newComment['formatted_date'] = formatArabicDate($newComment['date']);
            if ($newComment['user_pic']) {
                $newComment['user_pic_url'] = APP_URL . '/uploads/profiles/' . $newComment['user_pic'];
            }
        }
        
        successResponse($newComment, 'تم إضافة التعليق بنجاح');
    } else {
        errorResponse('حدث خطأ أثناء إضافة التعليق');
    }
}
