(window.webpackJsonp=window.webpackJsonp||[]).push([[41],{nkCL:function(t,n,e){"use strict";e.r(n);var l=e("W51F"),u={setup:function(){return{contentWidth:Object(l.a)().contentWidth}}},o=e("KHd+"),c={components:{LayoutFull:Object(o.a)(u,(function(){var t=this.$createElement,n=this._self._c||t;return n("div",{class:"boxed"===this.contentWidth?"container p-0":null},[n("router-view")],1)}),[],!1,null,null,null).exports}},r=Object(o.a)(c,(function(){var t=this.$createElement,n=this._self._c||t;return n("layout-full",[n("router-view")],1)}),[],!1,null,null,null);n.default=r.exports}}]);