<?php

namespace App\Http\Controllers;

use App\Models\Subscriptions;
use Illuminate\Http\Request;

class SubscriptionsController extends Controller
{
    public function get()
    {
        return Subscriptions::orderBy('id', 'DESC')->with('user')->get();
    }
    public function delete($id)
    {
        return Subscriptions::where('id', $id)->delete();
    }
    public function multiDelete(Request $request)
    {
        $data = $request->validate([
           'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Subscriptions::where('id', $id)->delete();
        }

        return true;
    }
    public function setInactive(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Subscriptions::where('id', $id)->update(['active' => false]);
        }

        return true;
    }
    public function addDays(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array'],
            'days' => ['nullable', 'numeric']
        ]);

        $expire_date = now()->addDays($data['days'] ?? 30);

        foreach($data['ids'] as $id)
        {
            Subscriptions::where('id', $id)->update(['active' => true, 'expire_date' => $expire_date]);
        }

        return true;
    }
    public function setActive(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Subscriptions::where('id', $id)->update(['active' => true]);
        }

        return true;
    }
}
