<?php
/**
 * أكثر المنشورات إعجاباً
 * Most Liked Posts API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// الحصول على عدد النتائج
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$limit = min($limit, 50); // حد أقصى 50

// الحصول على أكثر المنشورات إعجاباً
$sql = "SELECT o.*, 
               GROUP_CONCAT(of.file) as files,
               COUNT(DISTINCT oc.id) as comments_count
        FROM outlooks o
        LEFT JOIN outlooks_files of ON o.id = of.outlook_id
        LEFT JOIN outlooks_comments oc ON o.id = oc.outlook_id
        GROUP BY o.id
        ORDER BY o.likes DESC, o.id DESC
        LIMIT ?";

$posts = $db->select($sql, [$limit]);

// تنسيق البيانات
foreach ($posts as &$post) {
    // تحويل الملفات إلى مصفوفة
    if ($post['files']) {
        $post['files'] = array_map(function($file) {
            return [
                'file' => $file,
                'url' => APP_URL . '/uploads/outlooks/' . $file,
                'type' => getFileType($file)
            ];
        }, explode(',', $post['files']));
    } else {
        $post['files'] = [];
    }
    
    // تنسيق التاريخ
    $post['formatted_date'] = formatArabicDate($post['date']);
    
    // اختصار النص إذا كان طويل
    if (strlen($post['details']) > 100) {
        $post['short_details'] = substr($post['details'], 0, 100) . '...';
    } else {
        $post['short_details'] = $post['details'];
    }
}

successResponse($posts);
