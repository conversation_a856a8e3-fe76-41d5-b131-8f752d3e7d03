<?php
/**
 * إحصائيات العدد
 * Count Statistics API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// الحصول على نوع الإحصائية
$type = $_GET['type'] ?? 'all';

$stats = [];

switch ($type) {
    case 'users':
        $stats['count'] = $db->selectOne("SELECT COUNT(*) as count FROM users")['count'];
        break;
        
    case 'subs':
        $stats['count'] = $db->selectOne("SELECT COUNT(*) as count FROM subscriptions WHERE active = 1")['count'];
        break;
        
    case 'posts':
        $stats['count'] = $db->selectOne("SELECT COUNT(*) as count FROM outlooks")['count'];
        break;
        
    case 'weatherShots':
        $stats['count'] = $db->selectOne("SELECT COUNT(*) as count FROM weather_shots")['count'];
        break;
        
    case 'active':
        // عدد المستخدمين النشطين (الذين سجلوا دخول في آخر 30 يوم)
        $stats['count'] = $db->selectOne("SELECT COUNT(DISTINCT user_id) as count FROM subscriptions WHERE active = 1")['count'];
        break;
        
    case 'all':
    default:
        $stats = [
            'users_count' => $db->selectOne("SELECT COUNT(*) as count FROM users")['count'],
            'subscriptions_count' => $db->selectOne("SELECT COUNT(*) as count FROM subscriptions WHERE active = 1")['count'],
            'outlooks_count' => $db->selectOne("SELECT COUNT(*) as count FROM outlooks")['count'],
            'weather_shots_count' => $db->selectOne("SELECT COUNT(*) as count FROM weather_shots")['count'],
            'marketers_count' => $db->selectOne("SELECT COUNT(*) as count FROM marketers WHERE active = 1")['count'],
            'coupons_count' => $db->selectOne("SELECT COUNT(*) as count FROM coupons WHERE active = 1")['count'],
            'notifications_count' => $db->selectOne("SELECT COUNT(*) as count FROM notifications")['count'],
            'ads_count' => $db->selectOne("SELECT COUNT(*) as count FROM ads")['count'],
            'tickets_count' => $db->selectOne("SELECT COUNT(*) as count FROM support_tickets WHERE active = 1")['count'],
            'countries_count' => $db->selectOne("SELECT COUNT(*) as count FROM countries")['count']
        ];
        break;
}

successResponse($stats);
