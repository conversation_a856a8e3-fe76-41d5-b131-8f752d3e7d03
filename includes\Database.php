<?php
/**
 * كلاس قاعدة البيانات
 * Database Class
 */

class Database {
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $charset;
    private $pdo;
    private static $instance = null;

    public function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        
        $this->connect();
    }

    /**
     * إنشاء اتصال واحد بقاعدة البيانات (Singleton Pattern)
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch (PDOException $e) {
            if (APP_DEBUG) {
                die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
            } else {
                die("خطأ في الاتصال بقاعدة البيانات");
            }
        }
    }

    /**
     * الحصول على كائن PDO
     */
    public function getPdo() {
        return $this->pdo;
    }

    /**
     * تنفيذ استعلام SELECT
     */
    public function select($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->handleError($e);
            return false;
        }
    }

    /**
     * تنفيذ استعلام SELECT لصف واحد
     */
    public function selectOne($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->handleError($e);
            return false;
        }
    }

    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute($params);
            return $result ? $this->pdo->lastInsertId() : false;
        } catch (PDOException $e) {
            $this->handleError($e);
            return false;
        }
    }

    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->handleError($e);
            return false;
        }
    }

    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->handleError($e);
            return false;
        }
    }

    /**
     * تنفيذ استعلام عام
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            $this->handleError($e);
            return false;
        }
    }

    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->pdo->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->pdo->rollback();
    }

    /**
     * معالجة الأخطاء
     */
    private function handleError($e) {
        if (APP_DEBUG) {
            error_log("Database Error: " . $e->getMessage());
            echo "خطأ في قاعدة البيانات: " . $e->getMessage();
        } else {
            error_log("Database Error: " . $e->getMessage());
            echo "حدث خطأ في النظام";
        }
    }

    /**
     * إغلاق الاتصال
     */
    public function close() {
        $this->pdo = null;
    }
}
