<?php
/**
 * الحصول على التوقعات حسب البلد
 * Get Weather Outlooks by Country API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// الحصول على البلد من URL
$country = $_GET['country'] ?? '';

if (empty($country)) {
    errorResponse('البلد مطلوب');
}

// فك تشفير اسم البلد إذا كان مُرمز
$country = urldecode($country);

// التحقق من وجود البلد
$countrySql = "SELECT * FROM countries WHERE country = ?";
$countryData = $db->selectOne($countrySql, [$country]);

if (!$countryData) {
    errorResponse('البلد غير موجود', 404);
}

// التحقق من التوكن للمستخدمين المسجلين
$token = getAuthToken();
$user = null;

if ($token) {
    $user = $auth->validateToken($token, 'user');
}

// إذا كان المستخدم مسجل، تحقق من الاشتراك
if ($user) {
    $subscriptionSql = "SELECT * FROM outlooks_subscription WHERE user_id = ? AND country = ? AND expires_at > NOW()";
    $subscription = $db->selectOne($subscriptionSql, [$user['id'], $country]);
    
    if (!$subscription) {
        // التحقق من الاشتراك العام
        $generalSubscriptionSql = "SELECT * FROM subscriptions WHERE user_id = ? AND active = 1 AND expire_date >= CURDATE()";
        $generalSubscription = $db->selectOne($generalSubscriptionSql, [$user['id']]);
        
        if (!$generalSubscription) {
            errorResponse('يجب الاشتراك للوصول إلى توقعات هذا البلد', 403);
        }
    }
}

$now = date('Y-m-d H:i:s');

// الحصول على التوقعات للبلد المحدد
$sql = "SELECT o.*, 
               GROUP_CONCAT(of.file) as files,
               COUNT(DISTINCT oc.id) as comments_count,
               COUNT(DISTINCT ol.id) as likes_count
        FROM outlooks o
        LEFT JOIN outlooks_files of ON o.id = of.outlook_id
        LEFT JOIN outlooks_comments oc ON o.id = oc.outlook_id
        LEFT JOIN outlooks_likes ol ON o.id = ol.outlook_id
        WHERE o.country = ? 
        AND (o.schedule <= ? OR o.schedule IS NULL OR o.schedule = '')
        GROUP BY o.id
        ORDER BY o.id DESC
        LIMIT 10";

$outlooks = $db->select($sql, [$country, $now]);

// تنسيق البيانات
foreach ($outlooks as &$outlook) {
    // تحويل الملفات إلى مصفوفة
    if ($outlook['files']) {
        $outlook['files'] = array_map(function($file) {
            return [
                'file' => $file,
                'url' => APP_URL . '/uploads/outlooks/' . $file,
                'type' => getFileType($file)
            ];
        }, explode(',', $outlook['files']));
    } else {
        $outlook['files'] = [];
    }
    
    // الحصول على التعليقات
    $commentsSql = "SELECT oc.*, u.name as user_name, u.pic as user_pic 
                    FROM outlooks_comments oc 
                    LEFT JOIN users u ON oc.user_id = u.id 
                    WHERE oc.outlook_id = ? 
                    ORDER BY oc.id DESC";
    $outlook['comments'] = $db->select($commentsSql, [$outlook['id']]);
    
    // تنسيق التاريخ
    $outlook['formatted_date'] = formatArabicDate($outlook['date']);
    
    // التحقق من إعجاب المستخدم الحالي
    if ($user) {
        $likeSql = "SELECT id FROM outlooks_likes WHERE outlook_id = ? AND user_id = ?";
        $userLike = $db->selectOne($likeSql, [$outlook['id'], $user['id']]);
        $outlook['user_liked'] = $userLike ? true : false;
    } else {
        $outlook['user_liked'] = false;
    }
}

$response = [
    'outlooks' => $outlooks,
    'country' => $countryData,
    'total' => count($outlooks)
];

successResponse($response);
