(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{EG0M:function(t,s,a){"use strict";a.r(s);var e=a("HaE+"),i=a("o0o1"),n=a.n(i),o=a("vDqi"),l=a.n(o),r={components:{VueGoodTable:a("9hfn").a},data:function(){return{pageLength:3,dir:!1,columns:[{label:"#",field:"id",hidden:!0},{label:"العنوان",field:"title",sortable:!1},{label:"الدولة",field:"country",sortable:!1},{label:"التاريخ والوقت",field:"date"},{label:"عدد الاعجابات",field:"likes"},{label:"الاعدادات",field:"action",sortable:!1}],rows:[],pagesVisitColumns:[{label:"الصفحة",field:"pageTitle",sortable:!1},{label:"عدد الزيارات",field:"screenPageViews",sortable:!1},{label:"المتصلين حالياً",field:"activeUsers",sortable:!1}],pagesVisitRows:[],pagesVisitDays:7,visitCountColumns:[{label:"الدولة",field:"country",sortable:!1},{label:"عدد الزوار",field:"screenPageViews",sortable:!1},{label:"المتصلين حالياً",field:"activeUsers",sortable:!1}],visitCountRows:[],pagesVisitByCountryDays:7,currentlyActive:0,searchTerm:"",users:null,subsCount:null,postsCount:null,weatherShotsCount:null}},mounted:function(){var t=this;return Object(e.a)(n.a.mark((function s(){var a,e,i,o,r,c,u,d;return n.a.wrap((function(s){for(;;)switch(s.prev=s.next){case 0:return s.prev=0,s.next=3,l.a.get("/api/usersCount");case 3:return a=s.sent,t.users=a.data,s.next=7,l.a.get("/api/subsCount");case 7:return e=s.sent,t.subsCount=e.data,s.next=11,l.a.get("/api/postsCount");case 11:return i=s.sent,t.postsCount=i.data,s.next=15,l.a.get("/api/weatherShotsCount");case 15:return o=s.sent,t.weatherShotsCount=o.data,s.next=19,l.a.get("/api/most-likes-posts");case 19:return r=s.sent,t.rows=r.data,s.next=23,l.a.get("/api/activeCount");case 23:return c=s.sent,t.currentlyActive=c.data,s.next=27,l.a.get("/api/mostViewedPages?days="+t.pagesVisitDays);case 27:return u=s.sent,t.pagesVisitRows=u.data,s.next=31,l.a.get("/api/mostViewedPagesByCountry?days="+t.pagesVisitByCountryDays);case 31:d=s.sent,t.visitCountRows=d.data,s.next=38;break;case 35:s.prev=35,s.t0=s.catch(0),alert("حدث خطأ ما");case 38:case"end":return s.stop()}}),s,null,[[0,35]])})))()},methods:{updateVisitedPages:function(){var t=this;this.pagesVisitRows=[],l.a.get("/api/mostViewedPages?days="+this.pagesVisitDays).then((function(s){return t.pagesVisitRows=s.data}))},updateVisitCountByCountry:function(){var t=this;this.visitCountRows=[],l.a.get("/api/mostViewedPagesByCountry?days="+this.pagesVisitByCountryDays).then((function(s){return t.visitCountRows=s.data}))}}},c=a("KHd+"),u=Object(c.a)(r,(function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",[a("div",{staticClass:"row"},[a("div",{staticClass:"col-lg"},[a("div",{staticClass:"card"},[a("div",{staticClass:"card-body d-flex justify-content-between align-items-center"},[a("div",{staticClass:"truncate"},[a("h2",{staticClass:"mb-25 font-weight-bolder"},[t._v("\n                            "+t._s(t.users)+"\n                        ")]),t._v(" "),a("span",[t._v("عدد المستخدمين")])]),t._v(" "),a("span",{staticClass:"b-avatar badge-light-primary rounded-circle",staticStyle:{width:"45px",height:"45px"}},[a("span",{staticClass:"b-avatar-custom"},[a("feather-icon",{attrs:{icon:"UsersIcon",size:"22"}})],1)])])])]),t._v(" "),a("div",{staticClass:"col-lg"},[a("div",{staticClass:"card"},[a("div",{staticClass:"card-body d-flex justify-content-between align-items-center"},[a("div",{staticClass:"truncate"},[a("h2",{staticClass:"mb-25 font-weight-bolder"},[t._v("\n                            "+t._s(t.subsCount)+"\n                        ")]),t._v(" "),a("span",[t._v("عدد المشتركين")])]),t._v(" "),a("span",{staticClass:"b-avatar badge-light-success rounded-circle",staticStyle:{width:"45px",height:"45px"}},[a("span",{staticClass:"b-avatar-custom"},[a("feather-icon",{attrs:{icon:"UserIcon",size:"22"}})],1)])])])]),t._v(" "),a("div",{staticClass:"col-lg"},[a("div",{staticClass:"card"},[a("div",{staticClass:"card-body d-flex justify-content-between align-items-center"},[a("div",{staticClass:"truncate"},[a("h2",{staticClass:"mb-25 font-weight-bolder"},[t._v("\n                            "+t._s(t.postsCount)+"\n                        ")]),t._v(" "),a("span",[t._v("عدد المنشورات")])]),t._v(" "),a("span",{staticClass:"b-avatar badge-light-primary rounded-circle",staticStyle:{width:"45px",height:"45px"}},[a("span",{staticClass:"b-avatar-custom"},[a("feather-icon",{attrs:{icon:"CloudIcon",size:"22"}})],1)])])])])]),t._v(" "),a("div",{staticClass:"row"},[a("div",{staticClass:"col-lg"},[a("div",{staticClass:"card"},[a("div",{staticClass:"card-body d-flex justify-content-between align-items-center"},[a("div",{staticClass:"truncate"},[a("h2",{staticClass:"mb-25 font-weight-bolder"},[t._v("\n                            "+t._s(t.weatherShotsCount)+"\n                        ")]),t._v(" "),a("span",[t._v("عدد الوسائط المنشورة")])]),t._v(" "),a("span",{staticClass:"b-avatar badge-light-primary rounded-circle",staticStyle:{width:"45px",height:"45px"}},[a("span",{staticClass:"b-avatar-custom"},[a("feather-icon",{attrs:{icon:"BookmarkIcon",size:"22"}})],1)])])])]),t._v(" "),a("div",{staticClass:"col-lg"},[a("div",{staticClass:"card"},[a("div",{staticClass:"card-body d-flex justify-content-between align-items-center"},[a("div",{staticClass:"truncate"},[a("h2",{staticClass:"mb-25 font-weight-bolder"},[t._v(t._s(t.currentlyActive))]),t._v(" "),a("span",[t._v("المتواجدون حالياً")])]),t._v(" "),a("span",{staticClass:"b-avatar badge-light-success rounded-circle",staticStyle:{width:"45px",height:"45px"}},[a("span",{staticClass:"b-avatar-custom"},[a("feather-icon",{attrs:{icon:"CircleIcon",size:"22"}})],1)])])])])]),t._v(" "),a("div",{staticClass:"row"},[a("div",{staticClass:"col-lg"},[a("h4",[t._v("\n                الصفحات الاكثر زيارة\n\n                "),a("select",{directives:[{name:"model",rawName:"v-model",value:t.pagesVisitDays,expression:"pagesVisitDays"}],staticClass:"m-2",on:{change:[function(s){var a=Array.prototype.filter.call(s.target.options,(function(t){return t.selected})).map((function(t){return"_value"in t?t._value:t.value}));t.pagesVisitDays=s.target.multiple?a:a[0]},t.updateVisitedPages]}},[a("option",{attrs:{value:"7"}},[t._v("7 ايام")]),t._v(" "),a("option",{attrs:{value:"14"}},[t._v("14 ايام")]),t._v(" "),a("option",{attrs:{value:"30"}},[t._v("شهر")])]),t._v("\n\n                أو\n\n                "),a("lable",[a("input",{directives:[{name:"model",rawName:"v-model",value:t.pagesVisitDays,expression:"pagesVisitDays"}],staticClass:"rounded m-2 px-2",attrs:{type:"number",min:"0"},domProps:{value:t.pagesVisitDays},on:{change:t.updateVisitedPages,input:function(s){s.target.composing||(t.pagesVisitDays=s.target.value)}}})]),t._v("\n\n                يوم\n            ")],1),t._v(" "),a("vue-good-table",{attrs:{columns:t.pagesVisitColumns,rows:t.pagesVisitRows},scopedSlots:t._u([{key:"table-row",fn:function(s){return["fullName"===s.column.field?a("span",{staticClass:"text-nowrap"},[a("span",{staticClass:"text-nowrap"},[t._v(t._s(s.row.fullName))])]):a("span",[t._v("\n                        "+t._s(s.formattedRow[s.column.field])+"\n                    ")])]}}])},[a("div",{attrs:{slot:"emptystate"},slot:"emptystate"},[t._v("لا توجد بيانات")])])],1),t._v(" "),a("div",{staticClass:"col-lg"},[a("h4",[t._v("\n                عدد الزوار حسب الدولة\n                "),a("select",{directives:[{name:"model",rawName:"v-model",value:t.pagesVisitByCountryDays,expression:"pagesVisitByCountryDays"}],staticClass:"m-2",on:{change:[function(s){var a=Array.prototype.filter.call(s.target.options,(function(t){return t.selected})).map((function(t){return"_value"in t?t._value:t.value}));t.pagesVisitByCountryDays=s.target.multiple?a:a[0]},t.updateVisitCountByCountry]}},[a("option",{attrs:{value:"7"}},[t._v("7 ايام")]),t._v(" "),a("option",{attrs:{value:"14"}},[t._v("14 ايام")]),t._v(" "),a("option",{attrs:{value:"30"}},[t._v("شهر")])]),t._v("\n\n                أو\n\n                "),a("lable",[a("input",{directives:[{name:"model",rawName:"v-model",value:t.pagesVisitByCountryDays,expression:"pagesVisitByCountryDays"}],staticClass:"rounded m-2 px-2",attrs:{type:"number",min:"0"},domProps:{value:t.pagesVisitByCountryDays},on:{change:t.updateVisitCountByCountry,input:function(s){s.target.composing||(t.pagesVisitByCountryDays=s.target.value)}}})]),t._v("\n\n                يوم\n            ")],1),t._v(" "),a("vue-good-table",{attrs:{columns:t.visitCountColumns,rows:t.visitCountRows},scopedSlots:t._u([{key:"table-row",fn:function(s){return["fullName"===s.column.field?a("span",{staticClass:"text-nowrap"},[a("span",{staticClass:"text-nowrap"},[t._v(t._s(s.row.fullName))])]):a("span",[t._v("\n                        "+t._s(s.formattedRow[s.column.field])+"\n                    ")])]}}])},[a("div",{attrs:{slot:"emptystate"},slot:"emptystate"},[t._v("لا توجد بيانات")])])],1)]),t._v(" "),a("br"),a("br"),t._v(" "),a("h4",[t._v("اكثر المنشورات اعجاباً")]),t._v(" "),a("vue-good-table",{attrs:{columns:t.columns,rows:t.rows,rtl:t.dir,"search-options":{enabled:!0,externalQuery:t.searchTerm}},scopedSlots:t._u([{key:"table-row",fn:function(s){return["fullName"===s.column.field?a("span",{staticClass:"text-nowrap"},[a("span",{staticClass:"text-nowrap"},[t._v(t._s(s.row.fullName))])]):"action"===s.column.field?a("span",[a("span",[a("router-link",{attrs:{to:"/outlook/"+s.row.id}},[a("feather-icon",{staticClass:"text-body",attrs:{icon:"EyeIcon",size:"16"}})],1)],1)]):a("span",[t._v("\n                "+t._s(s.formattedRow[s.column.field])+"\n            ")])]}}])},[a("div",{attrs:{slot:"emptystate"},slot:"emptystate"},[t._v("لا توجد بيانات")])])],1)}),[],!1,null,null,null);s.default=u.exports}}]);