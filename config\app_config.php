<?php
/**
 * إعدادات التطبيق الجديد - PHP العادي
 * New Application Configuration - Plain PHP
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'rainapp4_matar');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات التطبيق
define('APP_NAME', 'مطر - لوحة التحكم');
define('APP_URL', 'http://localhost/rain');
define('APP_DEBUG', true);

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'avi', 'mov', 'wmv', '3gp', 'flv']);

// إعدادات الأمان
define('JWT_SECRET', 'rain-app-secret-key-2023');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات Firebase (للإشعارات)
define('FIREBASE_SERVER_KEY', 'your-firebase-server-key');
define('FIREBASE_SENDER_ID', 'your-firebase-sender-id');

// إعدادات البريد الإلكتروني
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-password');
define('MAIL_FROM_NAME', 'تطبيق مطر');

// إعدادات التحليلات
define('GOOGLE_ANALYTICS_ID', 'your-analytics-id');

// إعدادات التطبيق
define('DEFAULT_TIMEZONE', 'Asia/Muscat');
define('DEFAULT_LANGUAGE', 'ar');

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// إعدادات عرض الأخطاء
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// إعدادات الجلسة
ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);
session_set_cookie_params(SESSION_TIMEOUT);
