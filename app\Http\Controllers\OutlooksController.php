<?php

namespace App\Http\Controllers;

use App\Models\Comments;
use App\Models\Countries;
use App\Models\Outlook;
use App\Models\OutlookLikes;
use App\Models\OutlooksFiles;
use App\Models\OutlookShares;
use App\Models\OutlooksSubscription;
use App\Models\SystemSettings;
use App\Models\Users;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;

class OutlooksController extends Controller
{
    public function get()
    {
        if(request()->get("onlyFutureScheduled", false))
        {
            $now = now();

            return Outlook::query()
                ->where("schedule", ">", $now)
                ->orderBy('id', "DESC")
                ->with("files", 'comments')
                ->get();
        }

        $now = request()->get("ignoreSchedule", false) ? now()->addCenturiesNoOverflow(50) : now();

        return Outlook::query()
            ->where(function ($query) use ($now) {
                $query->where("schedule", "<=", $now)
                    ->orWhereNull("schedule");
            })
            ->orderBy('id', 'DESC')
            ->with('files', 'comments')
            ->get();
    }

    public function getByCountry($country, Request $request)
    {
        $now = request()->get("ignoreSchedule", false) ? now()->addCenturiesNoOverflow(50) : now();
        $getCountry = Countries::where('id', $country)->first();

        // /api/outlooks/عام
        if($country == "public"){
            $outlooks = Outlook::query()->where(function ($query) use ($now) {
                $query->where("schedule", "<=", $now)->orWhereNull("schedule");
            })->where('country', "عام")
                ->with('files', 'comments')
                ->orderBy('id', 'DESC')
                ->paginate(10);

            return $this->getOutlooksArray($outlooks, $request);
        }

        if ($getCountry !== null) {
            // check if user has subscription
            $subscription_mode = SystemSettings::select(['subscription_mode', 'subscription_length'])->where("id", 1)->first()['subscription_mode'];
            if($subscription_mode){

                if(!$request->has('user_id')) {return response()->json(['type' => 'error', 'message' => "user_id field is required"]);}

                $user_id = $request->get('user_id');

                $latest_subscription = OutlooksSubscription::where("user_id", $user_id)->where('country', $country)->latest()->first();

                if(is_null($latest_subscription)) return response()->json([
                    'type' => 'error',
                    'message' => 'عذراً، لا يوجد لديك اشتراك لمشاهدة هذه الصفحة.']
                );

                if(now() > $latest_subscription->expires_at)
                {
                    $since = ($latest_subscription->expires_at)->diffForHumans();

                    return response()->json([
                       'type' => 'error',
                       'message' =>  "عذراً، انتهي هذا الاشتراك منذ {$since} ",
                    ]);
                }
            }

            $outlooks = Outlook::query()
                ->where(function ($query) use ($now) {
                    $query->where("schedule", "<=", $now)
                        ->orWhereNull("schedule");
                })
                ->where('country', $getCountry->country)
                ->with('files', 'comments')
                ->orderBy('id', 'DESC')
                ->paginate(10);

            return $this->getOutlooksArray($outlooks, $request);

        } else {
            return response()->json(['alert' => 'لا توجد بيانات'], 404);
        }
    }
    public function fetch($id)
    {
        return Outlook::where('id', $id)->with('files', 'comments')->first();
    }
    public function add(Request $request)
    {
        $validate = $request->validate([
            'files.*' => 'required|mimes:jpeg,png,jpg,mp4,flv,3gp,mov,avi,wmv'
        ]);

        if ($validate) {
            $messaging = app('firebase.messaging');
            $dataDecode = json_decode($request->data, true);
            $shouldCreatePublicRecord = (bool)$dataDecode['is_public'];

            if(is_array($dataDecode['country']) && count($dataDecode['country']) > 0) {
                foreach($dataDecode['country'] as $country) {
                    // process each country
                    $getOutlook = Outlook::create([
                        'title' => $dataDecode['title'],
                        'date' => $dataDecode['date'],
                        'country' => $country,
                        'details' => $dataDecode['details'],
                        'schedule' => $dataDecode['schedule'],
                        'hide' => $dataDecode['hideDate'],
                        'likes' => 0,
                        'shares' => 0,
                    ]);

                    $country_id = Countries::select('id')->where("country", $country)->first()->id;
                    $messaging->send(
                        \Kreait\Firebase\Messaging\CloudMessage::withTarget('topic', $country_id)
                            ->withNotification(\Kreait\Firebase\Messaging\Notification::create('توقع جديد', 'هناك توقع جديد'))
                            ->withData($getOutlook->toArray())
                    );

                    foreach ($request->file('files') as $file) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file_path = $file->storeAs('/outlooks/', $filename, 'public');
                        OutlooksFiles::updateOrCreate([
                            'outlook_id' => $getOutlook->id,
                            'file' => $filename
                        ]);
                    }
                }

                if($shouldCreatePublicRecord){
                    // store the outlook as general outlook if no countries are provided
                    // /api/outlooks/public
                    $this->createPublicOutlookRecord($dataDecode, $request);
                    return true;
                }


            } else{
                if($shouldCreatePublicRecord){
                    // store the outlook as general outlook if no countries are provided
                    // /api/outlooks/public
                    $this->createPublicOutlookRecord($dataDecode, $request);

                    return true;
                }

                return abort(500, "Countries needs to be type of an array");
            }
        }

        return abort(500);
    }
    public function edit(Request $request)
    {
        $validate = $request->validate([
            'files.*' => 'nullable|mimes:jpeg,png,jpg,mp4,flv,3gp,mov,avi,wmv'
        ]);

        $dataDecode = json_decode($request->data, true);

        $outlook = Outlook::where("id", $dataDecode['id'])->first();

        if(is_null($outlook)) return abort(404);

        Outlook::where('id', $dataDecode['id'])->update([
            'title' => $dataDecode['title'],
            'date' => $dataDecode['date'],
            'country' => $dataDecode['country'],
            'details' => $dataDecode['details'],
            'schedule' => $dataDecode['schedule'],
            'hide' => $dataDecode['hideDate'],
        ]);

        if($request->hasFile('files'))
        {
            // delete old files
            foreach($outlook->files as $file)
            {
                // delete the stored file
                if($file->DeleteStorageFile())
                    // delete the database record
                    OutlooksFiles::where('id', $file->id)->delete();
            }

            // upload new files
            foreach ($request->file('files') as $file) {
                $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                $file_path = $file->storeAs('/outlooks/', $filename, 'public');
                OutlooksFiles::updateOrCreate([
                    'outlook_id' => $outlook->id,
                    'file' => $filename
                ]);
            }
        }

        return true;
    }
    public function delete(Request $request)
    {
        Outlook::where('id', $request->id)->delete();
        Comments::where('outlook_id', $request->id)->delete();
        OutlooksFiles::where('outlook_id', $request->id)->delete();
        OutlookLikes::where('outlook_id', $request->id)->delete();
        OutlookShares::where('outlook_id', $request->id)->delete();
    }
    public function multiDelete(Request $request)
    {
        $data = $request->validate([
           'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            Outlook::where('id', $id)->delete();
            Comments::where('outlook_id', $id)->delete();
            OutlooksFiles::where('outlook_id', $id)->delete();
            OutlookLikes::where('outlook_id', $id)->delete();
            OutlookShares::where('outlook_id', $id)->delete();
        }

        return true;
    }

    public function fetchComments()
    {
        return Comments::orderBy('id', 'DESC')->with(['user', 'outlook'])->get();
    }
    public function reply(Request $request)
    {
        Comments::where('id', $request->comment_id)->update([
            'reply' => $request->reply
        ]);
    }
    public function sendComment(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        Comments::create([
            'outlook_id' => $request->outlook_id,
            'user_id' => $user->id,
            'comment' => $request->comment,
            'date' => date('Y-m-d')
        ]);
    }
    public function sendReply(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        Comments::where([
            ['id', $request->comment_id],
            ['outlook_id', $request->outlook_id]
        ])->update([
            'reply' => $request->reply
        ]);
    }
    public function like(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        $getOutlook = Outlook::where('id', $request->outlook_id)->first();
        if ($getOutlook !== null) {
            $checkLike = OutlookLikes::where('outlook_id', $request->outlook_id)->where('user_id', $user->id)->first();
            if ($checkLike == null) {
                OutlookLikes::updateOrCreate([
                    'outlook_id' => $request->outlook_id,
                    'user_id' => $user->id,
                ]);
                Outlook::where('id', $request->outlook_id)->update([
                    'likes' => $getOutlook->likes + 1
                ]);
            } else {
                return response()->json(["alert" => "تم الاعجاب بالمنشور من قبل"], 404);
            }
        } else {
            return response()->json(["alert" => "المنشور غير موجود"], 404);
        }
    }
    public function delete_unused(Request $request)
    {
        $getExpired = Outlook::where('hide', '<=', date('Y-m-d H:i:s'))->get();
        foreach ($getExpired as $outlook) {
            if ($outlook->hide !== null && $outlook->hide !== '') {
                OutlookLikes::where('outlook_id', $outlook->id)->delete();
                OutlookShares::where('outlook_id', $outlook->id)->delete();
                $get_files = OutlooksFiles::where('outlook_id', $outlook->id)->get();
                foreach ($get_files as $file) {
                    File::delete(public_path() . '/storage/outlooks/' . $file->file);
                    OutlooksFiles::where('id', $file->id)->delete();
                }
                DB::table('outlooks_comments')->where('outlook_id', $outlook->id)->delete();
                Outlook::where('id', $outlook->id)->delete();
            }
        }
    }
    public function share(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        $getOutlook = Outlook::where('id', $request->outlook_id)->first();
        if ($getOutlook !== null) {
            OutlookShares::create([
                'outlook_id' => $request->outlook_id,
                'user_id' => $user->id,
            ]);
            Outlook::where('id', $request->outlook_id)->update([
                'shares' => $getOutlook->shares + 1
            ]);
        } else {
            return response()->json(["alert" => "المنشور غير موجود"], 404);
        }
    }

    /**
     * @param $dataDecode
     * @param Request $request
     */
    private function createPublicOutlookRecord($dataDecode, Request $request) : Outlook
    {
        $messaging = app('firebase.messaging');

        $getOutlook = Outlook::create([
            'title' => $dataDecode['title'],
            'date' => $dataDecode['date'],
            'country' => 'عام',
            'details' => $dataDecode['details'],
            'schedule' => $dataDecode['schedule'],
            'hide' => $dataDecode['hideDate'],
            'likes' => 0,
            'shares' => 0,
        ]);

        $messaging->send(
            \Kreait\Firebase\Messaging\CloudMessage::withTarget('topic', 'public')
                ->withNotification(\Kreait\Firebase\Messaging\Notification::create('توقع جديد', 'هناك توقع جديد عام'))
                ->withData($getOutlook->toArray())
        );

        foreach ($request->file('files') as $file) {
            $filename = uniqid() . '.' . $file->getClientOriginalExtension();
            $file->storeAs('/outlooks/', $filename, 'public');
            OutlooksFiles::updateOrCreate([
                'outlook_id' => $getOutlook->id,
                'file' => $filename
            ]);
        }

        return $getOutlook;
    }

    /**
     * @param \Illuminate\Contracts\Pagination\LengthAwarePaginator $outlooks
     * @param Request $request
     * @return array
     */
    private function getOutlooksArray(\Illuminate\Contracts\Pagination\LengthAwarePaginator $outlooks, Request $request): array
    {
        $items = collect($outlooks->items())->map(function ($outlook) use ($request) {
            $user = Users::where('token', $request->header('Authorization'))->first();
            $liked = false;

            if (!is_null($user)) {
                $liked = OutlookLikes::where('outlook_id', $request->outlook_id)->where('user_id', $user->id)->first();
            }

            $outlook['liked'] = $liked;

            return $outlook;
        });

        $outlooks = $outlooks->toArray();
        unset($outlooks['data']);
        $outlooks['data'] = $items->toArray();
        return $outlooks;
    }
}
