/*! For license information please see 2.js.LICENSE.txt */
(window.webpackJsonp=window.webpackJsonp||[]).push([[2],{"3Zo4":function(t,e,n){"use strict";n.d(e,"b",(function(){return w})),n.d(e,"a",(function(){return y}));var i=n("XuX8"),r=n.n(i),o=n("xjcK"),s=n("pyNs"),a=n("m3aq"),c=n("Iyau"),l=n("hpAl"),f=n("z3V6"),u=n("+nMp"),p=n("la6Y"),d=n("kO/s"),h=n("jBgq"),m=n("GUe+"),b=n("2C+6");function v(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?v(Object(n),!0).forEach((function(e){O(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):v(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function O(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var w=Object(f.d)(Object(b.m)(g(g(g({},d.b),p.b),{},{block:Object(f.c)(s.g,!1),html:Object(f.c)(s.t),lazy:Object(f.c)(s.g,!1),menuClass:Object(f.c)(s.e),noCaret:Object(f.c)(s.g,!1),role:Object(f.c)(s.t,"menu"),size:Object(f.c)(s.t),split:Object(f.c)(s.g,!1),splitButtonType:Object(f.c)(s.t,"button",(function(t){return Object(c.a)(["button","submit","reset"],t)})),splitClass:Object(f.c)(s.e),splitHref:Object(f.c)(s.t),splitTo:Object(f.c)(s.r),splitVariant:Object(f.c)(s.t),text:Object(f.c)(s.t),toggleClass:Object(f.c)(s.e),toggleTag:Object(f.c)(s.t,"button"),toggleText:Object(f.c)(s.t,"Toggle dropdown"),variant:Object(f.c)(s.t,"secondary")})),o.s),y=r.a.extend({name:o.s,mixins:[d.a,p.a,h.a],props:w,computed:{dropdownClasses:function(){var t=this.block,e=this.split;return[this.directionClass,this.boundaryClass,{show:this.visible,"btn-group":e||!t,"d-flex":t&&e}]},menuClasses:function(){return[this.menuClass,{"dropdown-menu-right":this.right,show:this.visible}]},toggleClasses:function(){var t=this.split;return[this.toggleClass,{"dropdown-toggle-split":t,"dropdown-toggle-no-caret":this.noCaret&&!t}]}},render:function(t){var e=this.visible,n=this.variant,i=this.size,r=this.block,o=this.disabled,s=this.split,c=this.role,f=this.hide,p=this.toggle,d={variant:n,size:i,block:r,disabled:o},h=this.normalizeSlot(a.e),b=this.hasNormalizedSlot(a.e)?{}:Object(l.a)(this.html,this.text),v=t();if(s){var O=this.splitTo,w=this.splitHref,y=this.splitButtonType,j=g(g({},d),{},{variant:this.splitVariant||n});O?j.to=O:w?j.href=w:y&&(j.type=y),v=t(m.a,{class:this.splitClass,attrs:{id:this.safeId("_BV_button_")},props:j,domProps:b,on:{click:this.onSplitClick},ref:"button"},h),h=[t("span",{class:["sr-only"]},[this.toggleText])],b={}}var E=t(m.a,{staticClass:"dropdown-toggle",class:this.toggleClasses,attrs:{id:this.safeId("_BV_toggle_"),"aria-haspopup":"true","aria-expanded":Object(u.g)(e)},props:g(g({},d),{},{tag:this.toggleTag,block:r&&!s}),domProps:b,on:{mousedown:this.onMousedown,click:p,keydown:p},ref:"toggle"},h),x=t("ul",{staticClass:"dropdown-menu",class:this.menuClasses,attrs:{role:c,tabindex:"-1","aria-labelledby":this.safeId(s?"_BV_button_":"_BV_toggle_")},on:{keydown:this.onKeydown},ref:"menu"},[!this.lazy||e?this.normalizeSlot(a.h,{hide:f}):t()]);return t("div",{staticClass:"dropdown b-dropdown",class:this.dropdownClasses,attrs:{id:this.safeId()}},[v,E,x])}})},"6Ytq":function(t,e,n){"use strict";n.d(e,"a",(function(){return v}));var i=n("XuX8"),r=n.n(i),o=n("tC49"),s=n("xjcK"),a=n("pyNs"),c=n("2C+6"),l=n("z3V6"),f=n("Sjgb"),u=n("qlm0");function p(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?p(Object(n),!0).forEach((function(e){h(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function h(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var m=Object(c.j)(u.b,["event","routerTag"]);delete m.href.default,delete m.to.default;var b=Object(l.d)(Object(c.m)(d(d({},m),{},{pill:Object(l.c)(a.g,!1),tag:Object(l.c)(a.t,"span"),variant:Object(l.c)(a.t,"secondary")})),s.b),v=r.a.extend({name:s.b,functional:!0,props:b,render:function(t,e){var n=e.props,i=e.data,r=e.children,s=n.active,a=n.disabled,c=Object(f.d)(n),p=c?u.a:n.tag,d=n.variant||"secondary";return t(p,Object(o.a)(i,{staticClass:"badge",class:["badge-".concat(d),{"badge-pill":n.pill,active:s,disabled:a}],props:c?Object(l.e)(m,n):{}}),r)}})},"8L3F":function(t,e,n){"use strict";(function(t){var n="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,i=function(){for(var t=["Edge","Trident","Firefox"],e=0;e<t.length;e+=1)if(n&&navigator.userAgent.indexOf(t[e])>=0)return 1;return 0}();var r=n&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then((function(){e=!1,t()})))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout((function(){e=!1,t()}),i))}};function o(t){return t&&"[object Function]"==={}.toString.call(t)}function s(t,e){if(1!==t.nodeType)return[];var n=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?n[e]:n}function a(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function c(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var e=s(t),n=e.overflow,i=e.overflowX,r=e.overflowY;return/(auto|scroll|overlay)/.test(n+r+i)?t:c(a(t))}function l(t){return t&&t.referenceNode?t.referenceNode:t}var f=n&&!(!window.MSInputMethodContext||!document.documentMode),u=n&&/MSIE 10/.test(navigator.userAgent);function p(t){return 11===t?f:10===t?u:f||u}function d(t){if(!t)return document.documentElement;for(var e=p(10)?document.body:null,n=t.offsetParent||null;n===e&&t.nextElementSibling;)n=(t=t.nextElementSibling).offsetParent;var i=n&&n.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===s(n,"position")?d(n):n:t?t.ownerDocument.documentElement:document.documentElement}function h(t){return null!==t.parentNode?h(t.parentNode):t}function m(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?t:e,r=n?e:t,o=document.createRange();o.setStart(i,0),o.setEnd(r,0);var s,a,c=o.commonAncestorContainer;if(t!==c&&e!==c||i.contains(r))return"BODY"===(a=(s=c).nodeName)||"HTML"!==a&&d(s.firstElementChild)!==s?d(c):c;var l=h(t);return l.host?m(l.host,e):m(t,h(e).host)}function b(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top",n="top"===e?"scrollTop":"scrollLeft",i=t.nodeName;if("BODY"===i||"HTML"===i){var r=t.ownerDocument.documentElement,o=t.ownerDocument.scrollingElement||r;return o[n]}return t[n]}function v(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=b(e,"top"),r=b(e,"left"),o=n?-1:1;return t.top+=i*o,t.bottom+=i*o,t.left+=r*o,t.right+=r*o,t}function g(t,e){var n="x"===e?"Left":"Top",i="Left"===n?"Right":"Bottom";return parseFloat(t["border"+n+"Width"])+parseFloat(t["border"+i+"Width"])}function O(t,e,n,i){return Math.max(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],p(10)?parseInt(n["offset"+t])+parseInt(i["margin"+("Height"===t?"Top":"Left")])+parseInt(i["margin"+("Height"===t?"Bottom":"Right")]):0)}function w(t){var e=t.body,n=t.documentElement,i=p(10)&&getComputedStyle(n);return{height:O("Height",e,n,i),width:O("Width",e,n,i)}}var y=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},j=function(){function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}}(),E=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},x=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t};function k(t){return x({},t,{right:t.left+t.width,bottom:t.top+t.height})}function C(t){var e={};try{if(p(10)){e=t.getBoundingClientRect();var n=b(t,"top"),i=b(t,"left");e.top+=n,e.left+=i,e.bottom+=n,e.right+=i}else e=t.getBoundingClientRect()}catch(t){}var r={left:e.left,top:e.top,width:e.right-e.left,height:e.bottom-e.top},o="HTML"===t.nodeName?w(t.ownerDocument):{},a=o.width||t.clientWidth||r.width,c=o.height||t.clientHeight||r.height,l=t.offsetWidth-a,f=t.offsetHeight-c;if(l||f){var u=s(t);l-=g(u,"x"),f-=g(u,"y"),r.width-=l,r.height-=f}return k(r)}function P(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=p(10),r="HTML"===e.nodeName,o=C(t),a=C(e),l=c(t),f=s(e),u=parseFloat(f.borderTopWidth),d=parseFloat(f.borderLeftWidth);n&&r&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var h=k({top:o.top-a.top-u,left:o.left-a.left-d,width:o.width,height:o.height});if(h.marginTop=0,h.marginLeft=0,!i&&r){var m=parseFloat(f.marginTop),b=parseFloat(f.marginLeft);h.top-=u-m,h.bottom-=u-m,h.left-=d-b,h.right-=d-b,h.marginTop=m,h.marginLeft=b}return(i&&!n?e.contains(l):e===l&&"BODY"!==l.nodeName)&&(h=v(h,e)),h}function T(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=t.ownerDocument.documentElement,i=P(t,n),r=Math.max(n.clientWidth,window.innerWidth||0),o=Math.max(n.clientHeight,window.innerHeight||0),s=e?0:b(n),a=e?0:b(n,"left"),c={top:s-i.top+i.marginTop,left:a-i.left+i.marginLeft,width:r,height:o};return k(c)}function D(t){var e=t.nodeName;if("BODY"===e||"HTML"===e)return!1;if("fixed"===s(t,"position"))return!0;var n=a(t);return!!n&&D(n)}function N(t){if(!t||!t.parentElement||p())return document.documentElement;for(var e=t.parentElement;e&&"none"===s(e,"transform");)e=e.parentElement;return e||document.documentElement}function H(t,e,n,i){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o={top:0,left:0},s=r?N(t):m(t,l(e));if("viewport"===i)o=T(s,r);else{var f=void 0;"scrollParent"===i?"BODY"===(f=c(a(e))).nodeName&&(f=t.ownerDocument.documentElement):f="window"===i?t.ownerDocument.documentElement:i;var u=P(f,s,r);if("HTML"!==f.nodeName||D(s))o=u;else{var p=w(t.ownerDocument),d=p.height,h=p.width;o.top+=u.top-u.marginTop,o.bottom=d+u.top,o.left+=u.left-u.marginLeft,o.right=h+u.left}}var b="number"==typeof(n=n||0);return o.left+=b?n:n.left||0,o.top+=b?n:n.top||0,o.right-=b?n:n.right||0,o.bottom-=b?n:n.bottom||0,o}function L(t){return t.width*t.height}function S(t,e,n,i,r){var o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var s=H(n,i,o,r),a={top:{width:s.width,height:e.top-s.top},right:{width:s.right-e.right,height:s.height},bottom:{width:s.width,height:s.bottom-e.bottom},left:{width:e.left-s.left,height:s.height}},c=Object.keys(a).map((function(t){return x({key:t},a[t],{area:L(a[t])})})).sort((function(t,e){return e.area-t.area})),l=c.filter((function(t){var e=t.width,i=t.height;return e>=n.clientWidth&&i>=n.clientHeight})),f=l.length>0?l[0].key:c[0].key,u=t.split("-")[1];return f+(u?"-"+u:"")}function F(t,e,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=i?N(e):m(e,l(n));return P(n,r,i)}function M(t){var e=t.ownerDocument.defaultView.getComputedStyle(t),n=parseFloat(e.marginTop||0)+parseFloat(e.marginBottom||0),i=parseFloat(e.marginLeft||0)+parseFloat(e.marginRight||0);return{width:t.offsetWidth+i,height:t.offsetHeight+n}}function I(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,(function(t){return e[t]}))}function A(t,e,n){n=n.split("-")[0];var i=M(t),r={width:i.width,height:i.height},o=-1!==["right","left"].indexOf(n),s=o?"top":"left",a=o?"left":"top",c=o?"height":"width",l=o?"width":"height";return r[s]=e[s]+e[c]/2-i[c]/2,r[a]=n===a?e[a]-i[l]:e[I(a)],r}function $(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function _(t,e,n){return(void 0===n?t:t.slice(0,function(t,e,n){if(Array.prototype.findIndex)return t.findIndex((function(t){return t[e]===n}));var i=$(t,(function(t){return t[e]===n}));return t.indexOf(i)}(t,"name",n))).forEach((function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var n=t.function||t.fn;t.enabled&&o(n)&&(e.offsets.popper=k(e.offsets.popper),e.offsets.reference=k(e.offsets.reference),e=n(e,t))})),e}function B(){if(!this.state.isDestroyed){var t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=F(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=S(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=A(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=_(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}function W(t,e){return t.some((function(t){var n=t.name;return t.enabled&&n===e}))}function V(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),i=0;i<e.length;i++){var r=e[i],o=r?""+r+n:t;if(void 0!==document.body.style[o])return o}return null}function z(){return this.state.isDestroyed=!0,W(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[V("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function R(t){var e=t.ownerDocument;return e?e.defaultView:window}function Y(t,e,n,i){n.updateBound=i,R(t).addEventListener("resize",n.updateBound,{passive:!0});var r=c(t);return function t(e,n,i,r){var o="BODY"===e.nodeName,s=o?e.ownerDocument.defaultView:e;s.addEventListener(n,i,{passive:!0}),o||t(c(s.parentNode),n,i,r),r.push(s)}(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function q(){this.state.eventsEnabled||(this.state=Y(this.reference,this.options,this.state,this.scheduleUpdate))}function U(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,R(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach((function(t){t.removeEventListener("scroll",e.updateBound)})),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function X(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function K(t,e){Object.keys(e).forEach((function(n){var i="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&X(e[n])&&(i="px"),t.style[n]=e[n]+i}))}var G=n&&/Firefox/i.test(navigator.userAgent);function Z(t,e,n){var i=$(t,(function(t){return t.name===e})),r=!!i&&t.some((function(t){return t.name===n&&t.enabled&&t.order<i.order}));if(!r){var o="`"+e+"`",s="`"+n+"`";console.warn(s+" modifier is required by "+o+" modifier in order to work, be sure to include it before "+o+"!")}return r}var Q=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],J=Q.slice(3);function tt(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=J.indexOf(t),i=J.slice(n+1).concat(J.slice(0,n));return e?i.reverse():i}var et="flip",nt="clockwise",it="counterclockwise";function rt(t,e,n,i){var r=[0,0],o=-1!==["right","left"].indexOf(i),s=t.split(/(\+|\-)/).map((function(t){return t.trim()})),a=s.indexOf($(s,(function(t){return-1!==t.search(/,|\s/)})));s[a]&&-1===s[a].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var c=/\s*,\s*|\s+/,l=-1!==a?[s.slice(0,a).concat([s[a].split(c)[0]]),[s[a].split(c)[1]].concat(s.slice(a+1))]:[s];return(l=l.map((function(t,i){var r=(1===i?!o:o)?"height":"width",s=!1;return t.reduce((function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,s=!0,t):s?(t[t.length-1]+=e,s=!1,t):t.concat(e)}),[]).map((function(t){return function(t,e,n,i){var r=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+r[1],s=r[2];if(!o)return t;if(0===s.indexOf("%")){var a=void 0;switch(s){case"%p":a=n;break;case"%":case"%r":default:a=i}return k(a)[e]/100*o}if("vh"===s||"vw"===s){return("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o}return o}(t,r,e,n)}))}))).forEach((function(t,e){t.forEach((function(n,i){X(n)&&(r[e]+=n*("-"===t[i-1]?-1:1))}))})),r}var ot={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,n=e.split("-")[0],i=e.split("-")[1];if(i){var r=t.offsets,o=r.reference,s=r.popper,a=-1!==["bottom","top"].indexOf(n),c=a?"left":"top",l=a?"width":"height",f={start:E({},c,o[c]),end:E({},c,o[c]+o[l]-s[l])};t.offsets.popper=x({},s,f[i])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var n=e.offset,i=t.placement,r=t.offsets,o=r.popper,s=r.reference,a=i.split("-")[0],c=void 0;return c=X(+n)?[+n,0]:rt(n,o,s,a),"left"===a?(o.top+=c[0],o.left-=c[1]):"right"===a?(o.top+=c[0],o.left+=c[1]):"top"===a?(o.left+=c[0],o.top-=c[1]):"bottom"===a&&(o.left+=c[0],o.top+=c[1]),t.popper=o,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,e){var n=e.boundariesElement||d(t.instance.popper);t.instance.reference===n&&(n=d(n));var i=V("transform"),r=t.instance.popper.style,o=r.top,s=r.left,a=r[i];r.top="",r.left="",r[i]="";var c=H(t.instance.popper,t.instance.reference,e.padding,n,t.positionFixed);r.top=o,r.left=s,r[i]=a,e.boundaries=c;var l=e.priority,f=t.offsets.popper,u={primary:function(t){var n=f[t];return f[t]<c[t]&&!e.escapeWithReference&&(n=Math.max(f[t],c[t])),E({},t,n)},secondary:function(t){var n="right"===t?"left":"top",i=f[n];return f[t]>c[t]&&!e.escapeWithReference&&(i=Math.min(f[n],c[t]-("right"===t?f.width:f.height))),E({},n,i)}};return l.forEach((function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";f=x({},f,u[e](t))})),t.offsets.popper=f,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,i=e.reference,r=t.placement.split("-")[0],o=Math.floor,s=-1!==["top","bottom"].indexOf(r),a=s?"right":"bottom",c=s?"left":"top",l=s?"width":"height";return n[a]<o(i[c])&&(t.offsets.popper[c]=o(i[c])-n[l]),n[c]>o(i[a])&&(t.offsets.popper[c]=o(i[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){var n;if(!Z(t.instance.modifiers,"arrow","keepTogether"))return t;var i=e.element;if("string"==typeof i){if(!(i=t.instance.popper.querySelector(i)))return t}else if(!t.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var r=t.placement.split("-")[0],o=t.offsets,a=o.popper,c=o.reference,l=-1!==["left","right"].indexOf(r),f=l?"height":"width",u=l?"Top":"Left",p=u.toLowerCase(),d=l?"left":"top",h=l?"bottom":"right",m=M(i)[f];c[h]-m<a[p]&&(t.offsets.popper[p]-=a[p]-(c[h]-m)),c[p]+m>a[h]&&(t.offsets.popper[p]+=c[p]+m-a[h]),t.offsets.popper=k(t.offsets.popper);var b=c[p]+c[f]/2-m/2,v=s(t.instance.popper),g=parseFloat(v["margin"+u]),O=parseFloat(v["border"+u+"Width"]),w=b-t.offsets.popper[p]-g-O;return w=Math.max(Math.min(a[f]-m,w),0),t.arrowElement=i,t.offsets.arrow=(E(n={},p,Math.round(w)),E(n,d,""),n),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,e){if(W(t.instance.modifiers,"inner"))return t;if(t.flipped&&t.placement===t.originalPlacement)return t;var n=H(t.instance.popper,t.instance.reference,e.padding,e.boundariesElement,t.positionFixed),i=t.placement.split("-")[0],r=I(i),o=t.placement.split("-")[1]||"",s=[];switch(e.behavior){case et:s=[i,r];break;case nt:s=tt(i);break;case it:s=tt(i,!0);break;default:s=e.behavior}return s.forEach((function(a,c){if(i!==a||s.length===c+1)return t;i=t.placement.split("-")[0],r=I(i);var l=t.offsets.popper,f=t.offsets.reference,u=Math.floor,p="left"===i&&u(l.right)>u(f.left)||"right"===i&&u(l.left)<u(f.right)||"top"===i&&u(l.bottom)>u(f.top)||"bottom"===i&&u(l.top)<u(f.bottom),d=u(l.left)<u(n.left),h=u(l.right)>u(n.right),m=u(l.top)<u(n.top),b=u(l.bottom)>u(n.bottom),v="left"===i&&d||"right"===i&&h||"top"===i&&m||"bottom"===i&&b,g=-1!==["top","bottom"].indexOf(i),O=!!e.flipVariations&&(g&&"start"===o&&d||g&&"end"===o&&h||!g&&"start"===o&&m||!g&&"end"===o&&b),w=!!e.flipVariationsByContent&&(g&&"start"===o&&h||g&&"end"===o&&d||!g&&"start"===o&&b||!g&&"end"===o&&m),y=O||w;(p||v||y)&&(t.flipped=!0,(p||v)&&(i=s[c+1]),y&&(o=function(t){return"end"===t?"start":"start"===t?"end":t}(o)),t.placement=i+(o?"-"+o:""),t.offsets.popper=x({},t.offsets.popper,A(t.instance.popper,t.offsets.reference,t.placement)),t=_(t.instance.modifiers,t,"flip"))})),t},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],i=t.offsets,r=i.popper,o=i.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return r[s?"left":"top"]=o[n]-(a?r[s?"width":"height"]:0),t.placement=I(e),t.offsets.popper=k(r),t}},hide:{order:800,enabled:!0,fn:function(t){if(!Z(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,n=$(t.instance.modifiers,(function(t){return"preventOverflow"===t.name})).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,i=e.y,r=t.offsets.popper,o=$(t.instance.modifiers,(function(t){return"applyStyle"===t.name})).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s=void 0!==o?o:e.gpuAcceleration,a=d(t.instance.popper),c=C(a),l={position:r.position},f=function(t,e){var n=t.offsets,i=n.popper,r=n.reference,o=Math.round,s=Math.floor,a=function(t){return t},c=o(r.width),l=o(i.width),f=-1!==["left","right"].indexOf(t.placement),u=-1!==t.placement.indexOf("-"),p=e?f||u||c%2==l%2?o:s:a,d=e?o:a;return{left:p(c%2==1&&l%2==1&&!u&&e?i.left-1:i.left),top:d(i.top),bottom:d(i.bottom),right:p(i.right)}}(t,window.devicePixelRatio<2||!G),u="bottom"===n?"top":"bottom",p="right"===i?"left":"right",h=V("transform"),m=void 0,b=void 0;if(b="bottom"===u?"HTML"===a.nodeName?-a.clientHeight+f.bottom:-c.height+f.bottom:f.top,m="right"===p?"HTML"===a.nodeName?-a.clientWidth+f.right:-c.width+f.right:f.left,s&&h)l[h]="translate3d("+m+"px, "+b+"px, 0)",l[u]=0,l[p]=0,l.willChange="transform";else{var v="bottom"===u?-1:1,g="right"===p?-1:1;l[u]=b*v,l[p]=m*g,l.willChange=u+", "+p}var O={"x-placement":t.placement};return t.attributes=x({},O,t.attributes),t.styles=x({},l,t.styles),t.arrowStyles=x({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){var e,n;return K(t.instance.popper,t.styles),e=t.instance.popper,n=t.attributes,Object.keys(n).forEach((function(t){!1!==n[t]?e.setAttribute(t,n[t]):e.removeAttribute(t)})),t.arrowElement&&Object.keys(t.arrowStyles).length&&K(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,n,i,r){var o=F(r,e,t,n.positionFixed),s=S(n.placement,o,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",s),K(e,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},st=function(){function t(e,n){var i=this,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};y(this,t),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=r(this.update.bind(this)),this.options=x({},t.Defaults,s),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=n&&n.jquery?n[0]:n,this.options.modifiers={},Object.keys(x({},t.Defaults.modifiers,s.modifiers)).forEach((function(e){i.options.modifiers[e]=x({},t.Defaults.modifiers[e]||{},s.modifiers?s.modifiers[e]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(t){return x({name:t},i.options.modifiers[t])})).sort((function(t,e){return t.order-e.order})),this.modifiers.forEach((function(t){t.enabled&&o(t.onLoad)&&t.onLoad(i.reference,i.popper,i.options,t,i.state)})),this.update();var a=this.options.eventsEnabled;a&&this.enableEventListeners(),this.state.eventsEnabled=a}return j(t,[{key:"update",value:function(){return B.call(this)}},{key:"destroy",value:function(){return z.call(this)}},{key:"enableEventListeners",value:function(){return q.call(this)}},{key:"disableEventListeners",value:function(){return U.call(this)}}]),t}();st.Utils=("undefined"!=typeof window?window:t).PopperUtils,st.placements=Q,st.Defaults=ot,e.a=st}).call(this,n("yLpj"))},A2ZE:function(t,e,n){var i=n("HAuM");t.exports=function(t,e,n){if(i(t),void 0===e)return t;switch(n){case 0:return function(){return t.call(e)};case 1:return function(n){return t.call(e,n)};case 2:return function(n,i){return t.call(e,n,i)};case 3:return function(n,i,r){return t.call(e,n,i,r)}}return function(){return t.apply(e,arguments)}}},HAuM:function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t}},ZfDv:function(t,e,n){var i=n("hh1v"),r=n("6LWA"),o=n("tiKp")("species");t.exports=function(t,e){var n;return r(t)&&("function"!=typeof(n=t.constructor)||n!==Array&&!r(n.prototype)?i(n)&&null===(n=n[o])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===e?0:e)}},la6Y:function(t,e,n){"use strict";n.d(e,"b",(function(){return T})),n.d(e,"a",(function(){return D}));var i=n("8L3F"),r=n("XuX8"),o=n.n(r),s=n("xjcK"),a=n("AFYn"),c=n("m/oX"),l=n("pyNs"),f=n("yoge"),u=n("bUBZ"),p=n("kGy3"),d=n("a3f1"),h=n("ex6f"),m=n("2C+6"),b=n("z3V6"),v=n("aGvM"),g=o.a.extend({data:function(){return{listenForClickOut:!1}},watch:{listenForClickOut:function(t,e){t!==e&&(Object(d.a)(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,a.S),t&&Object(d.b)(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,a.S))}},beforeCreate:function(){this.clickOutElement=null,this.clickOutEventName=null},mounted:function(){this.clickOutElement||(this.clickOutElement=document),this.clickOutEventName||(this.clickOutEventName="click"),this.listenForClickOut&&Object(d.b)(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,a.S)},beforeDestroy:function(){Object(d.a)(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,a.S)},methods:{isClickOut:function(t){return!Object(p.f)(this.$el,t.target)},_clickOutHandler:function(t){this.clickOutHandler&&this.isClickOut(t)&&this.clickOutHandler(t)}}}),O=o.a.extend({data:function(){return{listenForFocusIn:!1}},watch:{listenForFocusIn:function(t,e){t!==e&&(Object(d.a)(this.focusInElement,"focusin",this._focusInHandler,a.S),t&&Object(d.b)(this.focusInElement,"focusin",this._focusInHandler,a.S))}},beforeCreate:function(){this.focusInElement=null},mounted:function(){this.focusInElement||(this.focusInElement=document),this.listenForFocusIn&&Object(d.b)(this.focusInElement,"focusin",this._focusInHandler,a.S)},beforeDestroy:function(){Object(d.a)(this.focusInElement,"focusin",this._focusInHandler,a.S)},methods:{_focusInHandler:function(t){this.focusInHandler&&this.focusInHandler(t)}}}),w=n("kO/s"),y=n("YC3Q");function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function E(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach((function(e){x(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function x(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var k=Object(d.e)(s.s,a.O),C=Object(d.e)(s.s,a.s),P=[".dropdown-item",".b-dropdown-form"].map((function(t){return"".concat(t,":not(.disabled):not([disabled])")})).join(", "),T=Object(b.d)(Object(m.m)(E(E({},w.b),{},{boundary:Object(b.c)([f.c,l.t],"scrollParent"),disabled:Object(b.c)(l.g,!1),dropleft:Object(b.c)(l.g,!1),dropright:Object(b.c)(l.g,!1),dropup:Object(b.c)(l.g,!1),noFlip:Object(b.c)(l.g,!1),offset:Object(b.c)(l.o,0),popperOpts:Object(b.c)(l.p,{}),right:Object(b.c)(l.g,!1)})),s.s),D=o.a.extend({mixins:[w.a,y.a,g,O],provide:function(){return{bvDropdown:this}},inject:{bvNavbar:{default:null}},props:T,data:function(){return{visible:!1,visibleChangePrevented:!1}},computed:{inNavbar:function(){return!Object(h.g)(this.bvNavbar)},toggler:function(){var t=this.$refs.toggle;return t?t.$el||t:null},directionClass:function(){return this.dropup?"dropup":this.dropright?"dropright":this.dropleft?"dropleft":""},boundaryClass:function(){return"scrollParent"===this.boundary||this.inNavbar?"":"position-static"}},watch:{visible:function(t,e){if(this.visibleChangePrevented)this.visibleChangePrevented=!1;else if(t!==e){var n=t?a.N:a.t,i=new u.a(n,{cancelable:!0,vueTarget:this,target:this.$refs.menu,relatedTarget:null,componentId:this.safeId?this.safeId():this.id||null});if(this.emitEvent(i),i.defaultPrevented)return this.visibleChangePrevented=!0,this.visible=e,void this.$off(a.s,this.focusToggler);t?this.showMenu():this.hideMenu()}},disabled:function(t,e){t!==e&&t&&this.visible&&(this.visible=!1)}},created:function(){this.$_popper=null,this.$_hideTimeout=null},deactivated:function(){this.visible=!1,this.whileOpenListen(!1),this.destroyPopper()},beforeDestroy:function(){this.visible=!1,this.whileOpenListen(!1),this.destroyPopper(),this.clearHideTimeout()},methods:{emitEvent:function(t){var e=t.type;this.emitOnRoot(Object(d.e)(s.s,e),t),this.$emit(e,t)},showMenu:function(){var t=this;if(!this.disabled){if(!this.inNavbar)if(void 0===i.a)Object(v.a)("Popper.js not found. Falling back to CSS positioning",s.s);else{var e=this.dropup&&this.right||this.split?this.$el:this.$refs.toggle;e=e.$el||e,this.createPopper(e)}this.emitOnRoot(k,this),this.whileOpenListen(!0),this.$nextTick((function(){t.focusMenu(),t.$emit(a.O)}))}},hideMenu:function(){this.whileOpenListen(!1),this.emitOnRoot(C,this),this.$emit(a.s),this.destroyPopper()},createPopper:function(t){this.destroyPopper(),this.$_popper=new i.a(t,this.$refs.menu,this.getPopperConfig())},destroyPopper:function(){this.$_popper&&this.$_popper.destroy(),this.$_popper=null},updatePopper:function(){try{this.$_popper.scheduleUpdate()}catch(t){}},clearHideTimeout:function(){clearTimeout(this.$_hideTimeout),this.$_hideTimeout=null},getPopperConfig:function(){var t="bottom-start";this.dropup?t=this.right?"top-end":"top-start":this.dropright?t="right-start":this.dropleft?t="left-start":this.right&&(t="bottom-end");var e={placement:t,modifiers:{offset:{offset:this.offset||0},flip:{enabled:!this.noFlip}}},n=this.boundary;return n&&(e.modifiers.preventOverflow={boundariesElement:n}),Object(m.i)(e,this.popperOpts||{})},whileOpenListen:function(t){this.listenForClickOut=t,this.listenForFocusIn=t;var e=t?"$on":"$off";this.$root[e](k,this.rootCloseListener)},rootCloseListener:function(t){t!==this&&(this.visible=!1)},show:function(){var t=this;this.disabled||Object(p.B)((function(){t.visible=!0}))},hide:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.disabled||(this.visible=!1,t&&this.$once(a.s,this.focusToggler))},toggle:function(t){var e=t=t||{},n=e.type,i=e.keyCode;("click"===n||"keydown"===n&&-1!==[c.c,c.j,c.a].indexOf(i))&&(this.disabled?this.visible=!1:(this.$emit(a.Q,t),Object(d.f)(t),this.visible?this.hide(!0):this.show()))},onMousedown:function(t){Object(d.f)(t,{propagation:!1})},onKeydown:function(t){var e=t.keyCode;e===c.d?this.onEsc(t):e===c.a?this.focusNext(t,!1):e===c.k&&this.focusNext(t,!0)},onEsc:function(t){this.visible&&(this.visible=!1,Object(d.f)(t),this.$once(a.s,this.focusToggler))},onSplitClick:function(t){this.disabled?this.visible=!1:this.$emit(a.f,t)},hideHandler:function(t){var e=this,n=t.target;!this.visible||Object(p.f)(this.$refs.menu,n)||Object(p.f)(this.toggler,n)||(this.clearHideTimeout(),this.$_hideTimeout=setTimeout((function(){return e.hide()}),this.inNavbar?300:0))},clickOutHandler:function(t){this.hideHandler(t)},focusInHandler:function(t){this.hideHandler(t)},focusNext:function(t,e){var n=this,i=t.target;!this.visible||t&&Object(p.e)(".dropdown form",i)||(Object(d.f)(t),this.$nextTick((function(){var t=n.getItems();if(!(t.length<1)){var r=t.indexOf(i);e&&r>0?r--:!e&&r<t.length-1&&r++,r<0&&(r=0),n.focusItem(r,t)}})))},focusItem:function(t,e){var n=e.find((function(e,n){return n===t}));Object(p.d)(n)},getItems:function(){return(Object(p.D)(P,this.$refs.menu)||[]).filter(p.u)},focusMenu:function(){Object(p.d)(this.$refs.menu)},focusToggler:function(){var t=this;this.$nextTick((function(){Object(p.d)(t.toggler)}))}}})},nqqA:function(t,e,n){"use strict";n.d(e,"a",(function(){return g}));var i=n("XuX8"),r=n.n(i),o=n("xjcK"),s=n("AFYn"),a=n("pyNs"),c=n("kGy3"),l=n("2C+6"),f=n("z3V6"),u=n("STsD"),p=n("jBgq"),d=n("qlm0");function h(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,i)}return n}function m(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?h(Object(n),!0).forEach((function(e){b(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function b(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var v=Object(f.d)(Object(l.m)(m(m({},Object(l.j)(d.b,["event","routerTag"])),{},{linkClass:Object(f.c)(a.e),variant:Object(f.c)(a.t)})),o.u),g=r.a.extend({name:o.u,mixins:[u.a,p.a],inject:{bvDropdown:{default:null}},inheritAttrs:!1,props:v,computed:{computedAttrs:function(){return m(m({},this.bvAttrs),{},{role:"menuitem"})}},methods:{closeDropdown:function(){var t=this;Object(c.B)((function(){t.bvDropdown&&t.bvDropdown.hide(!0)}))},onClick:function(t){this.$emit(s.f,t),this.closeDropdown()}},render:function(t){var e=this.linkClass,n=this.variant,i=this.active,r=this.disabled,o=this.onClick,s=this.bvAttrs;return t("li",{class:s.class,style:s.style,attrs:{role:"presentation"}},[t(d.a,{staticClass:"dropdown-item",class:[e,b({},"text-".concat(n),n&&!(i||r))],props:this.$props,attrs:this.computedAttrs,on:{click:o},ref:"item"},this.normalizeSlot())])}})},tycR:function(t,e,n){var i=n("A2ZE"),r=n("RK3t"),o=n("ewvW"),s=n("UMSQ"),a=n("ZfDv"),c=[].push,l=function(t){var e=1==t,n=2==t,l=3==t,f=4==t,u=6==t,p=7==t,d=5==t||u;return function(h,m,b,v){for(var g,O,w=o(h),y=r(w),j=i(m,b,3),E=s(y.length),x=0,k=v||a,C=e?k(h,E):n||p?k(h,0):void 0;E>x;x++)if((d||x in y)&&(O=j(g=y[x],x,w),t))if(e)C[x]=O;else if(O)switch(t){case 3:return!0;case 5:return g;case 6:return x;case 2:c.call(C,g)}else switch(t){case 4:return!1;case 7:c.call(C,g)}return u?-1:l||f?f:C}};t.exports={forEach:l(0),map:l(1),filter:l(2),some:l(3),every:l(4),find:l(5),findIndex:l(6),filterOut:l(7)}}}]);