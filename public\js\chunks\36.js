(window.webpackJsonp=window.webpackJsonp||[]).push([[36],{"+Tzp":function(a,t,r){"use strict";r.r(t);var n=r("HaE+"),e=(r("sMBO"),r("6cQw"),r("o0o1")),l=r.n(e),i=r("GUe+"),o=r("giZP"),c=r("R5cT"),d=r("xD+F"),p=r("oVt+"),f=r("sove"),s=r("IF94"),k=r("KBId"),v=r("SWgu"),u=r("1uQM"),m=r("g2Gq"),h=r("Ed67"),y=r("KaE5"),b=r("w48C"),_=r.n(b),g=r("vDqi"),w=r.n(g),x={components:{BButton:i.a,BFormGroup:o.a,BFormInput:c.a,BFormFile:d.a,BRow:p.a,BCol:f.a,BCard:s.a,BCardImg:k.a,BCardTitle:v.a,BCardText:u.a,flatPickr:_.a,BFormSelect:m.a,BForm:h.a,BTable:y.a},data:function(){return{countries:[],ads:[],form:{title:"",redirect:"",hide:"",location:"",country:"",media:null}}},mounted:function(){var a=this;return Object(n.a)(l.a.mark((function t(){var r,n;return l.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,w.a.get("/api/countries");case 3:return r=t.sent,a.countries=r.data,t.next=7,w.a.get("/api/ads");case 7:n=t.sent,a.ads=n.data,t.next=14;break;case 11:t.prev=11,t.t0=t.catch(0),alert("حدث خطأ ما");case 14:case"end":return t.stop()}}),t,null,[[0,11]])})))()},methods:{addAd:function(){var a=new FormData;a.append("media",this.form.media,this.form.media.name);var t=JSON.stringify({title:this.form.title,redirect:this.form.redirect,hide:this.form.hide,location:this.form.location,country:this.form.country});a.append("data",t);var r={headers:{"content-type":"multipart/form-data",token:JSON.parse(localStorage.getItem("MatarAdmin")).token}};w.a.post("/api/admin/add-ad",a,r).then((function(a){alert("تم اضافة الاعلان"),location.reload()})).catch((function(a){alert("حدث خطأ ما")}))},deleteAd:function(a){window.confirm("هل انت متأكد من الحذف")&&w.a.post("/api/admin/delete-ad",{id:a},{headers:{token:JSON.parse(localStorage.getItem("MatarAdmin")).token}}).then((function(a){alert("تم حذف الاعلان"),location.reload()})).catch((function(a){alert("حدث خطأ ما")}))}}},R=(r("NPbe"),r("KHd+")),S=Object(R.a)(x,(function(){var a=this,t=a.$createElement,r=a._self._c||t;return r("div",[r("b-form",{on:{submit:function(t){return t.preventDefault(),a.addAd.apply(null,arguments)}}},[r("b-form-group",{attrs:{label:"عنوان الاعلان","label-for":"v-title"}},[r("b-form-input",{attrs:{id:"v-title",placeholder:"عنوان الاعلان",required:""},model:{value:a.form.title,callback:function(t){a.$set(a.form,"title",t)},expression:"form.title"}})],1),a._v(" "),r("b-form-group",{attrs:{label:"رابط التوجيه","label-for":"v-url"}},[r("b-form-input",{attrs:{id:"v-url",type:"url",placeholder:"رابط التوجيه"},model:{value:a.form.redirect,callback:function(t){a.$set(a.form,"redirect",t)},expression:"form.redirect"}})],1),a._v(" "),r("b-form-group",{attrs:{label:"تاريخ الاختفاء","label-for":"v-hideDate"}},[r("flat-pickr",{staticClass:"form-control",attrs:{id:"v-hideDate",config:{enableTime:!0,dateFormat:"Y-m-d H:i:s"}},model:{value:a.form.hide,callback:function(t){a.$set(a.form,"hide",t)},expression:"form.hide"}})],1),a._v(" "),r("b-form-group",{attrs:{label:"مكان الاعلان","label-for":"v-location"}},[r("b-form-select",{attrs:{id:"v-location"},model:{value:a.form.location,callback:function(t){a.$set(a.form,"location",t)},expression:"form.location"}},[r("option",{attrs:{value:"1"}},[a._v("قسم التوقعات")]),a._v(" "),r("option",{attrs:{value:"2"}},[a._v("قسم وسائط الطقس")]),a._v(" "),r("option",{attrs:{value:"0"}},[a._v("كلاهما")])])],1),a._v(" "),r("b-form-group",{attrs:{label:"الدولة","label-for":"v-country"}},[r("b-form-select",{attrs:{multiple:"",id:"v-country"},model:{value:a.form.country,callback:function(t){a.$set(a.form,"country",t)},expression:"form.country"}},[r("option",[a._v("عمان")]),a._v(" "),r("option",[a._v("الأمارات")]),a._v(" "),r("option",[a._v("السعودية")]),a._v(" "),r("option",[a._v("قطر")]),a._v(" "),r("option",[a._v("البحرين")]),a._v(" "),r("option",[a._v("الكويت")]),a._v(" "),r("option",[a._v("اليمن")]),a._v(" "),r("option",[a._v("العراق")]),a._v(" "),r("option",[a._v("الاردن")]),a._v(" "),r("option",[a._v("لبنان")]),a._v(" "),r("option",[a._v("سوريا")]),a._v(" "),r("option",[a._v("فلسطين")]),a._v(" "),r("option",[a._v("تونس")]),a._v(" "),r("option",[a._v("ليبيا")]),a._v(" "),r("option",[a._v("مصر")]),a._v(" "),r("option",[a._v("السودان")]),a._v(" "),r("option",[a._v("المغرب")]),a._v(" "),r("option",[a._v("الجزائر")]),a._v(" "),r("option",[a._v("موريتانيا")])])],1),a._v(" "),r("b-form-group",{attrs:{label:"صورة / فيديو","label-for":"v-pic"}},[r("b-form-file",{attrs:{placeholder:"اختر صورة / فيديو او اسحبه الي هنا","drop-placeholder":"افلت الملف هنا...",id:"pic",accept:"image/jpeg, image/png, image/jpg, video/mp4, video/flv, video/3gp, video/mov, video/avi, video/wmv",required:""},model:{value:a.form.media,callback:function(t){a.$set(a.form,"media",t)},expression:"form.media"}})],1),a._v(" "),r("b-button",{staticClass:"w-100",attrs:{type:"submit",variant:"primary"}},[a._v("\n            اضافة\n        ")])],1),a._v(" "),r("br"),r("br"),r("br"),a._v(" "),a.ads.length>0?r("h4",[a._v("الاعلانات المنشورة")]):a._e(),a._v(" "),r("br"),a._v(" "),r("b-row",a._l(a.ads,(function(t){return r("b-col",{key:t.id,attrs:{md:"5",lg:"3"}},[r("div",{staticClass:"card"},[r("img",{staticClass:"card-img-top",staticStyle:{height:"130px","object-fit":"contain"},attrs:{src:"/storage/ads/"+t.media}}),a._v(" "),r("div",{staticClass:"card-body"},[r("b-card-title",[a._v(a._s(t.title))]),a._v(" "),r("b-button",{directives:[{name:"b-modal",rawName:"v-b-modal",value:"modal-ad-"+t.id,expression:"`modal-ad-${ad.id}`"}],staticClass:"w-100",attrs:{variant:"primary"}},[a._v("\n                        تفاصيل\n                    ")]),a._v(" "),r("b-modal",{attrs:{id:"modal-ad-"+t.id,scrollable:"",title:"تفاصيل الاعلان","hide-footer":""}},[r("b-card-text",[r("div",{staticStyle:{"line-height":"2.9rem"}},[r("div",[r("span",{staticStyle:{"font-weight":"bold"}},[a._v("\n                                        تاريخ الاختفاء :\n                                    ")]),a._v(" "),r("span",[a._v(a._s(t.hide))])]),a._v(" "),r("div",[r("span",{staticStyle:{"font-weight":"bold"}},[a._v("\n                                        مكان النشر :\n                                    ")]),a._v(" "),"1"==t.location?r("span",[a._v("\n                                        قسم التوقعات\n                                    ")]):a._e(),a._v(" "),"2"==t.location?r("span",[a._v("\n                                        قسم وسائط الطقس\n                                    ")]):a._e(),a._v(" "),"0"==t.location?r("span",[a._v("\n                                        كلا القسمين\n                                    ")]):a._e()]),a._v(" "),r("div",[r("span",{staticStyle:{"font-weight":"bold"}},[a._v("\n                                        الدولة :\n                                    ")]),a._v(" "),r("span",[a._v(a._s(t.country))])]),a._v(" "),r("div",[r("span",{staticStyle:{"font-weight":"bold"}},[a._v("\n                                        رابط التحويل :\n                                    ")]),a._v(" "),r("span",[a._v(a._s(t.redirect))])])]),a._v(" "),r("div",{staticStyle:{"line-height":"2.9rem"}},[r("div",{staticStyle:{"text-align":"center"}},[r("span",{staticStyle:{"font-weight":"bold"}},[a._v("\n                                        اجمالي عدد المشاهدات :\n                                    ")]),a._v(" "),r("span",[a._v(a._s(t.details.filter((function(a){return"view"==a.type})).length))])])]),a._v(" "),r("table",{staticClass:"w-100",staticStyle:{"text-align":"center"}},[r("thead",{staticStyle:{background:"#f3f2f7"}},[r("tr",[r("th",{staticStyle:{color:"#5e5873"}},[a._v("\n                                            الدولة\n                                        ")]),a._v(" "),r("th",{staticStyle:{color:"#5e5873"}},[a._v("\n                                            مشاهدات\n                                        ")])])]),a._v(" "),a._l(t.details.filter((function(a){return"view"==a.type})),(function(t){return r("tr",{key:t.id},[r("th",[r("span",{staticClass:"font-weight-bold"},[a._v("\n                                            "+a._s(t.country)+"\n                                        ")])]),a._v(" "),r("td",[a._v(a._s(t.count))])])}))],2),a._v(" "),r("br"),a._v(" "),r("div",{staticStyle:{"line-height":"2.9rem"}},[r("div",{staticStyle:{"text-align":"center"}},[r("span",{staticStyle:{"font-weight":"bold"}},[a._v("\n                                        اجمالي عدد النقرات :\n                                    ")]),a._v(" "),r("span",[a._v(a._s(t.details.filter((function(a){return"click"==a.type})).length))])])]),a._v(" "),r("table",{staticClass:"w-100",staticStyle:{"text-align":"center"}},[r("thead",{staticStyle:{background:"#f3f2f7"}},[r("tr",[r("th",{staticStyle:{color:"#5e5873"}},[a._v("\n                                            الدولة\n                                        ")]),a._v(" "),r("th",{staticStyle:{color:"#5e5873"}},[a._v("\n                                            النقرات\n                                        ")])])]),a._v(" "),a._l(t.details.filter((function(a){return"click"==a.type})),(function(t){return r("tr",{key:t.id},[r("th",[r("span",{staticClass:"font-weight-bold"},[a._v("\n                                            "+a._s(t.country)+"\n                                        ")])]),a._v(" "),r("td",[a._v(a._s(t.count))])])}))],2)])],1),a._v(" "),r("br"),r("br"),a._v(" "),r("b-button",{staticClass:"w-100",attrs:{variant:"danger"},on:{click:function(r){return a.deleteAd(t.id)}}},[a._v("\n                        حذف\n                    ")])],1)])])})),1)],1)}),[],!1,null,null,null);t.default=S.exports},"07GY":function(a,t,r){var n=r("yiL4");"string"==typeof n&&(n=[[a.i,n,""]]);var e={hmr:!0,transform:void 0,insertInto:void 0};r("aET+")(n,e);n.locals&&(a.exports=n.locals)},NPbe:function(a,t,r){"use strict";r("07GY")},yiL4:function(a,t,r){(t=a.exports=r("I1BE")(!1)).i(r("k0tF"),""),t.push([a.i,".flatpickr-calendar .flatpickr-day {\n  color: #6e6b7b;\n}\n[dir] .flatpickr-calendar .flatpickr-day.today {\n  border-color: #7367f0;\n}\n.flatpickr-calendar .flatpickr-day.today:hover {\n  color: #6e6b7b;\n}\n[dir] .flatpickr-calendar .flatpickr-day.today:hover {\n  background: transparent;\n}\n.flatpickr-calendar .flatpickr-day.selected, .flatpickr-calendar .flatpickr-day.selected:hover {\n  color: #fff;\n}\n[dir] .flatpickr-calendar .flatpickr-day.selected, [dir] .flatpickr-calendar .flatpickr-day.selected:hover {\n  background: #7367f0;\n  border-color: #7367f0;\n}\n[dir] .flatpickr-calendar .flatpickr-day.inRange, [dir] .flatpickr-calendar .flatpickr-day.inRange:hover {\n  background: #f3f2fe;\n  border-color: #f3f2fe;\n}\n[dir=ltr] .flatpickr-calendar .flatpickr-day.inRange, [dir=ltr] .flatpickr-calendar .flatpickr-day.inRange:hover {\n  box-shadow: -5px 0 0 #f3f2fe, 5px 0 0 #f3f2fe;\n}\n[dir=rtl] .flatpickr-calendar .flatpickr-day.inRange, [dir=rtl] .flatpickr-calendar .flatpickr-day.inRange:hover {\n  box-shadow: 5px 0 0 #f3f2fe, -5px 0 0 #f3f2fe;\n}\n.flatpickr-calendar .flatpickr-day.startRange, .flatpickr-calendar .flatpickr-day.endRange, .flatpickr-calendar .flatpickr-day.startRange:hover, .flatpickr-calendar .flatpickr-day.endRange:hover {\n  color: #fff;\n}\n[dir] .flatpickr-calendar .flatpickr-day.startRange, [dir] .flatpickr-calendar .flatpickr-day.endRange, [dir] .flatpickr-calendar .flatpickr-day.startRange:hover, [dir] .flatpickr-calendar .flatpickr-day.endRange:hover {\n  background: #7367f0;\n  border-color: #7367f0;\n}\n[dir=ltr] .flatpickr-calendar .flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)), [dir=ltr] .flatpickr-calendar .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)), [dir=ltr] .flatpickr-calendar .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {\n  box-shadow: -10px 0 0 #7367f0;\n}\n[dir=rtl] .flatpickr-calendar .flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)), [dir=rtl] .flatpickr-calendar .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)), [dir=rtl] .flatpickr-calendar .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {\n  box-shadow: 10px 0 0 #7367f0;\n}\n.flatpickr-calendar .flatpickr-day.flatpickr-disabled, .flatpickr-calendar .flatpickr-day.prevMonthDay, .flatpickr-calendar .flatpickr-day.nextMonthDay {\n  color: #dae1e7;\n}\n[dir] .flatpickr-calendar .flatpickr-day:hover {\n  background: #f6f6f6;\n}\n.flatpickr-calendar:after, .flatpickr-calendar:before {\n  display: none;\n}\n.flatpickr-calendar .flatpickr-months .flatpickr-prev-month, .flatpickr-calendar .flatpickr-months .flatpickr-next-month {\n  top: -5px;\n}\n.flatpickr-calendar .flatpickr-months .flatpickr-prev-month:hover i, .flatpickr-calendar .flatpickr-months .flatpickr-prev-month:hover svg, .flatpickr-calendar .flatpickr-months .flatpickr-next-month:hover i, .flatpickr-calendar .flatpickr-months .flatpickr-next-month:hover svg {\n  fill: #7367f0;\n}\n.flatpickr-calendar .flatpickr-current-month span.cur-month {\n  font-weight: 300;\n}\n[dir] .flatpickr-time input:hover, [dir] .flatpickr-time .flatpickr-am-pm:hover, [dir] .flatpickr-time input:focus, [dir] .flatpickr-time .flatpickr-am-pm:focus {\n  background: #fff;\n}\n[dir] .dark-layout .flatpickr-calendar {\n  background: #161d31;\n  border-color: #161d31;\n  box-shadow: none;\n}\n.dark-layout .flatpickr-calendar .flatpickr-months i, .dark-layout .flatpickr-calendar .flatpickr-months svg {\n  fill: #b4b7bd;\n}\n.dark-layout .flatpickr-calendar .flatpickr-month {\n  color: #b4b7bd;\n}\n[dir=ltr] .dark-layout .flatpickr-calendar .flatpickr-weekwrapper .flatpickr-weeks {\n  box-shadow: 1px 0 0 #3b4253;\n}\n[dir=rtl] .dark-layout .flatpickr-calendar .flatpickr-weekwrapper .flatpickr-weeks {\n  box-shadow: -1px 0 0 #3b4253;\n}\n.dark-layout .flatpickr-calendar .flatpickr-weekday {\n  color: #b4b7bd;\n}\n.dark-layout .flatpickr-calendar .flatpickr-day, .dark-layout .flatpickr-calendar .flatpickr-day.today:hover {\n  color: #b4b7bd;\n}\n.dark-layout .flatpickr-calendar .flatpickr-day.selected {\n  color: #fff;\n}\n.dark-layout .flatpickr-calendar .flatpickr-day.prevMonthDay, .dark-layout .flatpickr-calendar .flatpickr-day.nextMonthDay, .dark-layout .flatpickr-calendar .flatpickr-day.flatpickr-disabled {\n  color: #4e5154 !important;\n}\n[dir] .dark-layout .flatpickr-calendar .flatpickr-day.inRange, [dir] .dark-layout .flatpickr-calendar .flatpickr-day.inRange:hover {\n  background: #283046;\n  border-color: #283046;\n}\n[dir=ltr] .dark-layout .flatpickr-calendar .flatpickr-day.inRange, [dir=ltr] .dark-layout .flatpickr-calendar .flatpickr-day.inRange:hover {\n  box-shadow: -5px 0 0 #283046, 5px 0 0 #283046;\n}\n[dir=rtl] .dark-layout .flatpickr-calendar .flatpickr-day.inRange, [dir=rtl] .dark-layout .flatpickr-calendar .flatpickr-day.inRange:hover {\n  box-shadow: 5px 0 0 #283046, -5px 0 0 #283046;\n}\n.dark-layout .flatpickr-calendar .flatpickr-day:hover:not(.selected):not(.today):not(.startRange):not(.endRange) {\n  color: #b4b7bd;\n}\n[dir] .dark-layout .flatpickr-calendar .flatpickr-day:hover:not(.selected):not(.today):not(.startRange):not(.endRange) {\n  border-color: #283046;\n}\n[dir] .dark-layout .flatpickr-calendar .flatpickr-days .flatpickr-day:hover:not(.selected):not(.today):not(.startRange):not(.endRange) {\n  background: #283046;\n}\n[dir] .dark-layout .flatpickr-calendar .flatpickr-time {\n  border-color: #161d31 !important;\n}\n.dark-layout .flatpickr-calendar .flatpickr-time .numInput, .dark-layout .flatpickr-calendar .flatpickr-time .flatpickr-am-pm {\n  color: #b4b7bd;\n}\n[dir] .dark-layout .flatpickr-calendar .flatpickr-time .numInput:hover, [dir] .dark-layout .flatpickr-calendar .flatpickr-time .flatpickr-am-pm:hover {\n  background: #161d31;\n}\n[dir] .dark-layout .flatpickr-calendar .flatpickr-time .arrowUp:after {\n  border-bottom-color: #b4b7bd;\n}\n[dir] .dark-layout .flatpickr-calendar .flatpickr-time .arrowDown:after {\n  border-top-color: #b4b7bd;\n}\n[dir] .dark-layout .flatpickr-time input:hover, [dir] .dark-layout .flatpickr-time .flatpickr-am-pm:hover, [dir] .dark-layout .flatpickr-time input:focus, [dir] .dark-layout .flatpickr-time .flatpickr-am-pm:focus {\n  background: #161d31;\n}\n.flatpickr-input[readonly], .flatpickr-input ~ .form-control[readonly], .flatpickr-human-friendly[readonly] {\n  opacity: 1 !important;\n}\n[dir] .flatpickr-input[readonly], [dir] .flatpickr-input ~ .form-control[readonly], [dir] .flatpickr-human-friendly[readonly] {\n  background-color: inherit;\n}\n[dir] .flatpickr-weekdays {\n  margin-top: 8px;\n}\n.flatpickr-current-month .flatpickr-monthDropdown-months {\n  -webkit-appearance: none;\n}\n.flatpickr-current-month .flatpickr-monthDropdown-months, .flatpickr-current-month .numInputWrapper {\n  font-size: 1.1rem;\n  transition: all 0.15s ease-out;\n}\n[dir] .flatpickr-current-month .flatpickr-monthDropdown-months, [dir] .flatpickr-current-month .numInputWrapper {\n  border-radius: 4px;\n  padding: 2px;\n}\n.flatpickr-current-month .flatpickr-monthDropdown-months span, .flatpickr-current-month .numInputWrapper span {\n  display: none;\n}\nhtml[dir=rtl] .flatpickr-calendar .flatpickr-prev-month svg, html[dir=rtl] .flatpickr-calendar .flatpickr-next-month svg {\n  transform: rotate(180deg);\n}",""])}}]);