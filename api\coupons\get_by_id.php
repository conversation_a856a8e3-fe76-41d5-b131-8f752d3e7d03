<?php
/**
 * الحصول على كوبون بالمعرف
 * Get Coupon by ID API
 */

require_once '../../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// الحصول على معرف الكوبون من URL
$couponId = $_GET['id'] ?? '';

if (empty($couponId)) {
    errorResponse('معرف الكوبون مطلوب');
}

// البحث عن الكوبون
$sql = "SELECT * FROM coupons WHERE id = ? OR coupon = ?";
$coupon = $db->selectOne($sql, [$couponId, $couponId]);

if (!$coupon) {
    errorResponse('الكوبون غير موجود', 404);
}

// التحقق من صحة الكوبون
if ($coupon['active'] != 1) {
    errorResponse('الكوبون غير نشط', 400);
}

if ($coupon['expire_date'] && strtotime($coupon['expire_date']) < time()) {
    errorResponse('انتهت صلاحية الكوبون', 400);
}

// تنسيق البيانات
if ($coupon['expire_date']) {
    $coupon['formatted_expire_date'] = formatArabicDate($coupon['expire_date']);
    
    // حساب الأيام المتبقية
    $expireTimestamp = strtotime($coupon['expire_date']);
    $currentTimestamp = time();
    $daysLeft = ceil(($expireTimestamp - $currentTimestamp) / (60 * 60 * 24));
    $coupon['days_left'] = max(0, $daysLeft);
}

// الحصول على معلومات المسوق إذا كان الكوبون مرتبط بمسوق
$marketerSql = "SELECT full_name, email, country FROM marketers WHERE coupon = ? AND active = 1";
$marketer = $db->selectOne($marketerSql, [$coupon['coupon']]);

if ($marketer) {
    $coupon['marketer'] = $marketer;
}

successResponse($coupon);
