<?php
/**
 * الحصول على الإعلانات
 * Get Ads API
 */

require_once '../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// الحصول على الإعلانات النشطة
$sql = "SELECT * FROM ads WHERE active = 1 ORDER BY id DESC";
$ads = $db->select($sql);

// تنسيق البيانات
foreach ($ads as &$ad) {
    // إضافة رابط الصورة/الفيديو
    if ($ad['media']) {
        $ad['media_url'] = APP_URL . '/uploads/ads/' . $ad['media'];
        $ad['media_type'] = getFileType($ad['media']);
    }
    
    // تنسيق التاريخ
    if ($ad['date']) {
        $ad['formatted_date'] = formatArabicDate($ad['date']);
    }
    
    // إضافة إحصائيات
    $ad['views'] = (int)$ad['views'];
    $ad['clicks'] = (int)$ad['clicks'];
    
    // حساب معدل النقر (CTR)
    if ($ad['views'] > 0) {
        $ad['ctr'] = round(($ad['clicks'] / $ad['views']) * 100, 2);
    } else {
        $ad['ctr'] = 0;
    }
}

successResponse($ads);
