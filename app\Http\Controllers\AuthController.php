<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Comments;
use App\Models\Users;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        $checkUser = Users::where('email', $request->email)->with('subscription')->first();
        if ($checkUser !== null) {
            if (Hash::check($request->password, $checkUser->password)) {
                return $checkUser;
            } else {
                return response()->json(['alert' => 'كلمة المرور غير صحيحة'], 404);
            }
        } else {
            return response()->json(['alert' => 'الحساب غير موجود'], 404);
        }
    }
    public function register(Request $request)
    {
        $checkUser = Users::where('email', $request->email)->first();
        if ($checkUser == null) {
            Users::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'country' => $request->country,
                'token' => md5(time()),
                'role' => 'user',
                'date' => date('Y-m-d'),
                'ban' => 0
            ]);
        } else {
            return response()->json(['alert' => 'البريد الالكتروني مسجل من قبل'], 404);
        }
    }
    public function social(Request $request, $service)
    {
        if ($service == 'google') {
            $checkUser = Users::where('email', $request->email)->first();
            if ($checkUser !== null) {
                if ($checkUser->google_token == null) {
                    $updateService = Users::where('email', $request->email)->update([
                        'google_token' => $request->google_token
                    ]);
                    return Users::where('google_token', $request->google_token)->first();
                } else {
                    return $checkUser;
                }
            } else {
                Users::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'google_token' => $request->google_token,
                    'password' => Hash::make($request->google_token),
                    'token' => md5(time()),
                    'date' => date('Y-m-d'),
                    'ban' => 0
                ]);
                return Users::where('google_token', $request->google_token)->first();
            }
        } elseif ($service == 'facebook') {
            $checkUser = Users::where('email', $request->email)->first();
            if ($checkUser !== null) {
                if ($checkUser->facebook_token == null) {
                    $updateService = Users::where('email', $request->email)->update([
                        'facebook_token' => $request->facebook_token
                    ]);
                    return Users::where('facebook_token', $request->facebook_token)->first();
                } else {
                    return $checkUser;
                }
            } else {
                Users::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'facebook_token' => $request->facebook_token,
                    'password' => Hash::make($request->facebook_token),
                    'token' => md5(time()),
                    'date' => date('Y-m-d'),
                    'ban' => 0
                ]);
                return Users::where('facebook_token', $request->facebook_token)->first();
            }
        }
    }
    public function getAll(Request $request)
    {
        return Users::orderBy('id', 'DESC')->get();
    }
    public function deleteSelf($request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();
        if ($user !== null) {
            // delete user comments
            $comments = Comments::where('user_id', $request->id)->get();
            foreach ($comments as $comment) {$comment->delete();}

            $user->delete();

            return response()->json(['alert' => 'تم حذف الحساب بنجاح', 'id' =>'account_deleted', ' type' => 'success']);
        } else {
            return response()->json(['alert' => 'لا يمكن ايجاد حساب المستخدم'], 404);
        }
    }
    public function deleteUser(Request $request)
    {
        $user = Users::where('id', $request->id)->first();
        if ($user !== null) {
            $comments = Comments::where('user_id', $request->id)->get();
            foreach ($comments as $comment) {
                $comment->delete();
            }
            $user->delete();
        } else {
            return response()->json(['alert' => 'المستخدم غير موجود'], 404);
        }
    }
    public function updateFirebaseToken(Request $request)
    {
        $user = Users::where('token', $request->header('Authorization'))->first();

        try{
            $user->update(['fcm_token'=>$request->token]);
            return response()->json([
                'success'=>true
            ]);
        }catch(\Exception $e){
            report($e);
            return response()->json([
                'success'=>false
            ],500);
        }
    }

    public function deleteUsers(Request $request)
    {
        $data = $request->validate([
            'ids' => ['required', 'array']
        ]);

        foreach($data['ids'] as $id)
        {
            $user = Users::where('id', $id)->first();
            if ($user !== null) {
                $comments = Comments::where('user_id', $id)->get();
                foreach ($comments as $comment) {
                    $comment->delete();
                }
                $user->delete();
            }
        }

        return true;
    }
    public function banUser(Request $request)
    {
        return Users::where('id', $request->id)->update([
            'ban' => 1
        ]);
    }
    public function unBanUser(Request $request)
    {
        return Users::where('id', $request->id)->update([
            'ban' => 0
        ]);
    }
}
