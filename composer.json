{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3||^8.1", "guzzlehttp/guzzle": "^7.0.1", "kreait/laravel-firebase": "^5.2", "laravel/framework": "^10.0", "laravel/tinker": "^2.7", "nesbot/carbon": "^2.58", "spatie/laravel-analytics": "^5.0"}, "require-dev": {"spatie/laravel-ignition": "^2.0", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}