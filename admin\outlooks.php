<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التوقعات - لوحة تحكم مطر</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 5px 0;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        
        .outlook-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .outlook-content {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <?php
    require_once '../includes/init.php';
    
    // التحقق من تسجيل الدخول
    if (!isAdmin()) {
        header('Location: login.php');
        exit;
    }
    
    // الحصول على التوقعات
    $outlooksSql = "SELECT o.*, 
                           GROUP_CONCAT(of.file) as files,
                           COUNT(DISTINCT oc.id) as comments_count,
                           COUNT(DISTINCT ol.id) as likes_count
                    FROM outlooks o
                    LEFT JOIN outlooks_files of ON o.id = of.outlook_id
                    LEFT JOIN outlooks_comments oc ON o.id = oc.outlook_id
                    LEFT JOIN outlooks_likes ol ON o.id = ol.outlook_id
                    GROUP BY o.id
                    ORDER BY o.id DESC
                    LIMIT 50";
    
    $outlooks = $db->select($outlooksSql);
    
    // الحصول على البلدان
    $countries = $db->select("SELECT * FROM countries ORDER BY country");
    ?>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="p-3">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-cloud-rain me-2"></i>
                        مطر
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home me-2"></i>
                            الرئيسية
                        </a>
                        <a class="nav-link active" href="outlooks.php">
                            <i class="fas fa-cloud-sun me-2"></i>
                            التوقعات الجوية
                        </a>
                        <a class="nav-link" href="weather-shots.php">
                            <i class="fas fa-camera me-2"></i>
                            صور الطقس
                        </a>
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i>
                            المستخدمين
                        </a>
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                        </a>
                        <a class="nav-link" href="ads.php">
                            <i class="fas fa-ad me-2"></i>
                            الإعلانات
                        </a>
                        <a class="nav-link" href="coupons.php">
                            <i class="fas fa-ticket-alt me-2"></i>
                            الكوبونات
                        </a>
                        <a class="nav-link" href="marketers.php">
                            <i class="fas fa-user-tie me-2"></i>
                            المسوقين
                        </a>
                        <a class="nav-link" href="subscriptions.php">
                            <i class="fas fa-credit-card me-2"></i>
                            الاشتراكات
                        </a>
                        <a class="nav-link" href="support.php">
                            <i class="fas fa-headset me-2"></i>
                            الدعم الفني
                        </a>
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                        <hr class="my-3">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            تسجيل الخروج
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>إدارة التوقعات الجوية</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addOutlookModal">
                        <i class="fas fa-plus me-2"></i>
                        إضافة توقع جديد
                    </button>
                </div>

                <!-- Outlooks Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المعرف</th>
                                        <th>الصورة</th>
                                        <th>العنوان/المحتوى</th>
                                        <th>البلد</th>
                                        <th>التاريخ</th>
                                        <th>الإعجابات</th>
                                        <th>التعليقات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($outlooks as $outlook): ?>
                                    <tr>
                                        <td><?php echo $outlook['id']; ?></td>
                                        <td>
                                            <?php if ($outlook['files']): ?>
                                                <?php $firstFile = explode(',', $outlook['files'])[0]; ?>
                                                <img src="../uploads/outlooks/<?php echo $firstFile; ?>" 
                                                     class="outlook-image" alt="صورة التوقع">
                                            <?php else: ?>
                                                <div class="outlook-image bg-light d-flex align-items-center justify-content-center">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="outlook-content">
                                                <strong><?php echo htmlspecialchars($outlook['title'] ?: 'بدون عنوان'); ?></strong><br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars(substr($outlook['details'], 0, 50)) . '...'; ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($outlook['country']); ?></td>
                                        <td><?php echo formatArabicDate($outlook['date']); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $outlook['likes']; ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $outlook['comments_count']; ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewOutlook(<?php echo $outlook['id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="editOutlook(<?php echo $outlook['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteOutlook(<?php echo $outlook['id']; ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Outlook Modal -->
    <div class="modal fade" id="addOutlookModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة توقع جوي جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addOutlookForm" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان (اختياري)</label>
                            <input type="text" class="form-control" id="title" name="title">
                        </div>
                        
                        <div class="mb-3">
                            <label for="details" class="form-label">تفاصيل التوقع *</label>
                            <textarea class="form-control" id="details" name="details" rows="4" required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="countries" class="form-label">البلدان</label>
                            <select class="form-select" id="countries" name="countries[]" multiple>
                                <?php foreach ($countries as $country): ?>
                                <option value="<?php echo htmlspecialchars($country['country']); ?>">
                                    <?php echo htmlspecialchars($country['country']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <small class="text-muted">اتركه فارغاً لإضافة التوقع لجميع البلدان</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="media" class="form-label">الملفات (صور/فيديو)</label>
                            <input type="file" class="form-control" id="media" name="media[]" multiple 
                                   accept="image/*,video/*">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <label for="schedule" class="form-label">تاريخ النشر</label>
                                <input type="datetime-local" class="form-control" id="schedule" name="schedule">
                            </div>
                            <div class="col-md-6">
                                <label for="hide" class="form-label">تاريخ الإخفاء</label>
                                <input type="datetime-local" class="form-control" id="hide" name="hide">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة التوقع</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إضافة توقع جديد
        document.getElementById('addOutlookForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                title: formData.get('title'),
                details: formData.get('details'),
                countries: formData.getAll('countries'),
                schedule: formData.get('schedule'),
                hide: formData.get('hide')
            };
            
            formData.append('data', JSON.stringify(data));
            
            fetch('../api/admin/outlooks/add.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة التوقع بنجاح');
                    location.reload();
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة التوقع');
            });
        });
        
        function viewOutlook(id) {
            // فتح صفحة عرض التوقع
            window.open(`view-outlook.php?id=${id}`, '_blank');
        }
        
        function editOutlook(id) {
            // فتح نافذة تعديل التوقع
            alert('سيتم إضافة وظيفة التعديل قريباً');
        }
        
        function deleteOutlook(id) {
            if (confirm('هل أنت متأكد من حذف هذا التوقع؟')) {
                fetch('../api/admin/outlooks/delete.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({id: id})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم حذف التوقع بنجاح');
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء حذف التوقع');
                });
            }
        }
    </script>
</body>
</html>
