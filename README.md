# تطبيق مطر - نسخة PHP العادي

هذا هو تطبيق مطر لإدارة التوقعات الجوية وصور الطقس، تم تحويله من Laravel إلى PHP العادي مع الحفاظ على جميع الوظائف والـ API endpoints.

## المتطلبات

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache مع mod_rewrite
- مساحة تخزين للملفات المرفوعة

## التثبيت

### 1. إعداد قاعدة البيانات

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE rainapp4_matar CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد البيانات من ملف SQL الموجود
mysql -u username -p rainapp4_matar < rainapp4_matar.sql
```

### 2. <PERSON><PERSON><PERSON><PERSON> الملفات

1. انسخ جميع الملفات إلى مجلد الويب
2. تأكد من صلاحيات الكتابة للمجلدات التالية:
   - `uploads/`
   - `logs/`

```bash
chmod 755 uploads/
chmod 755 logs/
```

### 3. إعداد التكوين

عدّل ملف `config/app_config.php` وحدث إعدادات قاعدة البيانات:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'rainapp4_matar');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

## الوظائف الرئيسية

### 1. إدارة المستخدمين
- تسجيل دخول وتسجيل جديد
- إدارة الحسابات والصلاحيات
- نظام المصادقة بالتوكن

### 2. إدارة التوقعات الجوية
- إضافة وتعديل التوقعات
- رفع الملفات (صور/فيديو)
- جدولة النشر
- تصنيف حسب البلدان

### 3. إدارة صور الطقس
- رفع صور ومقاطع فيديو
- إدارة المحتوى
- نظام المشاركة

### 4. نظام الإشعارات
- إرسال إشعارات للمستخدمين
- دعم Firebase Cloud Messaging
- جدولة الإشعارات

### 5. نظام الكوبونات والمسوقين
- إدارة كوبونات الخصم
- نظام العمولات للمسوقين
- تتبع الاستخدام

## API Endpoints

### ✅ المصادقة (Authentication)
```
POST /api/auth/login                    ✅ متوفر
POST /api/auth/signup                   ✅ متوفر
POST /api/auth/social/google            ✅ متوفر
POST /api/auth/social/facebook          ✅ متوفر
POST /api/admin/login                   ✅ متوفر
```

### ✅ التوقعات الجوية (Outlooks)
```
GET /api/outlooks                       ✅ متوفر
GET /api/outlooks/{country_id}          ✅ متوفر
GET /api/v2/outlooks                    ✅ متوفر (نسخة محدثة)
GET /api/v2/outlooks/{country_id}       ✅ متوفر (نسخة محدثة)
GET /api/v2/outlooks/public             ✅ متوفر
POST /api/send-comment                  ✅ متوفر (التعليقات)
POST /api/submit-like                   ✅ متوفر (الإعجابات)
POST /api/send-reply                    ✅ متوفر (الردود)
POST /api/outlook/share                 ✅ متوفر (المشاركة)
POST /api/admin/outlooks/add            ✅ متوفر
```

### ✅ صور الطقس (Weather Shots)
```
GET /api/weatherShots                   ✅ متوفر
POST /api/send-pending-shot             ✅ متوفر (رفع صور)
POST /api/weather-shot/share            ✅ متوفر (مشاركة)
```

### ✅ البلدان (Countries)
```
GET /api/countries                      ✅ متوفر
```

### ✅ الإشعارات (Notifications)
```
GET /api/all-notifications             ✅ متوفر
```

### ✅ الإعلانات (Ads)
```
GET /api/ads                           ✅ متوفر
POST /api/increase-views               ✅ متوفر
POST /api/increase-clicks              ✅ متوفر
```

### ✅ المستخدمين (Users)
```
POST /api/update-profile               ✅ متوفر
GET /api/profile                       ✅ متوفر
POST /api/shared-posts                 ✅ متوفر
POST /api/record-subscribe             ✅ متوفر (الاشتراكات)
POST /api/request-delete-account       ✅ متوفر
```

### ✅ الكوبونات (Coupons)
```
POST /api/apply-coupon                 ✅ متوفر
```

### ✅ الدعم الفني (Support)
```
POST /api/send-ticket                  ✅ متوفر
```

### ✅ إعادة تعيين كلمة المرور
```
POST /api/send-reset-password          ✅ متوفر
POST /api/check-reset-code             ✅ متوفر
POST /api/reset-password               ✅ متوفر
```

### ✅ الاشتراكات المتقدمة
```
POST /api/subscribe-via-ad             ✅ متوفر
```

### ✅ الأقمار الصناعية
```
GET /api/sattelite-link                ✅ متوفر
```

## لوحة التحكم

يمكن الوصول إلى لوحة التحكم عبر:
- الرابط: `/admin/`
- تسجيل الدخول: `/admin/login.php`

### بيانات تسجيل الدخول الافتراضية
```
البريد الإلكتروني: <EMAIL>
كلمة المرور: (موجودة في قاعدة البيانات مشفرة)
```

## هيكل المشروع

```
/
├── admin/                 # لوحة تحكم المشرفين
├── api/                   # API endpoints
│   ├── auth/             # المصادقة
│   ├── admin/            # وظائف المشرفين
│   ├── outlooks/         # التوقعات الجوية
│   ├── weather-shots/    # صور الطقس
│   ├── countries/        # البلدان
│   ├── notifications/    # الإشعارات
│   ├── coupons/          # الكوبونات
│   └── stats/            # الإحصائيات
├── config/               # ملفات الإعدادات
├── includes/             # الملفات المشتركة
│   ├── Database.php      # كلاس قاعدة البيانات
│   ├── Auth.php          # نظام المصادقة
│   ├── functions.php     # الوظائف المساعدة
│   └── init.php          # ملف التهيئة
├── uploads/              # الملفات المرفوعة
├── logs/                 # ملفات السجلات
└── index.php             # الصفحة الرئيسية
```

## الأمان

- تشفير كلمات المرور باستخدام password_hash()
- حماية من SQL Injection باستخدام Prepared Statements
- التحقق من صحة البيانات المدخلة
- نظام التوكن للمصادقة
- حماية الملفات الحساسة عبر .htaccess

## التطوير

### إضافة API endpoint جديد

1. أنشئ ملف PHP في المجلد المناسب تحت `/api/`
2. استخدم `require_once '../../includes/init.php'` في بداية الملف
3. استخدم الوظائف المساعدة مثل `jsonResponse()` و `errorResponse()`
4. أضف القاعدة المناسبة في `.htaccess` إذا لزم الأمر

### إضافة صفحة إدارة جديدة

1. أنشئ ملف PHP في مجلد `/admin/`
2. استخدم `require_once '../includes/init.php'`
3. تحقق من صلاحيات المشرف باستخدام `requireAdmin()`

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مخصص لتطبيق مطر ومحمي بحقوق الطبع والنشر.
