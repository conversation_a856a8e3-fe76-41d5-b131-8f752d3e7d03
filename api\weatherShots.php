<?php
/**
 * الحصول على صور الطقس
 * Get Weather Shots API
 */

require_once '../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

// التحقق من المعاملات
$onlyFutureScheduled = isset($_GET['onlyFutureScheduled']) && $_GET['onlyFutureScheduled'] === 'true';
$ignoreSchedule = isset($_GET['ignoreSchedule']) && $_GET['ignoreSchedule'] === 'true';

$now = date('Y-m-d H:i:s');

if ($onlyFutureScheduled) {
    // الحصول على الصور المجدولة في المستقبل فقط
    $sql = "SELECT * FROM weather_shots 
            WHERE schedule > ? 
            ORDER BY id DESC";
    
    $weatherShots = $db->select($sql, [$now]);
} else {
    // الحصول على الصور العادية
    $scheduleCondition = $ignoreSchedule ? 
        "1=1" : 
        "(schedule <= ? OR schedule IS NULL OR schedule = '')";
    
    $sql = "SELECT * FROM weather_shots 
            WHERE {$scheduleCondition}
            ORDER BY id DESC";
    
    $params = $ignoreSchedule ? [] : [$now];
    $weatherShots = $db->select($sql, $params);
}

// تنسيق البيانات
foreach ($weatherShots as &$shot) {
    // إضافة رابط الملف
    $shot['media_url'] = APP_URL . '/uploads/weather-shots/' . $shot['media'];
    $shot['media_type'] = getFileType($shot['media']);
    
    // تنسيق التاريخ
    $shot['formatted_date'] = formatArabicDate($shot['date']);
    
    // تنسيق تاريخ الجدولة إذا كان موجود
    if ($shot['schedule']) {
        $shot['formatted_schedule'] = formatArabicDate($shot['schedule']);
    }
    
    // تنسيق تاريخ الإخفاء إذا كان موجود
    if ($shot['hide']) {
        $shot['formatted_hide'] = formatArabicDate($shot['hide']);
    }
}

successResponse($weatherShots);
